<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/rounded_background"
    android:padding="8dp"
    android:layout_marginBottom="4dp">

    <!-- 设备信息行 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="4dp">

        <!-- 设备图标 -->
        <ImageView
            android:id="@+id/device_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_cast"
            android:layout_marginEnd="8dp"
            android:alpha="0.7" />

        <!-- 设备信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/device_name_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设备名称"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#333333" />

            <TextView
                android:id="@+id/device_address_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="*************:7777"
                android:textSize="12sp"
                android:textColor="#666666" />

        </LinearLayout>

        <!-- 连接状态 -->
        <TextView
            android:id="@+id/connection_status_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="未连接"
            android:textSize="11sp"
            android:textColor="#666666"
            android:background="@drawable/rounded_background"
            android:paddingStart="6dp"
            android:paddingEnd="6dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp" />

    </LinearLayout>

    <!-- 操作按钮行 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/connect_button"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:text="连接"
            android:textSize="12sp"
            android:padding="0dp"
            android:backgroundTint="@android:color/holo_green_light"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp"
            android:minWidth="60dp" />

        <Button
            android:id="@+id/control_button"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:text="控制"
            android:padding="0dp"
            android:textSize="12sp"
            android:backgroundTint="@android:color/holo_orange_light"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp"
            android:minWidth="60dp" />

        <Button
            android:id="@+id/edit_button"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:text="编辑"
            android:textSize="12sp"
            android:padding="0dp"
            android:backgroundTint="@android:color/holo_blue_light"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp"
            android:minWidth="50dp" />

        <Button
            android:id="@+id/delete_button"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:text="删除"
            android:textSize="12sp"
            android:padding="0dp"
            android:backgroundTint="@android:color/holo_red_light"
            android:textColor="@android:color/white"
            android:minWidth="50dp" />

    </LinearLayout>

</LinearLayout>
