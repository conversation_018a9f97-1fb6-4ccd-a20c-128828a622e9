package com.example.castapp.ui.windowsettings.interfaces

import android.view.Surface

/**
 * 变换状态监听接口
 * 用于监听投屏窗口的变换状态变化
 */
interface TransformStateListener {
    /**
     * 变换状态发生变化时的回调
     * @param connectionId 连接ID
     * @param x 实际显示的X坐标（左上角）
     * @param y 实际显示的Y坐标（左上角）
     * @param scale 缩放倍数
     * @param rotation 旋转角度
     */
    fun onTransformChanged(connectionId: String, x: Float, y: Float, scale: Float, rotation: Float)
}

/**
 * Surface状态监听接口
 * 用于监听TextureView Surface的生命周期
 */
interface SurfaceStateListener {
    /**
     * Surface可用时的回调
     * @param surface Surface对象，null表示Surface不可用
     * @param connectionId 连接ID
     */
    fun onSurfaceAvailable(surface: Surface?, connectionId: String)
    
    /**
     * Surface销毁时的回调
     * @param connectionId 连接ID
     */
    fun onSurfaceDestroyed(connectionId: String)
}
