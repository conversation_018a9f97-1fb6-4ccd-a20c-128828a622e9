package com.example.castapp.manager;

/**
 * 窗口管理器（重构版）
 * 采用模块化设计，将功能委托给各个专门的模块处理
 * 保持原有的公共API不变，确保向后兼容
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00ce\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010#\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010%\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b \u0018\u0000 \u0096\u00012\u00020\u0001:\u0002\u0096\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J8\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020 \u0012\u0004\u0012\u00020 0\u001f2\u0006\u0010!\u001a\u00020 2\u0006\u0010\"\u001a\u00020 2\u0012\u0010#\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010$H\u0002J\u0014\u0010%\u001a\u00020\u001a2\f\u0010&\u001a\b\u0012\u0004\u0012\u00020(0\'J,\u0010)\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u001d0\u001f2\u0006\u0010*\u001a\u00020\u001d2\u0006\u0010+\u001a\u00020\u001d2\u0006\u0010,\u001a\u00020-H\u0002J.\u0010.\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u0010*\u001a\u00020\u001d2\u0006\u0010+\u001a\u00020\u001d2\u0006\u00100\u001a\u00020\u001d2\u0006\u00101\u001a\u00020\u001dJ\u0016\u00102\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u00103\u001a\u000204J\u0016\u00105\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u00106\u001a\u00020\u000fJ\u001e\u00107\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u0010*\u001a\u00020\u001d2\u0006\u0010+\u001a\u00020\u001dJ\u0016\u00108\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u00109\u001a\u00020\u001dJ&\u0010:\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u00109\u001a\u00020\u001d2\u0006\u0010*\u001a\u00020\u001d2\u0006\u0010+\u001a\u00020\u001dJ\u0016\u0010;\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u0010<\u001a\u00020\u001dJ&\u0010=\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u0010<\u001a\u00020\u001d2\u0006\u0010*\u001a\u00020\u001d2\u0006\u0010+\u001a\u00020\u001dJ*\u0010>\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u0010?\u001a\u00020\t2\u0012\u0010#\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010$J4\u0010@\u001a\u00020\u001a2\u0006\u0010A\u001a\u00020B2\u0006\u0010?\u001a\u00020\t2\u0006\u0010C\u001a\u00020\t2\u0012\u0010#\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010$H\u0002J\u0012\u0010D\u001a\u0004\u0018\u00010\t2\u0006\u0010E\u001a\u00020FH\u0002J6\u0010G\u001a\u00020\u001a2$\u0010H\u001a \u0012\u0016\u0012\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010$0\'\u0012\u0004\u0012\u00020\u001a0I2\b\b\u0002\u0010J\u001a\u00020\u000fJ\u0006\u0010K\u001a\u00020\u001aJ\u000e\u0010L\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\tJ\u0018\u0010M\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u0010N\u001a\u00020OH\u0002J\u0016\u0010P\u001a\u00020\u001a2\u0006\u0010Q\u001a\u00020\t2\u0006\u0010R\u001a\u00020\tJ.\u0010S\u001a\u00020\u001a2\u0006\u0010T\u001a\u00020\t2\u0006\u0010U\u001a\u00020\t2\u0006\u0010V\u001a\u00020\t2\u0006\u0010W\u001a\u00020X2\u0006\u0010Y\u001a\u00020\tJ\u0016\u0010Z\u001a\u00020\u001a2\u0006\u0010[\u001a\u00020\t2\u0006\u0010?\u001a\u00020\tJ\f\u0010\\\u001a\b\u0012\u0004\u0012\u00020]0\'J\u000e\u0010^\u001a\u00020\u000f2\u0006\u0010/\u001a\u00020\tJ\u000e\u0010_\u001a\u00020\u000f2\u0006\u0010/\u001a\u00020\tJ\u0010\u0010`\u001a\u0004\u0018\u00010-2\u0006\u0010/\u001a\u00020\tJ\u0010\u0010a\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\tH\u0002J\u0010\u0010b\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\tH\u0002J\u0010\u0010c\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\tH\u0002J\u0016\u0010d\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u00106\u001a\u00020\u000fJ\u000e\u0010e\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\tJ\u001e\u0010f\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u0010g\u001a\u00020 2\u0006\u0010h\u001a\u00020 J\u0018\u0010i\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u0010j\u001a\u00020 H\u0002J*\u0010k\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u0010l\u001a\u00020 2\b\b\u0002\u0010m\u001a\u00020 2\b\b\u0002\u0010n\u001a\u00020 J\u0018\u0010o\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u00106\u001a\u00020\u000fH\u0002J\u0018\u0010p\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u0010q\u001a\u00020 H\u0002J\u0010\u0010r\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\tH\u0002J\u0016\u0010s\u001a\u00020\u001a2\u0006\u0010t\u001a\u00020u2\u0006\u0010v\u001a\u00020wJ\u0006\u0010x\u001a\u00020\u001aJ\u0006\u0010y\u001a\u00020\u001aJ\u0006\u0010z\u001a\u00020\u001aJ\u000e\u0010{\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\tJ\u000e\u0010|\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\tJ\u0018\u0010}\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u00106\u001a\u00020\u000fH\u0002J\u0016\u0010~\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u00106\u001a\u00020\u000fJ\u0017\u0010\u007f\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0007\u0010\u0080\u0001\u001a\u00020 J\u0018\u0010\u0081\u0001\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0007\u0010\u0082\u0001\u001a\u00020\u000fJ\u0017\u0010\u0083\u0001\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u0010g\u001a\u00020\u001dJ\u0018\u0010\u0084\u0001\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0007\u0010\u0085\u0001\u001a\u00020\u001dJ\u0018\u0010\u0086\u0001\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0007\u0010\u0082\u0001\u001a\u00020\u000fJ\u0018\u0010\u0087\u0001\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0007\u0010\u0082\u0001\u001a\u00020\u000fJ\u0017\u0010\u0088\u0001\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u00106\u001a\u00020\u000fJ\u0018\u0010\u0089\u0001\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0007\u0010\u0082\u0001\u001a\u00020\u000fJ\u0015\u0010\u008a\u0001\u001a\u00020\u001a2\f\u0010H\u001a\b\u0012\u0004\u0012\u00020\u001a0\u0019J\u0018\u0010\u008b\u0001\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0007\u0010\u0082\u0001\u001a\u00020\u000fJ\u0018\u0010\u008c\u0001\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0007\u0010\u0082\u0001\u001a\u00020\u000fJ3\u0010\u008d\u0001\u001a\u00020\u001a2*\u0010H\u001a&\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u001a0\u001cJ\u0018\u0010\u008e\u0001\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0007\u0010\u008f\u0001\u001a\u00020\u001dJ\u0018\u0010\u0090\u0001\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0007\u0010\u0091\u0001\u001a\u00020\u000fJ\t\u0010\u0092\u0001\u001a\u00020\u001aH\u0002J\u0007\u0010\u0093\u0001\u001a\u00020\u001aJ\u0007\u0010\u0094\u0001\u001a\u00020\u001aJ\u0017\u0010\u0095\u0001\u001a\u00020\u001a2\u0006\u0010/\u001a\u00020\t2\u0006\u00106\u001a\u00020\u000fR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u000f0\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0018\u001a\n\u0012\u0004\u0012\u00020\u001a\u0018\u00010\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R4\u0010\u001b\u001a(\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u001a\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0097\u0001"}, d2 = {"Lcom/example/castapp/manager/WindowSettingsManager;", "", "()V", "creationModule", "Lcom/example/castapp/manager/windowsettings/WindowCreationModule;", "dataModule", "Lcom/example/castapp/manager/windowsettings/WindowDataModule;", "deletingConnections", "", "", "dialogModule", "Lcom/example/castapp/manager/windowsettings/WindowDialogModule;", "infoModule", "Lcom/example/castapp/manager/windowsettings/WindowInfoModule;", "isInitialized", "", "landscapeModeStates", "", "layoutModule", "Lcom/example/castapp/manager/windowsettings/WindowLayoutModule;", "lifecycleModule", "Lcom/example/castapp/manager/windowsettings/WindowLifecycleModule;", "operationModule", "Lcom/example/castapp/manager/windowsettings/WindowOperationModule;", "precisionControlUpdateCallback", "Lkotlin/Function0;", "", "transformValueChangeCallback", "Lkotlin/Function5;", "", "adjustWindowSizeForReceiver", "Lkotlin/Pair;", "", "remoteWidth", "remoteHeight", "formatData", "", "applyLayoutItemsToExistingWindows", "layoutItems", "", "Lcom/example/castapp/database/entity/WindowLayoutItemEntity;", "applyPreciseBoundaryAlignment", "x", "y", "transformHandler", "Lcom/example/castapp/ui/windowsettings/TransformHandler;", "applyPrecisionTransform", "connectionId", "scale", "rotation", "applyRemoteCrop", "cropRatio", "Landroid/graphics/RectF;", "applyRemoteEditMode", "isEnabled", "applyRemotePosition", "applyRemoteRotation", "rotationAngle", "applyRemoteRotationAndPosition", "applyRemoteScale", "scaleFactor", "applyRemoteScaleAndPosition", "applyRemoteTextFormat", "textContent", "applyRichTextFormat", "textWindowManager", "Lcom/example/castapp/ui/windowsettings/TextWindowManager;", "richTextData", "bitmapToBase64", "bitmap", "Landroid/graphics/Bitmap;", "captureAllWindowScreenshots", "callback", "Lkotlin/Function1;", "forRemoteControl", "cleanup", "clearLandscapeModeState", "coordinatedWebSocketDisconnect", "webSocketManager", "Lcom/example/castapp/manager/WebSocketManager;", "createCameraWindow", "cameraId", "cameraName", "createMediaWindow", "mediaId", "mediaType", "fileName", "uri", "Landroid/net/Uri;", "contentType", "createTextWindow", "textId", "getCurrentWindowInfoList", "Lcom/example/castapp/model/CastWindowInfo;", "getEditState", "getLandscapeModeEnabled", "getWindowMapping", "gracefulStopMediaCodec", "handleCameraWindowDelete", "handleCastWindowDelete", "handleLandscapeModeSwitch", "handleNewConnection", "handleScreenResolution", "width", "height", "handleVideoLoopCount", "loopCount", "handleVideoOrientationChanged", "orientation", "videoWidth", "videoHeight", "handleVideoPlaySwitch", "handleVideoVolume", "volume", "handleWindowDelete", "initialize", "activity", "Landroid/app/Activity;", "container", "Landroid/widget/FrameLayout;", "refreshLayerManagerDialog", "refreshWindowManagerDialog", "removeAllTextureViews", "removeTextureViewForConnection", "resetWindowTransform", "sendLandscapeModeControlMessage", "sendLandscapeModeControlMessagePublic", "setBorderColor", "color", "setBorderEnabled", "enabled", "setBorderWidth", "setCornerRadius", "radius", "setCropEnabled", "setDragEnabled", "setLandscapeModeStateOnly", "setMirrorEnabled", "setPrecisionControlUpdateCallback", "setRotationEnabled", "setScaleEnabled", "setTransformValueChangeCallback", "setWindowAlpha", "alpha", "setWindowVisibility", "visible", "setupModuleCallbacks", "showLayerManagerDialog", "showWindowManagerDialog", "toggleControlMode", "Companion", "app_debug"})
public final class WindowSettingsManager {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.WindowSettingsManager INSTANCE;
    private com.example.castapp.manager.windowsettings.WindowDataModule dataModule;
    private com.example.castapp.manager.windowsettings.WindowCreationModule creationModule;
    private com.example.castapp.manager.windowsettings.WindowLifecycleModule lifecycleModule;
    private com.example.castapp.manager.windowsettings.WindowOperationModule operationModule;
    private com.example.castapp.manager.windowsettings.WindowLayoutModule layoutModule;
    private com.example.castapp.manager.windowsettings.WindowInfoModule infoModule;
    private com.example.castapp.manager.windowsettings.WindowDialogModule dialogModule;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> precisionControlUpdateCallback;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function5<? super java.lang.String, ? super java.lang.Float, ? super java.lang.Float, ? super java.lang.Float, ? super java.lang.Float, kotlin.Unit> transformValueChangeCallback;
    private boolean isInitialized = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<java.lang.String> deletingConnections = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.Boolean> landscapeModeStates = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.WindowSettingsManager.Companion Companion = null;
    
    private WindowSettingsManager() {
        super();
    }
    
    /**
     * 初始化管理器
     */
    public final void initialize(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    android.widget.FrameLayout container) {
    }
    
    /**
     * 设置模块间的回调关系
     */
    private final void setupModuleCallbacks() {
    }
    
    /**
     * 设置精准控制面板更新回调
     */
    public final void setPrecisionControlUpdateCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 设置变换值变化回�?
     */
    public final void setTransformValueChangeCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function5<? super java.lang.String, ? super java.lang.Float, ? super java.lang.Float, ? super java.lang.Float, ? super java.lang.Float, kotlin.Unit> callback) {
    }
    
    /**
     * 处理新连接事�?
     */
    public final void handleNewConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 处理屏幕分辨率事件
     */
    public final void handleScreenResolution(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, int width, int height) {
    }
    
    /**
     * 🎯 横竖屏适配：处理视频方向变化
     */
    public final void handleVideoOrientationChanged(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, int orientation, int videoWidth, int videoHeight) {
    }
    
    /**
     * 移除指定连接的投屏窗口
     */
    public final void removeTextureViewForConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 移除所有投屏窗口
     */
    public final void removeAllTextureViews() {
    }
    
    /**
     * 显示窗口管理BottomSheet
     */
    public final void showWindowManagerDialog() {
    }
    
    /**
     * 显示层级管理BottomSheet
     */
    public final void showLayerManagerDialog() {
    }
    
    /**
     * 刷新窗口管理对话框
     */
    public final void refreshWindowManagerDialog() {
    }
    
    /**
     * 🔄 刷新层级管理对话框
     */
    public final void refreshLayerManagerDialog() {
    }
    
    /**
     * 获取当前活跃的投屏窗口信息列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.model.CastWindowInfo> getCurrentWindowInfoList() {
        return null;
    }
    
    /**
     * 应用布局项到现有窗口（直接使用布局项列表）
     */
    public final void applyLayoutItemsToExistingWindows(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.database.entity.WindowLayoutItemEntity> layoutItems) {
    }
    
    /**
     * 应用精准变换
     */
    public final void applyPrecisionTransform(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float x, float y, float scale, float rotation) {
    }
    
    /**
     * 🎯 应用远程位置控制（边界对齐优化版）
     */
    public final void applyRemotePosition(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float x, float y) {
    }
    
    /**
     * 🎯 应用远程缩放控制
     */
    public final void applyRemoteScale(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float scaleFactor) {
    }
    
    /**
     * 🎯 应用远程旋转控制
     */
    public final void applyRemoteRotation(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float rotationAngle) {
    }
    
    /**
     * 🎯 应用远程裁剪控制
     */
    public final void applyRemoteCrop(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    android.graphics.RectF cropRatio) {
    }
    
    /**
     * 🎯 增强型同步：应用远程缩放和位置组合控制
     */
    public final void applyRemoteScaleAndPosition(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float scaleFactor, float x, float y) {
    }
    
    /**
     * 🎯 增强型同步：应用远程旋转和位置组合控制
     */
    public final void applyRemoteRotationAndPosition(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float rotationAngle, float x, float y) {
    }
    
    /**
     * 重置窗口变换
     */
    public final void resetWindowTransform(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 切换精准调控功能
     */
    public final void toggleControlMode(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 🎯 获取窗口映射（用于外部模块访问TransformHandler）
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.windowsettings.TransformHandler getWindowMapping(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 🔄 设置拖动功能启用状态
     */
    public final void setDragEnabled(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean enabled) {
    }
    
    /**
     * 🔄 设置缩放功能启用状态
     */
    public final void setScaleEnabled(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean enabled) {
    }
    
    /**
     * 📝 获取编辑功能启用状态（仅文本窗口）
     */
    public final boolean getEditState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 🔄 设置旋转功能启用状态
     */
    public final void setRotationEnabled(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean enabled) {
    }
    
    /**
     * 🔄 设置裁剪功能启用状态
     */
    public final void setCropEnabled(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean enabled) {
    }
    
    /**
     * 🔄 设置窗口可见性
     */
    public final void setWindowVisibility(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean visible) {
    }
    
    /**
     * 🔄 设置镜像功能启用状态
     */
    public final void setMirrorEnabled(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean enabled) {
    }
    
    /**
     * 🔄 设置圆角半径
     */
    public final void setCornerRadius(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float radius) {
    }
    
    /**
     * 🔄 设置窗口透明度
     */
    public final void setWindowAlpha(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float alpha) {
    }
    
    /**
     * 🔄 设置边框启用状态
     */
    public final void setBorderEnabled(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean enabled) {
    }
    
    /**
     * 🔄 设置边框颜色
     */
    public final void setBorderColor(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, int color) {
    }
    
    /**
     * 🔄 设置边框宽度
     */
    public final void setBorderWidth(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, float width) {
    }
    
    /**
     * 捕获所有投屏窗口的截图
     * @param callback 截图完成回调，参数为截图数据列表
     * @param forRemoteControl 🎯 是否为遥控端请求，true时返回原始截图，false时返回变换后截图
     */
    public final void captureAllWindowScreenshots(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>>, kotlin.Unit> callback, boolean forRemoteControl) {
    }
    
    /**
     * 将 Bitmap 转换为 Base64 字符串
     */
    private final java.lang.String bitmapToBase64(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 创建摄像头窗口
     */
    public final void createCameraWindow(@org.jetbrains.annotations.NotNull()
    java.lang.String cameraId, @org.jetbrains.annotations.NotNull()
    java.lang.String cameraName) {
    }
    
    /**
     * 创建媒体窗口（视频/图片）
     */
    public final void createMediaWindow(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId, @org.jetbrains.annotations.NotNull()
    java.lang.String mediaType, @org.jetbrains.annotations.NotNull()
    java.lang.String fileName, @org.jetbrains.annotations.NotNull()
    android.net.Uri uri, @org.jetbrains.annotations.NotNull()
    java.lang.String contentType) {
    }
    
    /**
     * 创建文本窗口
     */
    public final void createTextWindow(@org.jetbrains.annotations.NotNull()
    java.lang.String textId, @org.jetbrains.annotations.NotNull()
    java.lang.String textContent) {
    }
    
    /**
     * 🎬 处理视频播放开关
     */
    private final void handleVideoPlaySwitch(java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 🎬 处理视频循环次数设置
     */
    private final void handleVideoLoopCount(java.lang.String connectionId, int loopCount) {
    }
    
    /**
     * 🎬 处理视频音量调整
     */
    private final void handleVideoVolume(java.lang.String connectionId, int volume) {
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
    
    /**
     * 🗑️ 处理窗口删除 - 优化版本
     * 重新设计删除流程，确保时序正确和状态一致性
     */
    private final void handleWindowDelete(java.lang.String connectionId) {
    }
    
    /**
     * 🗑️ 处理摄像头窗口删除
     */
    private final void handleCameraWindowDelete(java.lang.String connectionId) {
    }
    
    /**
     * 🗑️ 处理投屏窗口删除 - 优化时序
     */
    private final void handleCastWindowDelete(java.lang.String connectionId) {
    }
    
    /**
     * 🚀 优雅停止MediaCodec，避免强制终止异常
     */
    private final void gracefulStopMediaCodec(java.lang.String connectionId) {
    }
    
    /**
     * 🚀 协调WebSocket断开，避免双向冲突
     */
    private final void coordinatedWebSocketDisconnect(java.lang.String connectionId, com.example.castapp.manager.WebSocketManager webSocketManager) {
    }
    
    /**
     * 处理横屏模式开关变化
     */
    public final void handleLandscapeModeSwitch(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 🎯 获取横屏模式状态
     */
    public final boolean getLandscapeModeEnabled(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 🎯 仅设置横屏模式状态（不发送控制消息）
     * 用于首次投屏时的状态恢复，避免时序问题
     */
    public final void setLandscapeModeStateOnly(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 🎯 发送横屏模式控制消息（公共方法）
     * 用于延迟发送控制消息
     */
    public final void sendLandscapeModeControlMessagePublic(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 🎯 清除横屏模式状态（连接断开时调用）
     */
    public final void clearLandscapeModeState(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 发送横屏模式控制消息给发送端
     * 🎯 修复：通过接收端WebSocket服务器发送消息给发送端
     */
    private final void sendLandscapeModeControlMessage(java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 🎯 应用远程编辑模式控制
     */
    public final void applyRemoteEditMode(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 🎯 应用远程文字格式同步（支持富文本格式）
     */
    public final void applyRemoteTextFormat(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.lang.String textContent, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
    }
    
    /**
     * 🎯 应用富文本格式到接收端
     */
    private final void applyRichTextFormat(com.example.castapp.ui.windowsettings.TextWindowManager textWindowManager, java.lang.String textContent, java.lang.String richTextData, java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
    }
    
    /**
     * 🎯 新增：为接收端调整窗口尺寸（根据DPI差异）
     * 确保接收端窗口尺寸能够显示与遥控端相同的文字布局
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> adjustWindowSizeForReceiver(int remoteWidth, int remoteHeight, java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
        return null;
    }
    
    /**
     * 🎯 新增：精确边界对齐算法
     * 检测窗口位置是否接近屏幕边界，如果是则进行精确对齐
     */
    private final kotlin.Pair<java.lang.Float, java.lang.Float> applyPreciseBoundaryAlignment(float x, float y, com.example.castapp.ui.windowsettings.TransformHandler transformHandler) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/manager/WindowSettingsManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/manager/WindowSettingsManager;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.WindowSettingsManager getInstance() {
            return null;
        }
    }
}