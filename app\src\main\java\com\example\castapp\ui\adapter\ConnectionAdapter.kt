package com.example.castapp.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.appcompat.widget.SwitchCompat
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.manager.StateManager
import com.example.castapp.model.Connection
import com.example.castapp.utils.AppLog

/**
 * 连接列表适配器
 * 从 StateManager 获取状态信息
 */
class ConnectionAdapter(
    private val connections: MutableList<Connection>,
    private val stateManager: StateManager,
    private val onCastToggle: (Connection, Boolean) -> Unit,
    private val onMediaAudioToggle: (Connection, Boolean) -> Unit,
    private val onMicAudioToggle: (Connection, Boolean) -> Unit,
    private val onEditConnection: (Connection) -> Unit,
    private val onConfirmRemoveConnection: (Connection) -> Unit
) : RecyclerView.Adapter<ConnectionAdapter.ConnectionViewHolder>() {

    class ConnectionViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val connectionText: TextView = itemView.findViewById(R.id.connection_text)
        val connectionIdText: TextView = itemView.findViewById(R.id.connection_id_text)
        val connectionStatusText: TextView = itemView.findViewById(R.id.connection_status_text)
        val castSwitch: SwitchCompat = itemView.findViewById(R.id.cast_switch)
        val mediaAudioSwitch: SwitchCompat = itemView.findViewById(R.id.media_audio_switch)
        val micAudioSwitch: SwitchCompat = itemView.findViewById(R.id.mic_audio_switch)
        val editButton: ImageButton = itemView.findViewById(R.id.edit_button)
        val disconnectButton: ImageButton = itemView.findViewById(R.id.disconnect_button)
        val statusIndicator: View = itemView.findViewById(R.id.connection_status_indicator)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ConnectionViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_connection, parent, false)
        return ConnectionViewHolder(view)
    }

    override fun onBindViewHolder(holder: ConnectionViewHolder, position: Int) {
        val connection = connections[position]

        // 直接从StateManager获取最新的Connection（包含状态）
        val currentConnection = stateManager.getConnection(connection.connectionId) ?: connection

        holder.connectionText.text = connection.getDisplayText()
        holder.connectionIdText.text = holder.itemView.context.getString(
            R.string.connection_id_format,
            connection.connectionId.takeLast(8)
        )

        // 使用Connection的状态摘要
        holder.connectionStatusText.text = currentConnection.getStatusSummary()

        // 设置状态指示器状态，使用XML selector
        holder.statusIndicator.isSelected = currentConnection.isCasting

        // 设置投屏开关
        holder.castSwitch.setOnCheckedChangeListener(null) // 清除之前的监听器
        holder.castSwitch.isChecked = currentConnection.isCasting
        holder.castSwitch.isEnabled = true // 投屏开关始终启用，让用户可以尝试投屏

        // 添加调试日志
        AppLog.d("设置开关: ${connection.getDisplayText()}, " +
                "投屏中: ${currentConnection.isCasting}, " +
                "开关启用: ${holder.castSwitch.isEnabled}")

        holder.castSwitch.setOnCheckedChangeListener { _, isChecked ->
            AppLog.d("投屏开关切换: ${connection.getDisplayText()}, 新状态: $isChecked")
            onCastToggle(connection, isChecked)
        }

        // 设置媒体音频开关
        holder.mediaAudioSwitch.setOnCheckedChangeListener(null) // 清除之前的监听器
        holder.mediaAudioSwitch.isChecked = currentConnection.isMediaAudioEnabled
        holder.mediaAudioSwitch.setOnCheckedChangeListener { _, isChecked ->
            AppLog.d("媒体音频开关切换: ${connection.getDisplayText()}, 新状态: $isChecked")
            onMediaAudioToggle(connection, isChecked)
        }

        // 设置麦克风音频开关
        holder.micAudioSwitch.setOnCheckedChangeListener(null) // 清除之前的监听器
        holder.micAudioSwitch.isChecked = currentConnection.isMicAudioEnabled
        holder.micAudioSwitch.setOnCheckedChangeListener { _, isChecked ->
            AppLog.d("麦克风音频开关切换: ${connection.getDisplayText()}, 新状态: $isChecked")
            onMicAudioToggle(connection, isChecked)
        }

        // 设置编辑和删除按钮的状态
        val isAnyServiceActive = currentConnection.isCasting ||
                currentConnection.isMediaAudioEnabled ||
                currentConnection.isMicAudioEnabled

        // 设置编辑按钮
        holder.editButton.isEnabled = !isAnyServiceActive
        holder.editButton.alpha = if (isAnyServiceActive) 0.3f else 0.7f
        holder.editButton.setOnClickListener {
            if (!isAnyServiceActive) {
                onEditConnection(connection)
            }
        }

        // 设置删除按钮 - 同样在服务活跃时禁用
        holder.disconnectButton.isEnabled = !isAnyServiceActive
        holder.disconnectButton.alpha = if (isAnyServiceActive) 0.3f else 0.7f
        holder.disconnectButton.setOnClickListener {
            if (!isAnyServiceActive) {
                onConfirmRemoveConnection(connection)
            }
        }
    }

    override fun getItemCount(): Int = connections.size

    /**
     * 🚀 新增：精准更新指定连接的状态，避免全量刷新
     */
    fun updateConnectionPrecisely(connectionId: String, updatedConnection: Connection) {
        val position = connections.indexOfFirst { it.connectionId == connectionId }
        if (position != -1) {
            val startTime = System.currentTimeMillis()

            // 更新本地数据
            connections[position] = updatedConnection

            // 只通知这一个item发生了变化，避免全量刷新
            notifyItemChanged(position)

            val endTime = System.currentTimeMillis()
            AppLog.d("⚡ 精准更新连接状态: ${updatedConnection.getDisplayText()}, " +
                    "位置: $position, " +
                    "投屏: ${updatedConnection.isCasting}, " +
                    "媒体音频: ${updatedConnection.isMediaAudioEnabled}, " +
                    "麦克风: ${updatedConnection.isMicAudioEnabled}, " +
                    "耗时: ${endTime - startTime}ms")
        } else {
            AppLog.w("未找到连接进行精准更新: $connectionId")
        }
    }

}