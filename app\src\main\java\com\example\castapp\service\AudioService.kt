package com.example.castapp.service

import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import com.example.castapp.manager.MediaProjectionManager
import com.example.castapp.manager.StateManager
import com.example.castapp.manager.WebSocketManager
import com.example.castapp.model.Connection
import com.example.castapp.audio.AudioCaptureManager
import com.example.castapp.audio.AudioEncoder
import com.example.castapp.audio.AudioRtpSender
import com.example.castapp.websocket.ControlMessage
import com.example.castapp.utils.NotificationManager as AppNotificationManager
import java.util.concurrent.ConcurrentHashMap
import com.example.castapp.utils.AppLog

/**
 * 独立的音频流服务
 * 完全独立于投屏服务，专门处理音频流功能
 */
class AudioService : Service() {
    companion object {
        // 音频相关的Action常量
        const val ACTION_START_MEDIA_AUDIO = "action_start_media_audio"
        const val ACTION_STOP_MEDIA_AUDIO = "action_stop_media_audio"
        const val ACTION_START_MIC_AUDIO = "action_start_mic_audio"
        const val ACTION_STOP_MIC_AUDIO = "action_stop_mic_audio"
        const val ACTION_HANDLE_MEDIA_AUDIO_PERMISSION = "action_handle_media_audio_permission"
        const val ACTION_UPDATE_MEDIA_VOLUME = "action_update_media_volume"
        const val ACTION_UPDATE_MIC_VOLUME = "action_update_mic_volume"

        // Intent参数常量
        const val EXTRA_CONNECTION_ID = "connection_id"
        const val EXTRA_RESULT_CODE = "result_code"
        const val EXTRA_RESULT_DATA = "result_data"
        const val EXTRA_VOLUME = "volume"
    }

    // 独立的MediaProjection管理器实例
    private var mediaProjectionManager: MediaProjectionManager? = null

    // 状态管理器实例
    private lateinit var stateManager: StateManager

    // 统一WebSocket管理器实例
    private val webSocketManager = WebSocketManager.getInstance()

    // 音频捕获管理器
    private var audioCaptureManager: AudioCaptureManager? = null

    // 音频编码器
    private var mediaAudioEncoder: AudioEncoder? = null
    private var micAudioEncoder: AudioEncoder? = null

    // RTP发送器映射
    private val mediaAudioRtpSenders = ConcurrentHashMap<String, AudioRtpSender>()
    private val micAudioRtpSenders = ConcurrentHashMap<String, AudioRtpSender>()



    override fun onCreate() {
        super.onCreate()
        AppLog.audio("音频流服务创建")
        stateManager = StateManager.getInstance(application)
        AppNotificationManager.createNotificationChannel(this, AppNotificationManager.NotificationType.AUDIO_STREAMING)
        AppLog.audio("音频流服务初始化完成")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (intent == null) {
            AppLog.w("收到空Intent")
            return START_NOT_STICKY
        }

        when (intent.action) {
            ACTION_START_MEDIA_AUDIO -> handleAudioAction(intent, ::startMediaAudio, "媒体音频")
            ACTION_STOP_MEDIA_AUDIO -> handleStopAction(intent, ::stopMediaAudio)
            ACTION_START_MIC_AUDIO -> handleAudioAction(intent, ::startMicAudio, "麦克风音频")
            ACTION_STOP_MIC_AUDIO -> handleStopAction(intent, ::stopMicAudio)
            ACTION_UPDATE_MEDIA_VOLUME -> updateAudioVolume(intent, true)
            ACTION_UPDATE_MIC_VOLUME -> updateAudioVolume(intent, false)
            ACTION_HANDLE_MEDIA_AUDIO_PERMISSION -> handlePermissionResult(intent)
        }
        return START_NOT_STICKY
    }

    private fun handleAudioAction(intent: Intent, action: (String, String) -> Unit, audioType: String) {
        val connectionId = intent.getStringExtra(EXTRA_CONNECTION_ID) ?: return
        val connection = stateManager.findConnectionById(connectionId)
        if (connection == null) {
            AppLog.e("未找到连接: $connectionId")
            return
        }
        AppLog.audio("启动$audioType: $connectionId -> ${connection.ipAddress}:${connection.port}")
        action(connectionId, connection.ipAddress)
    }

    private fun handleStopAction(intent: Intent, action: (String) -> Unit) {
        intent.getStringExtra(EXTRA_CONNECTION_ID)?.let(action)
    }

    private fun updateAudioVolume(intent: Intent, isMedia: Boolean) {
        val volume = intent.getIntExtra(EXTRA_VOLUME, 80)
        if (isMedia) updateMediaAudioVolume(volume) else updateMicAudioVolume(volume)
    }

    private fun handlePermissionResult(intent: Intent) {
        val resultCode = intent.getIntExtra(EXTRA_RESULT_CODE, -1)
        val resultData = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra(EXTRA_RESULT_DATA, Intent::class.java)
        } else {
            @Suppress("DEPRECATION")
            intent.getParcelableExtra(EXTRA_RESULT_DATA)
        }
        if (resultCode != -1 && resultData != null) {
            handleMediaAudioPermissionResult(resultCode, resultData)
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun initializeMediaProjectionManager() {
        if (mediaProjectionManager == null) {
            mediaProjectionManager = MediaProjectionManager.getInstance(this)
        }
    }

    private fun initializeAudioCaptureManager() {
        if (audioCaptureManager == null) {
            audioCaptureManager = AudioCaptureManager(this)
        }
    }



    private fun startForegroundService() {
        val notification = AppNotificationManager.createAudioStreamingNotification(this, "音频流传输中")
        startForeground(AppNotificationManager.NotificationType.AUDIO_STREAMING.notificationId, notification)
    }

    private fun startWebSocketFunction(connection: Connection, functionType: String, audioType: String): Boolean {
        val connected = webSocketManager.startFunction(connection, functionType)
        if (!connected) {
            AppLog.w("无法为${audioType}创建WebSocket连接")
        }
        return connected
    }

    private fun createAudioRtpSender(connection: Connection, targetIp: String, targetPort: Int, payloadType: Int): AudioRtpSender? {
        val ssrc = connection.getSSRC()
        val rtpSender = AudioRtpSender(targetIp, targetPort, ssrc, payloadType, 48000)
        return if (rtpSender.start()) rtpSender else null
    }

    private fun sendAudioControlMessages(connectionId: String, ssrc: Long, isMedia: Boolean, enable: Boolean) {
        webSocketManager.getWebSocketClient(connectionId)?.let { client ->
            client.sendMessage(ControlMessage.createSsrcMapping(connectionId, ssrc))
            val controlMessage = if (isMedia) {
                ControlMessage.createMediaAudioControl(connectionId, enable)
            } else {
                ControlMessage.createMicAudioControl(connectionId, enable)
            }
            client.sendMessage(controlMessage)
        }
    }

    private fun startMediaAudio(connectionId: String, targetIp: String) {
        try {
            startForegroundService()
            val connection = stateManager.findConnectionById(connectionId) ?: run {
                AppLog.e("未找到连接: $connectionId")
                return
            }

            initializeMediaProjectionManager()
            initializeAudioCaptureManager()

            if (!startWebSocketFunction(connection, WebSocketManager.FUNCTION_MEDIA_AUDIO, "媒体音频")) return

            val rtpSender = createAudioRtpSender(connection, targetIp, connection.getMediaAudioPort(), 97)
            if (rtpSender == null) {
                AppLog.e("媒体音频RTP发送器创建失败")
                return
            }

            synchronized(mediaAudioRtpSenders) {
                mediaAudioRtpSenders[connectionId] = rtpSender
            }

            // 🚀 新增：设置WebSocket消息监听器，用于接收断开通知
            webSocketManager.setMessageListener(connectionId) { controlMessage ->
                handleWebSocketMessage(connectionId, controlMessage)
            }

            sendAudioControlMessages(connectionId, connection.getSSRC(), isMedia = true, enable = true)
            initializeMediaEncoder(isMedia = true)
            startMediaCapture()
            handleNewConnectionConfig(connectionId, mediaAudioRtpSenders, mediaAudioEncoder)
            updateNotificationOrStopService()
        } catch (e: Exception) {
            AppLog.e("启动媒体音频失败: $connectionId", e)
        }
    }

    private fun initializeMediaEncoder(isMedia: Boolean) {
        if (isMedia && mediaAudioEncoder == null) {
            mediaAudioEncoder = createAudioEncoder(2, 128000, mediaAudioRtpSenders)
            applySavedMediaAudioVolume()
        } else if (!isMedia && micAudioEncoder == null) {
            micAudioEncoder = createAudioEncoder(1, 64000, micAudioRtpSenders)
            applySavedMicAudioVolume()
        }
    }

    private fun createAudioEncoder(channelCount: Int, bitRate: Int, senders: ConcurrentHashMap<String, AudioRtpSender>): AudioEncoder? {
        val encoder = AudioEncoder(
            sampleRate = 48000,
            channelCount = channelCount,
            bitRate = bitRate,
            onEncodedData = { dataView, size, _ ->
                // 🚀 零拷贝优化：直接使用DataView，避免转换为ByteArray
                synchronized(senders) {
                    senders.values.forEach { sender ->
                        try {
                            sender.sendAacDataView(dataView, size)
                        } catch (e: Exception) {
                            AppLog.e("发送音频数据失败", e)
                        }
                    }
                }
            },

            onConfigurationDataViaWebSocket = { configData ->
                // 🎯 新架构：通过WebSocket发送配置数据
                // 遍历所有活跃的RTP发送器对应的连接，发送配置数据
                synchronized(senders) {
                    senders.keys.forEach { connectionId ->
                        webSocketManager.getWebSocketClient(connectionId)?.let { client ->
                            try {
                                client.sendAacConfig(configData)
                                AppLog.audio("通过WebSocket发送AAC配置数据到连接: $connectionId")
                            } catch (e: Exception) {
                                AppLog.e("通过WebSocket发送配置数据失败: $connectionId", e)
                            }
                        }
                    }
                }
            },
            lowLatencyMode = true
        )
        return if (encoder.start()) encoder else null
    }

    private fun startMediaCapture() {
        if (audioCaptureManager?.isMediaCapturing() != true) {
            val mediaProjectionCreated = mediaProjectionManager?.createMediaProjectionInForegroundService(
                MediaProjectionManager.FEATURE_MEDIA_AUDIO
            ) ?: false

            if (mediaProjectionCreated) {
                mediaProjectionManager?.getMediaProjection()?.let { projection ->
                    audioCaptureManager?.startMediaCapture(projection) { pcmData ->
                        mediaAudioEncoder?.encode(pcmData)
                    }
                }
            } else {
                AppLog.w("MediaProjection权限处理中...")
            }
        }
    }

    private fun handleNewConnectionConfig(connectionId: String, senders: ConcurrentHashMap<String, AudioRtpSender>, encoder: AudioEncoder?) {
        if (senders.size > 1 && encoder != null) {
            Thread {
                try {
                    Thread.sleep(200)

                    // 🐾 根源修复：通过WebSocket发送缓存配置数据，带去重检查
                    webSocketManager.getWebSocketClient(connectionId)?.let { client ->
                        encoder.sendCachedConfigurationDataViaWebSocket(connectionId) { configData ->
                            try {
                                client.sendAacConfig(configData)
                                AppLog.audio("通过WebSocket发送缓存AAC配置数据到新连接: $connectionId")
                            } catch (e: Exception) {
                                AppLog.e("通过WebSocket发送缓存配置数据失败: $connectionId", e)
                            }
                        }
                    }
                } catch (e: Exception) {
                    AppLog.w("新连接初始化延迟处理失败", e)
                }
            }.start()
        }
    }

    private fun startMicAudio(connectionId: String, targetIp: String) {
        try {
            startForegroundService()
            val connection = stateManager.findConnectionById(connectionId) ?: run {
                AppLog.e("未找到连接: $connectionId")
                return
            }

            initializeAudioCaptureManager()
            if (!startWebSocketFunction(connection, WebSocketManager.FUNCTION_MIC_AUDIO, "麦克风音频")) return

            val rtpSender = createAudioRtpSender(connection, targetIp, connection.getMicAudioPort(), 98)
            if (rtpSender == null) {
                AppLog.e("麦克风音频RTP发送器创建失败")
                return
            }

            synchronized(micAudioRtpSenders) {
                micAudioRtpSenders[connectionId] = rtpSender
            }

            // 🚀 新增：设置WebSocket消息监听器，用于接收断开通知
            webSocketManager.setMessageListener(connectionId) { controlMessage ->
                handleWebSocketMessage(connectionId, controlMessage)
            }

            sendAudioControlMessages(connectionId, connection.getSSRC(), isMedia = false, enable = true)
            initializeMediaEncoder(isMedia = false)
            startMicCapture()
            handleNewConnectionConfig(connectionId, micAudioRtpSenders, micAudioEncoder)
            updateNotificationOrStopService()
        } catch (e: Exception) {
            AppLog.e("启动麦克风音频失败: $connectionId", e)
        }
    }

    private fun startMicCapture() {
        if (audioCaptureManager?.isMicCapturing() != true) {
            audioCaptureManager?.startMicCapture { pcmData ->
                micAudioEncoder?.encode(pcmData)
            }
        }
    }



    private fun stopMediaAudio(connectionId: String) {
        stopAudioStream(connectionId, true, mediaAudioRtpSenders, WebSocketManager.FUNCTION_MEDIA_AUDIO) {
            audioCaptureManager?.stopMediaCapture()
            mediaAudioEncoder?.stop()
            mediaAudioEncoder = null
            mediaProjectionManager?.stopMediaAudioProjectionKeepPermission()
        }
    }

    private fun stopMicAudio(connectionId: String) {
        stopAudioStream(connectionId, false, micAudioRtpSenders, WebSocketManager.FUNCTION_MIC_AUDIO) {
            audioCaptureManager?.stopMicCapture()
            micAudioEncoder?.stop()
            micAudioEncoder = null
        }
    }

    private fun stopAudioStream(
        connectionId: String,
        isMedia: Boolean,
        senders: ConcurrentHashMap<String, AudioRtpSender>,
        functionType: String,
        stopCapture: () -> Unit
    ) {
        try {
            val connection = stateManager.findConnectionById(connectionId) ?: run {
                AppLog.e("未找到连接: $connectionId")
                return
            }

            cleanupRtpSender(senders.remove(connectionId), connectionId)
            sendAudioControlMessages(connectionId, connection.getSSRC(), isMedia = isMedia, enable = false)
            webSocketManager.stopFunction(connection, functionType)

            // 🚀 新增：如果该连接的所有音频流都已停止，清理WebSocket监听器
            if (!mediaAudioRtpSenders.containsKey(connectionId) && !micAudioRtpSenders.containsKey(connectionId)) {
                webSocketManager.removeListeners(connectionId)
                AppLog.audio("所有音频流已停止，清理WebSocket监听器: $connectionId")
            }

            if (senders.isEmpty()) {
                stopCapture()
            }
            updateNotificationOrStopService()
        } catch (e: Exception) {
            AppLog.e("停止音频流失败: $connectionId", e)
        }
    }

    /**
     * 🚀 新增：处理WebSocket消息，特别是断开连接消息
     */
    private fun handleWebSocketMessage(connectionId: String, controlMessage: ControlMessage) {
        when (controlMessage.type) {
            ControlMessage.TYPE_DISCONNECT -> {
                AppLog.audio("收到断开连接消息，停止音频服务: $connectionId")
                handleDisconnectMessage(connectionId)
            }
            else -> {
                AppLog.audio("收到其他WebSocket消息: ${controlMessage.type}, 连接: $connectionId")
            }
        }
    }

    /**
     * 🚀 新增：处理断开连接消息，停止所有音频服务并更新状态
     */
    private fun handleDisconnectMessage(connectionId: String) {
        try {
            // 检查并停止媒体音频
            if (mediaAudioRtpSenders.containsKey(connectionId)) {
                AppLog.audio("断开连接，停止媒体音频: $connectionId")
                stopMediaAudio(connectionId)

                // 更新StateManager中的媒体音频状态
                stateManager.updateConnection(connectionId) { conn ->
                    conn.withMediaAudio(false)
                }
            }

            // 检查并停止麦克风音频
            if (micAudioRtpSenders.containsKey(connectionId)) {
                AppLog.audio("断开连接，停止麦克风音频: $connectionId")
                stopMicAudio(connectionId)

                // 更新StateManager中的麦克风音频状态
                stateManager.updateConnection(connectionId) { conn ->
                    conn.withMicAudio(false)
                }
            }

            // 移除WebSocket消息监听器
            webSocketManager.removeListeners(connectionId)

            AppLog.audio("断开连接处理完成: $connectionId")
        } catch (e: Exception) {
            AppLog.e("处理断开连接消息失败: $connectionId", e)
        }
    }

    private fun cleanupRtpSender(sender: AudioRtpSender?, connectionId: String) {
        sender?.let {
            try {
                it.cleanup()
            } catch (e: Exception) {
                AppLog.e("清理RtpSender失败: $connectionId", e)
                try { it.stop() } catch (stopException: Exception) {
                    AppLog.e("停止RtpSender失败: $connectionId", stopException)
                }
            }
        }

        // 🐾 根源修复：清理编码器中的连接配置状态
        mediaAudioEncoder?.clearConnectionConfigState(connectionId)
        micAudioEncoder?.clearConnectionConfigState(connectionId)
    }

    /**
     * 处理媒体音频权限结果
     */
    private fun handleMediaAudioPermissionResult(resultCode: Int, resultData: Intent) {
        try {
            AppLog.audio("处理媒体音频权限结果: resultCode=$resultCode")

            // 初始化MediaProjection管理器
            initializeMediaProjectionManager()

            // 使用统一的MediaProjection管理器启动媒体音频
            if (mediaProjectionManager?.startMediaAudioProjection(resultData) == true) {
                AppLog.audio("媒体音频MediaProjection启动成功")

                // 延迟一小段时间确保MediaProjection完全初始化
                Thread {
                    try {
                        Thread.sleep(500)
                        // 重新尝试启动所有等待中的媒体音频连接
                        retryPendingMediaAudioConnections()
                    } catch (e: Exception) {
                        AppLog.e("延迟重试媒体音频连接失败", e)
                    }
                }.start()
            } else {
                AppLog.e("媒体音频MediaProjection启动失败")
            }

        } catch (e: Exception) {
            AppLog.e("处理媒体音频权限结果失败", e)
        }
    }

    /**
     * 重新尝试启动等待中的媒体音频连接
     */
    private fun retryPendingMediaAudioConnections() {
        try {
            // 获取所有启用媒体音频但RTP发送器不存在的连接
            val connections = stateManager.connections.value ?: return

            connections.filter { connection ->
                // 从 StateManager 获取连接状态
                val currentConnection = stateManager.getConnection(connection.connectionId)
                currentConnection?.isMediaAudioEnabled ?: false
            }.forEach { connection ->
                val connectionId = connection.connectionId
                if (!mediaAudioRtpSenders.containsKey(connectionId)) {
                    AppLog.audio("重新尝试启动媒体音频: 连接ID=$connectionId")
                    startMediaAudio(connectionId, connection.ipAddress)
                }
            }

        } catch (e: Exception) {
            AppLog.e("重新尝试启动媒体音频连接失败", e)
        }
    }

    private fun stopAllAudioStreams() {
        try {
            audioCaptureManager?.stopAll()
            mediaAudioEncoder?.stop()
            micAudioEncoder?.stop()

            cleanupAllRtpSenders(mediaAudioRtpSenders)
            cleanupAllRtpSenders(micAudioRtpSenders)

            mediaAudioRtpSenders.clear()
            micAudioRtpSenders.clear()

            AppLog.audio("所有音频流已停止")
        } catch (e: Exception) {
            AppLog.e("停止音频流失败", e)
        }
    }

    private fun cleanupAllRtpSenders(senders: ConcurrentHashMap<String, AudioRtpSender>) {
        senders.values.forEach { sender ->
            try {
                sender.cleanup()
            } catch (e: Exception) {
                AppLog.e("清理RtpSender失败", e)
                try { sender.stop() } catch (stopException: Exception) {
                    AppLog.e("停止RtpSender失败", stopException)
                }
            }
        }
    }

    private fun updateNotificationOrStopService() {
        try {
            val totalActiveStreams = mediaAudioRtpSenders.size + micAudioRtpSenders.size
            when (totalActiveStreams) {
                0 -> {
                    stopForeground(STOP_FOREGROUND_REMOVE)
                    stopSelf()
                }
                1 -> {
                    val title = when {
                        mediaAudioRtpSenders.isNotEmpty() -> "媒体音频传输中"
                        else -> "麦克风音频传输中"
                    }
                    updateNotification(title)
                }
                else -> updateNotification("音频流传输中 ($totalActiveStreams 个连接)")
            }
        } catch (e: Exception) {
            AppLog.e("更新通知状态失败", e)
        }
    }

    private fun updateNotification(title: String) {
        try {
            val notification = AppNotificationManager.createAudioStreamingNotification(this, title)
            val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.notify(AppNotificationManager.NotificationType.AUDIO_STREAMING.notificationId, notification)
        } catch (e: Exception) {
            AppLog.e("更新通知失败", e)
        }
    }

    private fun updateMediaAudioVolume(volume: Int) = updateEncoderVolume(mediaAudioEncoder, volume, "媒体音频")
    private fun updateMicAudioVolume(volume: Int) = updateEncoderVolume(micAudioEncoder, volume, "麦克风音频")

    private fun updateEncoderVolume(encoder: AudioEncoder?, volume: Int, audioType: String) {
        try {
            encoder?.updateVolume(volume / 100.0f)
        } catch (e: Exception) {
            AppLog.e("更新${audioType}音量失败: $volume%", e)
        }
    }

    private fun applySavedMediaAudioVolume() = applySavedVolume("media_audio_volume", mediaAudioEncoder)
    private fun applySavedMicAudioVolume() = applySavedVolume("mic_audio_volume", micAudioEncoder)

    private fun applySavedVolume(key: String, encoder: AudioEncoder?) {
        try {
            val sharedPrefs = getSharedPreferences("cast_settings", MODE_PRIVATE)
            val savedVolume = sharedPrefs.getInt(key, 80)
            encoder?.updateVolume(savedVolume / 100.0f)
        } catch (e: Exception) {
            AppLog.e("应用保存音量失败", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            stopAllAudioStreams()
            mediaProjectionManager = null
            audioCaptureManager = null
            AppLog.audio("音频流服务已销毁")
        } catch (e: Exception) {
            AppLog.e("销毁音频流服务失败", e)
        }
    }
}
