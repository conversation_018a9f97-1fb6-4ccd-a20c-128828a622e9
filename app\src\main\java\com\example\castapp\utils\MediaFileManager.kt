package com.example.castapp.utils

import android.content.Context
import android.content.SharedPreferences
import android.net.Uri
import androidx.core.net.toUri
import org.json.JSONObject

/**
 * 📁 媒体文件管理器
 * 负责媒体文件信息的保存、读取和管理
 */
class MediaFileManager(private val context: Context) {

    companion object {
        private const val PREFS_NAME = "media_files_prefs"
        private const val KEY_PREFIX = "media_"
    }

    private val sharedPrefs: SharedPreferences =
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    /**
     * 📁 媒体文件信息数据类
     */
    data class MediaFileInfo(
        val mediaId: String,
        val fileName: String,
        val uri: String,
        val contentType: String,
        val mediaType: String
    )

    /**
     * 📁 保存媒体文件信息（支持持久化URI权限）
     * @param mediaId 媒体窗口ID
     * @param fileName 文件名
     * @param uri 文件URI
     * @param contentType 内容类型（video/image）
     * @param mediaType 媒体类型（视频/图片）
     */
    fun saveMediaFileInfo(
        mediaId: String,
        fileName: String,
        uri: Uri,
        contentType: String,
        mediaType: String
    ) {
        try {
            // 📁 尝试获取持久化URI权限
            try {
                context.contentResolver.takePersistableUriPermission(
                    uri,
                    android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION
                )
                AppLog.d("📁 持久化URI权限获取成功: $uri")
            } catch (e: SecurityException) {
                AppLog.w("📁 无法获取持久化URI权限，可能是临时URI: $uri", e)
            }

            val mediaInfo = JSONObject().apply {
                put("mediaId", mediaId)
                put("fileName", fileName)
                put("uri", uri.toString())
                put("contentType", contentType)
                put("mediaType", mediaType)
                put("hasPersistentPermission", hasPersistentPermission(uri))
            }

            sharedPrefs.edit().apply {
                putString("$KEY_PREFIX$mediaId", mediaInfo.toString())
                apply()
            }

            AppLog.d("📁 媒体文件信息已保存: ID=$mediaId, 文件=$fileName, 类型=$contentType")

        } catch (e: Exception) {
            AppLog.e("📁 保存媒体文件信息失败: ID=$mediaId", e)
        }
    }

    /**
     * 📁 获取媒体文件信息（检查URI权限有效性）
     * @param mediaId 媒体窗口ID
     * @return 媒体文件信息，如果不存在或权限失效则返回null
     */
    fun getMediaFileInfo(mediaId: String): MediaFileInfo? {
        return try {
            val mediaInfoJson = sharedPrefs.getString("$KEY_PREFIX$mediaId", null)
            if (mediaInfoJson != null) {
                val jsonObject = JSONObject(mediaInfoJson)
                val uri = jsonObject.getString("uri").toUri()

                // 📁 检查URI权限是否仍然有效
                if (!isUriAccessible(uri)) {
                    AppLog.w("📁 媒体文件URI权限已失效: $mediaId, URI=$uri")
                    return null
                }

                MediaFileInfo(
                    mediaId = jsonObject.getString("mediaId"),
                    fileName = jsonObject.getString("fileName"),
                    uri = jsonObject.getString("uri"),
                    contentType = jsonObject.getString("contentType"),
                    mediaType = jsonObject.getString("mediaType")
                )
            } else {
                null
            }
        } catch (e: Exception) {
            AppLog.e("📁 获取媒体文件信息失败: ID=$mediaId", e)
            null
        }
    }

    /**
     * 📁 检查URI是否具有持久化权限
     */
    private fun hasPersistentPermission(uri: Uri): Boolean {
        return try {
            val persistedUris = context.contentResolver.persistedUriPermissions
            persistedUris.any { it.uri == uri && it.isReadPermission }
        } catch (e: Exception) {
            AppLog.e("📁 检查持久化权限失败: $uri", e)
            false
        }
    }

    /**
     * 📁 检查URI是否可访问
     */
    private fun isUriAccessible(uri: Uri): Boolean {
        return try {
            // 尝试打开输入流来检查访问权限
            context.contentResolver.openInputStream(uri)?.use {
                true
            } ?: false
        } catch (e: Exception) {
            AppLog.w("📁 URI不可访问: $uri", e)
            false
        }
    }
}
