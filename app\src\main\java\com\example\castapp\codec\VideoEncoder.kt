package com.example.castapp.codec

import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaCodecList
import android.media.MediaFormat
import android.os.Build
import android.view.Surface
import com.example.castapp.utils.ResourceManager
import com.example.castapp.utils.ResourceManager.safeExecute
import kotlin.math.abs
import java.util.Locale
import java.nio.ByteBuffer
import java.util.concurrent.atomic.AtomicInteger
import com.example.castapp.utils.AppLog

/**
 * H.264视频编码器
 */
class VideoEncoder(
    private val width: Int,
    private val height: Int,
    private var bitRate: Int = 8_000_000, // 8Mbps
    private val frameRate: Int = 30,
    private val onEncodedData: (ByteBuffer, Int) -> Unit, // 直接传递ByteBuffer和大小，避免拷贝
    private val onConfigurationData: ((ByteArray?, ByteArray?) -> Unit)? = null, // 配置数据回调（兼容性）
    private val onConfigurationDataWithResolution: ((ByteArray?, ByteArray?, Int, Int) -> Unit)? = null, // 🚀 新增：包含分辨率的配置数据回调
    private val onConfigurationDataWithOrientation: ((ByteArray?, ByteArray?, Int, Int, Int) -> Unit)? = null // 🎯 横竖屏适配：包含方向信息的配置数据回调
) {
    companion object {        private const val MIME_TYPE = MediaFormat.MIMETYPE_VIDEO_AVC
        private const val I_FRAME_INTERVAL = 1 // 保持1秒关键帧间隔，平衡延迟和画质
    }

    private var mediaCodec: MediaCodec? = null
    private var inputSurface: Surface? = null
    private var isRunning = false
    private var configurationSent = false

    // 选中的硬件编码器信息
    private var selectedEncoderName: String? = null
    private var selectedEncoderType: String? = null

    // 缓存SPS/PPS数据，用于新连接
    private var cachedSpsData: ByteArray? = null
    private var cachedPpsData: ByteArray? = null

    // 🎯 横竖屏适配：当前方向信息
    private var currentOrientation: Int = android.content.res.Configuration.ORIENTATION_PORTRAIT

    // 延迟监控
    private var lastFrameTime = 0L
    private var frameCount = 0L

    // 码率监控相关变量
    private var totalDataSent = 0L
    private var lastBitrateCheckTime = 0L
    private var lastDataSent = 0L

    // 🚀 异步网络发送线程池 - 专门处理网络发送，避免主线程阻塞
    private val networkExecutor = java.util.concurrent.Executors.newSingleThreadExecutor { r ->
        Thread(r, "VideoEncoder-AsyncNetwork").apply {
            priority = Thread.NORM_PRIORITY
            isDaemon = true
        }
    }

    /**
     * 缓冲区引用管理器 - 解决ByteBuffer生命周期问题
     * 🚀 零拷贝核心：通过引用计数延迟释放MediaCodec缓冲区
     */
    private class BufferReference(
        val originalBuffer: ByteBuffer,
        private val codec: MediaCodec,
        private val bufferIndex: Int,
        val dataSize: Int
    ) {
        private val refCount = AtomicInteger(1)
        @Volatile private var isReleased = false

        // 创建数据视图，避免拷贝
        val dataView: ByteBuffer by lazy {
            originalBuffer.asReadOnlyBuffer().apply {
                position(0)
                limit(dataSize)
            }
        }

        /**
         * 减少引用计数，当计数为0时释放MediaCodec缓冲区
         */
        fun release() {
            if (isReleased) return

            val count = refCount.decrementAndGet()
            if (count == 0) {
                isReleased = true
                try {
                    codec.releaseOutputBuffer(bufferIndex, false)
                    // 🚀 CPU优化：移除高频缓冲区释放日志，减少CPU占用
                } catch (e: Exception) {
                    AppLog.w("释放MediaCodec缓冲区时出错: ${e.message}")
                }
            } else if (count < 0) {
                AppLog.w("缓冲区引用计数异常: count=$count")
            }
        }
    }

    /**
     * MediaCodec异步回调处理器
     * 🚀 异步模式：系统驱动的高效编码处理
     */
    private inner class MediaCodecCallback : MediaCodec.Callback() {

        override fun onInputBufferAvailable(codec: MediaCodec, index: Int) {
            // 对于Surface输入模式，输入缓冲区由系统自动管理
            // 这里不需要处理，Surface会直接向编码器提供数据
        }

        override fun onOutputBufferAvailable(codec: MediaCodec, index: Int, info: MediaCodec.BufferInfo) {
            if (!isRunning) return

            try {
                val outputBuffer = codec.getOutputBuffer(index)
                if (outputBuffer != null && info.size > 0) {
                    // 🚀 零拷贝优化：直接传递ByteBuffer，避免数据拷贝
                    outputBuffer.position(info.offset)
                    outputBuffer.limit(info.offset + info.size)

                    // 🚀 CPU优化：进一步减少性能监控频率
                    frameCount++
                    if (frameCount % 3600 == 0L) { // 每3600帧（120秒）监控一次，减少CPU占用
                        val currentTime = System.nanoTime()
                        if (lastFrameTime > 0) {
                            val avgInterval = (currentTime - lastFrameTime) / 3600_000_000
                            val fps = String.format(Locale.US, "%.1f", 1000.0 / avgInterval)
                            AppLog.video("异步编码帧率: ${fps}fps")
                        }
                        lastFrameTime = currentTime
                    }

                    // 码率监控 - 每30秒检查一次
                    totalDataSent += info.size
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastBitrateCheckTime > 30000) { // 30秒
                        val timeDiffSeconds = (currentTime - lastBitrateCheckTime) / 1000.0
                        val dataDiff = totalDataSent - lastDataSent
                        val actualBitrateMbps = (dataDiff * 8) / (timeDiffSeconds * 1_000_000)
                        val targetBitrateMbps = bitRate / 1_000_000.0

                        // 只在码率差异较大时才输出日志
                        val bitrateDiff = abs(actualBitrateMbps - targetBitrateMbps)
                        if (bitrateDiff > 1.0) {
                            AppLog.video("异步码率监控: 目标=${String.format(Locale.US, "%.1f", targetBitrateMbps)}Mbps, " +
                                    "实际=${String.format(Locale.US, "%.1f", actualBitrateMbps)}Mbps")
                        }
                        lastBitrateCheckTime = currentTime
                        lastDataSent = totalDataSent
                    }

                    // 🚀 零拷贝核心优化：使用引用计数管理缓冲区生命周期
                    // 创建缓冲区引用，延迟释放MediaCodec缓冲区直到所有网络发送完成
                    val bufferRef = BufferReference(outputBuffer, codec, index, info.size)

                    // 在后台线程中执行网络发送，使用零拷贝缓冲区视图
                    networkExecutor.execute {
                        try {
                            // 直接传递只读缓冲区视图，完全避免数据拷贝
                            onEncodedData(bufferRef.dataView, info.size)
                            // 🚀 CPU优化：移除高频帧数据发送日志，减少CPU占用
                        } catch (e: Exception) {
                            if (isRunning) {
                                AppLog.e("异步网络发送失败", e)
                            }
                        } finally {
                            // 网络发送完成后释放缓冲区引用
                            bufferRef.release()
                        }
                    }
                } else {
                    // 如果没有有效数据，直接释放缓冲区
                    codec.releaseOutputBuffer(index, false)
                }

            } catch (e: Exception) {
                if (isRunning) {
                    AppLog.e("异步编码输出处理出错", e)
                }
                // 确保在异常情况下也释放缓冲区
                try {
                    codec.releaseOutputBuffer(index, false)
                } catch (releaseException: Exception) {
                    AppLog.w("释放异常缓冲区时出错: ${releaseException.message}")
                }
            }
        }

        override fun onError(codec: MediaCodec, e: MediaCodec.CodecException) {
            AppLog.e("MediaCodec异步编码出错: ${e.message}", e)
            // 在错误情况下停止编码器
            if (isRunning) {
                isRunning = false
            }
        }

        override fun onOutputFormatChanged(codec: MediaCodec, format: MediaFormat) {
            AppLog.video("异步编码器输出格式改变: $format")

            // 发送SPS/PPS配置数据
            if (!configurationSent) {
                sendConfigurationData(format)
                configurationSent = true
            }
        }
    }

    /**
     * 检测并获取可用的硬件H.264编码器列表
     */
    private fun getAvailableHardwareEncoders(): List<MediaCodecInfo> {
        val hardwareEncoders = mutableListOf<MediaCodecInfo>()
        val codecList = MediaCodecList(MediaCodecList.ALL_CODECS)

        for (codecInfo in codecList.codecInfos) {
            if (!codecInfo.isEncoder) continue

            val supportedTypes = codecInfo.supportedTypes
            if (!supportedTypes.contains(MIME_TYPE)) continue

            // 检查是否为硬件编码器
            if (isHardwareEncoder(codecInfo)) {
                hardwareEncoders.add(codecInfo)

                // 只在获取详细信息失败时输出警告
                try {
                    codecInfo.getCapabilitiesForType(MIME_TYPE)
                } catch (e: Exception) {
                    AppLog.w("无法获取编码器 ${codecInfo.name} 详细信息: ${e.message}")
                }
            }
        }

        return hardwareEncoders
    }

    /**
     * 判断编码器是否为硬件编码器
     */
    private fun isHardwareEncoder(codecInfo: MediaCodecInfo): Boolean {
        val name = codecInfo.name.lowercase()

        // 软件编码器通常包含这些关键词
        val softwareKeywords = listOf(
            "sw", "software", "google", "ffmpeg", "c2.android"
        )

        // 检查是否包含软件编码器关键词
        for (keyword in softwareKeywords) {
            if (name.contains(keyword)) {
                return false
            }
        }

        // 硬件编码器通常包含这些厂商关键词
        val hardwareKeywords = listOf(
            "qcom", "qualcomm", "qti", "mtk", "mediatek", "exynos",
            "samsung", "hisi", "kirin", "nvidia", "intel", "amd",
            "omx", "hantro", "rk", "rockchip", "amlogic"
        )

        for (keyword in hardwareKeywords) {
            if (name.contains(keyword)) {
                return true
            }
        }

        // 如果没有明确的关键词，但不在软件编码器列表中，则认为是硬件编码器
        return true
    }

    /**
     * 选择最佳的硬件编码器
     */
    private fun selectBestHardwareEncoder(hardwareEncoders: List<MediaCodecInfo>): MediaCodecInfo? {
        if (hardwareEncoders.isEmpty()) {
            return null
        }

        // 优先级排序：高通 > 联发科 > 三星 > 其他
        val priorityOrder = listOf("qcom", "qualcomm", "qti", "mtk", "mediatek", "exynos", "samsung")

        for (priority in priorityOrder) {
            val encoder = hardwareEncoders.find {
                it.name.lowercase().contains(priority)
            }
            if (encoder != null) {
                return encoder
            }
        }

        // 如果没有匹配优先级的，选择第一个
        return hardwareEncoders.first()
    }

    /**
     * 启动编码器
     */
    fun start(): Surface? {
        return safeExecute("启动H.264硬件编码器") {
            AppLog.video("开始启动H.264硬件编码器...")

            // 检测可用的硬件编码器
            val hardwareEncoders = getAvailableHardwareEncoders()

            if (hardwareEncoders.isEmpty()) {
                val errorMsg = "未检测到任何H.264硬件编码器，无法启动编码器"
                AppLog.e(errorMsg)
                throw IllegalStateException(errorMsg)
            }

            // 选择最佳的硬件编码器
            val selectedEncoder = selectBestHardwareEncoder(hardwareEncoders)
                ?: throw IllegalStateException("无法选择合适的硬件编码器")

            selectedEncoderName = selectedEncoder.name
            selectedEncoderType = "硬件编码器"

            createEncoderWithConfig(createStandardFormat(), selectedEncoder)
        }
    }

    /**
     * 创建标准格式 - 优化发热问题
     */
    private fun createStandardFormat(): MediaFormat {
        return MediaFormat.createVideoFormat(MIME_TYPE, width, height).apply {
            setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface)
            setInteger(MediaFormat.KEY_BIT_RATE, bitRate)
            setInteger(MediaFormat.KEY_FRAME_RATE, frameRate)
            setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, I_FRAME_INTERVAL)
            setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileHigh)
            setInteger(MediaFormat.KEY_LEVEL, MediaCodecInfo.CodecProfileLevel.AVCLevel4)
            setInteger(MediaFormat.KEY_BITRATE_MODE, MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_CBR)

            // 进一步优化参数以减少发热，同时保持画质
            setInteger(MediaFormat.KEY_COMPLEXITY, 0) // 最低复杂度
            // KEY_QUALITY 需要 API 28+，添加版本检查
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                setInteger(MediaFormat.KEY_QUALITY, 75) // 适当提高到85，平衡画质和发热
            }
            setInteger(MediaFormat.KEY_PRIORITY, 0) // 最低优先级，减少CPU占用

            // 添加更多省电参数
            try {
                setInteger(MediaFormat.KEY_LEVEL, MediaCodecInfo.CodecProfileLevel.AVCLevel31) // 限制编码级别
                setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileMain) // 使用主配置，平衡性能和画质
            } catch (e: Exception) {
                AppLog.w("无法设置编码级别和配置: ${e.message}")
            }

            // 厂商特定优化 - 平衡省电和性能
            try {
                setInteger("vendor.qti-ext-enc-preprocess.enable", 0) // 禁用预处理以减少CPU负担
                setInteger("vendor.qti-ext-enc-ltr.enable", 0) // 禁用长期参考帧
                setInteger("vendor.qti-ext-enc-avc-vui.enable", 0) // 禁用VUI信息
                setInteger("vendor.qti-ext-enc-low-power.enable", 1) // 启用低功耗模式
                setInteger("vendor.qti-ext-enc-power-save.enable", 1) // 启用省电模式
                setInteger("vendor.qti-ext-enc-entropy-mode.value", 0) // 使用CAVLC而非CABAC，减少计算复杂度
                setInteger("vendor.qti-ext-enc-slice-spacing.value", 0) // 禁用切片间隔以减少处理开销
            } catch (_: Exception) {
                // 忽略不支持的厂商特定参数
            }
        }
    }

    /**
     * 使用指定格式和编码器创建编码器 - 异步回调模式
     */
    private fun createEncoderWithConfig(format: MediaFormat, codecInfo: MediaCodecInfo): Surface? {
        return safeExecute("创建异步硬件编码器: ${codecInfo.name}") {
            // 验证编码器基本能力
            validateEncoderCapabilities(codecInfo, format)

            val codec = MediaCodec.createByCodecName(codecInfo.name)

            try {
                // 🚀 设置异步回调 - 在configure之前设置
                codec.setCallback(MediaCodecCallback())

                codec.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)
                inputSurface = codec.createInputSurface()

                // 只有在成功启动后才赋值给成员变量
                mediaCodec = codec
                isRunning = true

                // 🚀 启动异步编码器 - 系统将自动调用回调
                codec.start()

                AppLog.video("异步编码器启动: ${codecInfo.name}, ${width}x${height}@${frameRate}fps, ${bitRate / 1_000_000}Mbps")

                inputSurface
            } catch (e: Exception) {
                // 确保在异常情况下释放资源
                safeReleaseCodec(codec)
                inputSurface = null
                throw e
            }
        }
    }

    /**
     * 安全释放MediaCodec资源
     */
    private fun safeReleaseCodec(codec: MediaCodec) {
        ResourceManager.safeReleaseMediaCodec(codec, "视频编码器")
    }

    /**
     * 验证编码器能力
     */
    private fun validateEncoderCapabilities(codecInfo: MediaCodecInfo, format: MediaFormat) {
        safeExecute("验证编码器 ${codecInfo.name} 能力") {
            val capabilities = codecInfo.getCapabilitiesForType(MIME_TYPE)
            val encoderCapabilities = capabilities.encoderCapabilities
            val videoCapabilities = capabilities.videoCapabilities

            // 检查分辨率支持
            if (videoCapabilities != null) {
                val supportedWidths = videoCapabilities.supportedWidths
                val supportedHeights = videoCapabilities.supportedHeights

                // 检查当前分辨率是否在支持范围内
                if (!supportedWidths.contains(width) || !supportedHeights.contains(height)) {
                    val minWidth = supportedWidths.lower
                    val minHeight = supportedHeights.lower

                    AppLog.e("错误：当前分辨率 ${width}x${height} 不在编码器支持范围内")
                    AppLog.e("  支持的宽度: ${supportedWidths.lower}-${supportedWidths.upper}")
                    AppLog.e("  支持的高度: ${supportedHeights.lower}-${supportedHeights.upper}")
                    AppLog.e("  最小支持分辨率: ${minWidth}x${minHeight}")

                    throw IllegalArgumentException(
                        "分辨率 ${width}x${height} 超出编码器支持范围。" +
                        "最小支持分辨率: ${minWidth}x${minHeight}"
                    )
                }
            }

            // 检查是否支持CBR模式，如果不支持则使用VBR模式
            if (!encoderCapabilities.isBitrateModeSupported(MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_CBR)) {
                AppLog.w("编码器 ${codecInfo.name} 不支持CBR模式，将使用VBR模式")
                format.setInteger(MediaFormat.KEY_BITRATE_MODE, MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_VBR)
            }
        } ?: throw IllegalStateException("验证编码器 ${codecInfo.name} 能力失败")
    }

    /**
     * 停止异步编码器
     */
    fun stop() {
        isRunning = false
        configurationSent = false
        cachedSpsData = null
        cachedPpsData = null
        selectedEncoderName = null
        selectedEncoderType = null

        // 🚀 关闭异步网络发送线程池
        try {
            networkExecutor.shutdown()
            if (!networkExecutor.awaitTermination(2, java.util.concurrent.TimeUnit.SECONDS)) {
                networkExecutor.shutdownNow()
                AppLog.video("强制关闭异步网络发送线程池")
            }
        } catch (e: Exception) {
            AppLog.w("关闭异步网络发送线程池时出错: ${e.message}")
            networkExecutor.shutdownNow()
        }

        // 使用ResourceManager安全释放资源
        ResourceManager.safeReleaseAll(
            inputSurface to "输入Surface",
            mediaCodec to "异步视频编码器"
        )
        inputSurface = null
        mediaCodec = null

        AppLog.video("异步硬件编码器已停止")
    }



    /**
     * 超快速停止异步编码器 - 立即停止但在后台释放资源
     * 用于分辨率调整时最小化延迟
     */
    fun ultraFastStop(): Pair<MediaCodec?, Surface?> {
        AppLog.video("开始超快速停止异步编码器")

        // 保存旧资源引用
        val oldMediaCodec = mediaCodec
        val oldInputSurface = inputSurface

        // 🚀 异步模式：立即停止回调处理
        isRunning = false
        configurationSent = false
        cachedSpsData = null
        cachedPpsData = null
        selectedEncoderName = null
        selectedEncoderType = null

        // 清空当前引用
        mediaCodec = null
        inputSurface = null

        AppLog.video("异步编码器超快速停止完成，返回旧资源用于后台释放")
        return Pair(oldMediaCodec, oldInputSurface)
    }




    /**
     * 发送配置数据（SPS/PPS）
     * 🚀 增强版：包含分辨率信息传递
     */
    private fun sendConfigurationData(format: MediaFormat) {
        try {
            var spsData: ByteArray? = null
            var ppsData: ByteArray? = null

            // 获取SPS数据
            val spsBuffer = format.getByteBuffer("csd-0")
            if (spsBuffer != null) {
                spsData = ByteArray(spsBuffer.remaining())
                spsBuffer.get(spsData)
                cachedSpsData = spsData.copyOf() // 缓存SPS数据
                AppLog.video("获取SPS数据: ${spsData.size} bytes")
            }

            // 获取PPS数据
            val ppsBuffer = format.getByteBuffer("csd-1")
            if (ppsBuffer != null) {
                ppsData = ByteArray(ppsBuffer.remaining())
                ppsBuffer.get(ppsData)
                cachedPpsData = ppsData.copyOf() // 缓存PPS数据
                AppLog.video("获取PPS数据: ${ppsData.size} bytes")
            }

            // 🎯 优先使用包含方向信息的回调
            if (onConfigurationDataWithOrientation != null) {
                onConfigurationDataWithOrientation.invoke(spsData, ppsData, width, height, currentOrientation)
                AppLog.video("🎯 通过WebSocket发送H.264配置数据，包含分辨率和方向: ${width}x${height}, 方向: ${getOrientationName(currentOrientation)}")
            } else if (onConfigurationDataWithResolution != null) {
                // 🚀 回退到包含分辨率信息的回调
                onConfigurationDataWithResolution.invoke(spsData, ppsData, width, height)
                AppLog.video("通过WebSocket发送H.264配置数据，包含分辨率: ${width}x${height}")
            } else {
                // 🚀 最后回退到兼容性回调
                onConfigurationData?.invoke(spsData, ppsData)
                AppLog.video("通过WebSocket发送H.264配置数据（兼容模式）")
            }

        } catch (e: Exception) {
            AppLog.e("发送配置数据失败", e)
        }
    }

    /**
     * 🚀 新增：通过WebSocket为新连接发送包含分辨率信息的缓存配置数据
     */
    fun sendCachedConfigurationDataViaWebSocketWithResolution(onConfigData: (ByteArray?, ByteArray?, Int, Int) -> Unit) {
        try {
            onConfigData(cachedSpsData, cachedPpsData, width, height)
            AppLog.video("通过WebSocket发送缓存的H.264配置数据，包含分辨率: ${width}x${height}")
        } catch (e: Exception) {
            AppLog.e("通过WebSocket发送缓存配置数据失败", e)
        }
    }

    /**
     * 动态调整码率
     * @param newBitRate 新的码率值（bps）
     */
    fun updateBitRate(newBitRate: Int) {
        try {
            if (!isRunning || mediaCodec == null) {
                AppLog.w("编码器未运行，无法调整码率")
                return
            }

            if (newBitRate == bitRate) {
                AppLog.video("码率未变化，跳过调整: $newBitRate")
                return
            }

            val oldBitRate = bitRate
            AppLog.video("开始强制调整码率: ${oldBitRate / 1_000_000}Mbps -> ${newBitRate / 1_000_000}Mbps")

            // 方法1：使用多个参数强制调整码率
            try {
                val bitrateBundle = android.os.Bundle().apply {
                    putInt(MediaFormat.KEY_BIT_RATE, newBitRate)
                    putInt("video-bitrate", newBitRate)
                    putInt("bitrate", newBitRate)
                    // 强制立即生效
                    putInt("request-sync", 0)
                }
                mediaCodec?.setParameters(bitrateBundle)
                AppLog.video("已设置码率参数: ${newBitRate / 1_000_000}Mbps")
            } catch (e: Exception) {
                AppLog.w("设置码率参数失败: ${e.message}")
            }

            // 方法2：强制请求关键帧，确保码率变化立即可见
            try {
                val keyFrameBundle = android.os.Bundle().apply {
                    putInt("request-sync", 0)
                }
                mediaCodec?.setParameters(keyFrameBundle)
                AppLog.video("已强制请求关键帧")
            } catch (e: Exception) {
                AppLog.w("请求关键帧失败: ${e.message}")
            }

            // 方法3：如果是高通编码器，尝试特定的参数
            if (selectedEncoderName?.contains("qcom", ignoreCase = true) == true) {
                try {
                    val qcomBundle = android.os.Bundle().apply {
                        putInt("vendor.qti-ext-enc-bitrate-mode.value", 2) // CBR模式
                        putInt("vendor.qti-ext-enc-target-bitrate.value", newBitRate)
                    }
                    mediaCodec?.setParameters(qcomBundle)
                    AppLog.video("已设置高通编码器特定参数")
                } catch (e: Exception) {
                    AppLog.w("设置高通编码器参数失败: ${e.message}")
                }
            }

            // 立即更新内部码率值
            bitRate = newBitRate

            // 延迟验证码率是否生效
            Thread {
                Thread.sleep(1000) // 等待1秒
                val currentBitrate = getCurrentBitRate()
                if (currentBitrate == newBitRate) {
                    AppLog.video("码率调整验证成功: ${currentBitrate / 1_000_000}Mbps")
                } else {
                    AppLog.w("码率调整验证失败: 期望${newBitRate / 1_000_000}Mbps，实际${currentBitrate / 1_000_000}Mbps")
                }
            }.start()

            AppLog.video("码率调整完成: ${bitRate / 1_000_000}Mbps (编码器: $selectedEncoderName)")

        } catch (e: Exception) {
            AppLog.e("调整码率失败: ${e.message}", e)
            AppLog.w("码率调整失败，保持原码率: ${bitRate / 1_000_000}Mbps")
        }
    }

    /**
     * 获取当前码率
     */
    fun getCurrentBitRate(): Int {
        return bitRate
    }

    /**
     * 强制重新配置码率（更激进的方法）
     */
    fun forceUpdateBitRate(newBitRate: Int) {
        try {
            if (!isRunning || mediaCodec == null) {
                AppLog.w("编码器未运行，无法强制调整码率")
                return
            }

            AppLog.video("开始强制重新配置码率: ${bitRate / 1_000_000}Mbps -> ${newBitRate / 1_000_000}Mbps")

            // 方法1：切换到VBR模式再切换回CBR模式
            try {
                // 先切换到VBR模式
                val vbrBundle = android.os.Bundle().apply {
                    putInt(MediaFormat.KEY_BITRATE_MODE, MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_VBR)
                    putInt(MediaFormat.KEY_BIT_RATE, newBitRate)
                }
                mediaCodec?.setParameters(vbrBundle)
                Thread.sleep(100) // 短暂等待

                // 再切换回CBR模式
                val cbrBundle = android.os.Bundle().apply {
                    putInt(MediaFormat.KEY_BITRATE_MODE, MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_CBR)
                    putInt(MediaFormat.KEY_BIT_RATE, newBitRate)
                    putInt("request-sync", 0)
                }
                mediaCodec?.setParameters(cbrBundle)
                AppLog.video("已通过模式切换强制更新码率")
            } catch (e: Exception) {
                AppLog.w("模式切换方法失败: ${e.message}")
                // 回退到普通方法
                updateBitRate(newBitRate)
            }

            bitRate = newBitRate
            AppLog.video("强制码率调整完成: ${bitRate / 1_000_000}Mbps")

        } catch (e: Exception) {
            AppLog.e("强制调整码率失败: ${e.message}", e)
        }
    }

    /**
     * 获取当前使用的编码器信息
     */
    fun getEncoderInfo(): Pair<String?, String?> {
        return Pair(selectedEncoderName, selectedEncoderType)
    }

    /**
     * 检查编码器是否正在使用硬件加速
     */
    fun isUsingHardwareAcceleration(): Boolean {
        return selectedEncoderName != null && selectedEncoderType == "硬件编码器"
    }

    // ========== 🎯 横竖屏适配功能 ==========

    /**
     * 设置当前屏幕方向
     */
    fun setOrientation(orientation: Int) {
        currentOrientation = orientation
        AppLog.video("🎯 VideoEncoder方向已设置: ${getOrientationName(orientation)}")
    }



    /**
     * 获取方向名称（用于日志）
     */
    private fun getOrientationName(orientation: Int): String {
        return when (orientation) {
            android.content.res.Configuration.ORIENTATION_LANDSCAPE -> "横屏"
            android.content.res.Configuration.ORIENTATION_PORTRAIT -> "竖屏"
            else -> "未知($orientation)"
        }
    }
}
