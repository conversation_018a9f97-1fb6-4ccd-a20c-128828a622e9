package com.example.castapp.manager

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.fragment.app.FragmentManager
import com.example.castapp.model.RemoteSenderConnection
import com.example.castapp.remote.RemoteSenderWebSocketClient
import com.example.castapp.ui.dialog.RemoteSenderControlDialog
import com.example.castapp.utils.AppLog
import com.example.castapp.websocket.ControlMessage

/**
 * 🐾 发送端管理器
 * 负责处理所有发送端相关的业务逻辑，包括连接管理、消息处理和控制对话框管理
 * 
 * 主要功能：
 * - 发送端设备的连接和断开
 * - 发送端消息的处理和分发
 * - 发送端控制对话框的管理
 * - 与 RemoteConnectionManager 协作进行状态管理
 */
class RemoteSenderManager(
    private val connectionManager: RemoteConnectionManager = RemoteConnectionManager.getInstance()
) {

    // 🐾 UI线程Handler，用于在UI线程显示Toast
    private val uiHandler = Handler(Looper.getMainLooper())

    /**
     * 连接到远程发送端设备
     */
    fun connectToRemoteDevice(
        connection: RemoteSenderConnection,
        context: Context,
        onConnectionStateChanged: ((RemoteSenderConnection, Boolean) -> Unit)? = null
    ): Boolean {
        AppLog.d("尝试连接到远程设备: ${connection.getDisplayText()}")

        // 🐾 检查是否已经有活跃连接存在
        val existingClient = connectionManager.getRemoteClient(connection.id)
        if (existingClient != null) {
            val isActuallyConnected = existingClient.getConnectionStatus()
            if (isActuallyConnected) {
                AppLog.d("远程设备已连接: ${connection.deviceName}")
                showToastOnUiThread(context, "设备已连接：${connection.deviceName}")
                return true
            } else {
                // 🐾 客户端存在但未连接，清理并重新连接
                AppLog.d("发现断开的WebSocket客户端，清理并重新连接: ${connection.deviceName}")
                connectionManager.removeRemoteClient(connection.id)
            }
        }

        val client = RemoteSenderWebSocketClient(
            targetIp = connection.ipAddress,
            targetPort = connection.port,
            deviceName = connection.deviceName,
            context = context, // 🎯 新增：传递Context参数
            onMessageReceived = { message ->
                handleRemoteMessage(connection, message)
            },
            onConnectionStateChanged = { isConnected ->
                handleConnectionStateChanged(connection, isConnected, context, onConnectionStateChanged)
            }
        )

        return if (client.connect()) {
            // 🐾 使用全局管理器存储连接
            connectionManager.addRemoteClient(connection.id, client)
            showToastOnUiThread(context, "正在连接到：${connection.deviceName}")
            true
        } else {
            showToastOnUiThread(context, "连接失败：${connection.deviceName}")
            false
        }
    }

    /**
     * 断开与远程发送端设备的连接
     */
    fun disconnectFromRemoteDevice(
        connection: RemoteSenderConnection,
        context: Context,
        onConnectionStateChanged: ((RemoteSenderConnection, Boolean) -> Unit)? = null
    ) {
        AppLog.d("断开与远程设备的连接: ${connection.getDisplayText()}")

        // 🐾 使用全局管理器断开连接
        connectionManager.removeRemoteClient(connection.id)

        // 🐾 更新全局状态副本
        val updatedConnection = connection.withConnectionState(false)
        connectionManager.updateGlobalConnectionState(updatedConnection)

        // 🐾 立即保存状态，不依赖Fragment
        connectionManager.saveConnectionsToPreferences(context)

        // 通知状态变化
        onConnectionStateChanged?.invoke(updatedConnection, false)

        showToastOnUiThread(context, "已断开连接：${connection.deviceName}")
    }

    /**
     * 显示发送端控制对话框
     */
    fun showRemoteSenderControlDialog(
        connection: RemoteSenderConnection,
        fragmentManager: FragmentManager,
        context: Context
    ) {
        // 🐾 使用全局管理器获取连接
        val client = connectionManager.getRemoteClient(connection.id)
        if (client != null) {
            val dialog = RemoteSenderControlDialog(connection, client)

            // 🐾 使用全局管理器注册对话框以便消息转发
            connectionManager.addActiveControlDialog(connection.id, dialog)

            // 设置对话框关闭监听器，用于清理注册
            dialog.setOnDismissListener {
                // 🐾 从全局管理器清理注册
                connectionManager.removeActiveControlDialog(connection.id)
                AppLog.d("远程控制对话框已关闭，从全局管理器清理注册: ${connection.id}")
            }

            dialog.show(fragmentManager, "RemoteSenderControlDialog")
            AppLog.d("显示远程控制窗口: ${connection.getDisplayText()}")
        } else {
            showToastOnUiThread(context, "未找到连接客户端")
        }
    }

    /**
     * 处理连接状态变化
     */
    private fun handleConnectionStateChanged(
        connection: RemoteSenderConnection,
        isConnected: Boolean,
        context: Context,
        onConnectionStateChanged: ((RemoteSenderConnection, Boolean) -> Unit)?
    ) {
        // 🐾 更新全局状态副本
        val updatedConnection = connection.withConnectionState(isConnected)
        connectionManager.updateGlobalConnectionState(updatedConnection)

        // 🐾 立即保存状态，使用安全的Context获取方式
        connectionManager.saveConnectionsToPreferences(context)

        // 通知状态变化
        onConnectionStateChanged?.invoke(updatedConnection, isConnected)

        val statusMessage = if (isConnected) {
            "已连接到：${connection.deviceName}"
        } else {
            // 连接断开时从全局管理器中移除WebSocket客户端
            connectionManager.removeRemoteClient(connection.id)
            // 🐾 注意：不删除全局状态，只是更新为未连接状态（已在上面更新）
            "与${connection.deviceName}的连接已断开"
        }

        // 🐾 修复：在UI线程显示Toast
        showToastOnUiThread(context, statusMessage)
    }

    /**
     * 处理来自远程发送端设备的消息
     */
    private fun handleRemoteMessage(connection: RemoteSenderConnection, message: ControlMessage) {
        AppLog.d("处理远程设备消息: ${message.type} from ${connection.deviceName}")

        when (message.type) {
            ControlMessage.TYPE_REMOTE_CONTROL_RESPONSE -> {
                handleRemoteControlResponse(connection, message)
            }

            ControlMessage.TYPE_REMOTE_SETTINGS_SYNC -> {
                forwardMessageToControlDialog(connection.id, message) { dialog ->
                    dialog.handleSettingsSync(message)
                }
            }

            ControlMessage.TYPE_REMOTE_CONNECTION_LIST_SYNC -> {
                forwardMessageToControlDialog(connection.id, message) { dialog ->
                    dialog.handleSettingsSync(message) // 复用设置同步处理方法
                }
            }

            ControlMessage.TYPE_REMOTE_CONNECTION_ADDED -> {
                forwardMessageToControlDialog(connection.id, message) { dialog ->
                    dialog.handleConnectionAdded(message)
                }
            }

            ControlMessage.TYPE_REMOTE_CONNECTION_UPDATED -> {
                forwardMessageToControlDialog(connection.id, message) { dialog ->
                    dialog.handleConnectionUpdated(message)
                }
            }

            ControlMessage.TYPE_REMOTE_CONNECTION_REMOVED -> {
                forwardMessageToControlDialog(connection.id, message) { dialog ->
                    dialog.handleConnectionRemoved(message)
                }
            }

            ControlMessage.TYPE_REMOTE_CONTROL_SERVICE_STOPPED -> {
                handleRemoteControlServiceStopped(connection, message)
            }

            ControlMessage.TYPE_DISCONNECT -> {
                handleDisconnectMessage(connection)
            }

            else -> {
                AppLog.d("未处理的远程消息类型: ${message.type}")
            }
        }
    }

    /**
     * 处理远程控制响应消息
     */
    private fun handleRemoteControlResponse(connection: RemoteSenderConnection, message: ControlMessage) {
        val success = message.getBooleanData("success") ?: false
        val responseMessage = message.getStringData("message") ?: ""

        // 这里需要在UI线程中处理，由调用方负责线程切换
        AppLog.d("远程控制响应: success=$success, message=$responseMessage, device=${connection.deviceName}")
    }

    /**
     * 处理远程被控服务停止消息
     */
    private fun handleRemoteControlServiceStopped(connection: RemoteSenderConnection, message: ControlMessage) {
        val reason = message.getStringData("reason") ?: "远程被控服务已停止"
        AppLog.d("遥控端收到远程被控服务停止通知: ${connection.deviceName}, 原因: $reason")

        // 更新连接状态为断开
        val updatedConnection = connection.withConnectionState(false)
        connectionManager.updateGlobalConnectionState(updatedConnection)

        // 从全局管理器中移除连接
        connectionManager.removeRemoteClient(connection.id)

        AppLog.d("已处理遥控端远程被控服务停止通知: ${connection.deviceName}")
    }

    /**
     * 处理断开连接消息
     */
    private fun handleDisconnectMessage(connection: RemoteSenderConnection) {
        AppLog.d("遥控端收到断开连接消息: ${connection.deviceName}")

        // 更新连接状态为断开
        val updatedConnection = connection.withConnectionState(false)
        connectionManager.updateGlobalConnectionState(updatedConnection)

        // 从全局管理器中移除连接
        connectionManager.removeRemoteClient(connection.id)

        AppLog.d("已处理遥控端断开连接: ${connection.deviceName}")
    }

    /**
     * 转发消息到对应的控制对话框
     */
    private fun forwardMessageToControlDialog(
        connectionId: String,
        message: ControlMessage,
        handler: (RemoteSenderControlDialog) -> Unit
    ) {
        val dialog = connectionManager.getActiveControlDialog(connectionId)
        if (dialog != null) {
            handler(dialog)
            AppLog.d("已转发消息到控制对话框: $connectionId, 消息类型: ${message.type}")
        } else {
            AppLog.w("未找到对应的控制对话框: $connectionId")
        }
    }

    /**
     * 🐾 在UI线程安全地显示Toast
     */
    private fun showToastOnUiThread(context: Context, message: String) {
        uiHandler.post {
            try {
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                AppLog.w("无法显示Toast，Context可能已销毁: $message", e)
            }
        }
    }
}
