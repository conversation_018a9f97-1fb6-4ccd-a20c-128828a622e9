package com.example.castapp.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.example.castapp.database.converter.DateConverter
import com.example.castapp.database.dao.WindowLayoutDao
import com.example.castapp.database.entity.WindowLayoutEntity
import com.example.castapp.database.entity.WindowLayoutItemEntity

/**
 * CastApp应用的Room数据库
 * 管理窗口布局相关的数据存储
 */
@Database(
    entities = [
        WindowLayoutEntity::class,
        WindowLayoutItemEntity::class
    ],
    version = 11, // 🎯 增加版本号以支持横屏开关字段
    exportSchema = false
)
@TypeConverters(DateConverter::class)
abstract class CastAppDatabase : RoomDatabase() {
    
    /**
     * 获取窗口布局数据访问对象
     */
    abstract fun windowLayoutDao(): WindowLayoutDao
    
    companion object {
        // 数据库名称
        private const val DATABASE_NAME = "cast_app_database"
        
        // 单例实例
        @Volatile
        private var INSTANCE: CastAppDatabase? = null
        
        /**
         * 获取数据库实例（单例模式）
         */
        fun getDatabase(context: Context): CastAppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    CastAppDatabase::class.java,
                    DATABASE_NAME
                )
                    .fallbackToDestructiveMigration() // 开发阶段使用，生产环境需要提供迁移策略
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
