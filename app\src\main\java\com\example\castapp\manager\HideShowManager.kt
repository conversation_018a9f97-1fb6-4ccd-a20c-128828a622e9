package com.example.castapp.manager

import android.content.Context
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.core.view.isGone
import androidx.core.view.isVisible
import com.example.castapp.ui.view.GestureOverlayView
import com.example.castapp.utils.AppLog
import java.lang.ref.WeakReference

/**
 * 清屏功能管理器
 * 负责处理功能按钮组的隐藏/显示和手势解锁功能
 */
class HideShowManager private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: HideShowManager? = null
        
        fun getInstance(): HideShowManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: HideShowManager().also { INSTANCE = it }
            }
        }
    }
    
    // 手势覆盖层缓存（使用WeakReference避免内存泄漏）
    private var gestureOverlayRef: WeakReference<GestureOverlayView>? = null

    // 恢复按钮组显示的回调
    private var onRestoreButtonGroupCallback: (() -> Unit)? = null
    
    /**
     * 初始化管理器 - 创建手势覆盖层并添加到主容器
     */
    fun initialize(
        context: Context,
        mainContainer: RelativeLayout,
        onRestoreButtonGroup: () -> Unit
    ) {
        // 设置回调
        this.onRestoreButtonGroupCallback = onRestoreButtonGroup

        // 初始化手势覆盖层
        initializeGestureOverlay(context, mainContainer)
        AppLog.d("【HideShowManager】初始化完成")
    }
    
    /**
     * 初始化手势覆盖层
     */
    private fun initializeGestureOverlay(context: Context, mainContainer: RelativeLayout) {
        val gestureOverlay = GestureOverlayView(context)

        // 设置覆盖层布局参数
        val layoutParams = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            RelativeLayout.LayoutParams.MATCH_PARENT
        )
        gestureOverlay.layoutParams = layoutParams
        gestureOverlay.isGone = true // 初始隐藏

        // 设置手势监听器
        gestureOverlay.setOnGestureListener(object : GestureOverlayView.OnGestureListener {
            override fun onCLetterDetected() {
                handleCLetterDetected()
            }
        })

        // 添加到主容器
        mainContainer.addView(gestureOverlay)

        // 使用WeakReference缓存
        gestureOverlayRef = WeakReference(gestureOverlay)

        AppLog.d("【HideShowManager】手势覆盖层初始化完成")
    }
    
    /**
     * 处理清屏操作 - 隐藏功能按钮组
     */
    fun handleClearScreen(buttonGroup: LinearLayout) {
        try {
            // 隐藏功能按钮组
            buttonGroup.isGone = true

            // 显示手势覆盖层
            gestureOverlayRef?.get()?.isVisible = true

            // 静默隐藏，不显示任何提示
            AppLog.d("【HideShowManager】清屏完成，按钮组已隐藏")

        } catch (e: Exception) {
            AppLog.e("【HideShowManager】处理清屏操作失败", e)
            // 保持静默，不显示错误提示
        }
    }
    
    /**
     * 处理检测到C字母手势 - 恢复显示功能按钮组
     * 需要通过回调方式通知外部恢复按钮组显示
     */
    private fun handleCLetterDetected() {
        try {
            val gestureOverlay = gestureOverlayRef?.get()
            if (gestureOverlay != null) {
                // 隐藏手势覆盖层
                gestureOverlay.isGone = true

                // 清除手势轨迹
                gestureOverlay.clearGesture()

                // 通过回调通知外部恢复按钮组
                onRestoreButtonGroupCallback?.invoke()

                AppLog.d("【HideShowManager】C字母检测成功，已通知恢复按钮组显示")
            }
        } catch (e: Exception) {
            AppLog.e("【HideShowManager】处理C字母检测失败", e)
            // 保持静默，不显示错误提示
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup(mainContainer: RelativeLayout) {
        try {
            // 移除手势覆盖层
            gestureOverlayRef?.get()?.let { overlay ->
                mainContainer.removeView(overlay)
            }

            // 清空引用
            gestureOverlayRef?.clear()
            gestureOverlayRef = null
            onRestoreButtonGroupCallback = null

            AppLog.d("【HideShowManager】资源清理完成")

        } catch (e: Exception) {
            AppLog.e("【HideShowManager】清理资源时发生异常", e)
        }
    }


}
