<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background"
    android:padding="12dp"
    android:elevation="8dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="12dp"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="📡 接收端设置"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary" />

        <ImageButton
            android:id="@+id/close_dialog_button"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="关闭窗口"
            android:scaleType="centerInside"
            android:alpha="0.8"/>
    </LinearLayout>

    <!-- 网络配置卡片 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/info_card_background"
        android:padding="12dp"
        android:layout_marginBottom="12dp">

        <!-- 标签行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="6dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:text="🌐 本机IP地址"
                android:textStyle="bold"
                android:textSize="13sp"
                android:textColor="@color/text_primary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.6"
                android:text="端口号"
                android:textStyle="bold"
                android:textSize="13sp"
                android:textColor="@color/text_primary"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.3"
                android:text="音视频服务"
                android:textStyle="bold"
                android:textSize="13sp"
                android:textColor="@color/text_primary"
                android:gravity="center" />

        </LinearLayout>

        <!-- 内容行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/ip_address_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:text="正在获取IP地址..."
                android:textIsSelectable="true"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:minHeight="36dp"
                android:gravity="center_vertical" />

            <EditText
                android:id="@+id/port_input"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="0.6"
                android:background="@drawable/edit_text_background"
                android:hint="端口"
                android:inputType="number"
                android:text="8888"
                android:textSize="14sp"
                android:gravity="center"
                android:paddingHorizontal="8dp"/>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1.3"
                android:gravity="center">

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/audio_video_server_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:thumbTint="@color/white"
                    android:trackTint="@color/primary_blue" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启动后将开启视音频UDP端口(0/+2/+3)、WS端口(+1)"
            android:textSize="11sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="4dp" />

    </LinearLayout>

    <!-- 播放模式设置卡片 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/info_card_background"
        android:padding="12dp"
        android:layout_marginBottom="12dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🔊 播放模式"
            android:textStyle="bold"
            android:textSize="13sp"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="8dp" />

        <RadioGroup
            android:id="@+id/audio_output_mode_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/speaker_mode_radio"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="🔊 扬声器"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:checked="true" />

            <RadioButton
                android:id="@+id/earpiece_mode_radio"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="📞 听筒"
                android:textSize="14sp"
                android:textColor="@color/text_secondary" />

        </RadioGroup>

    </LinearLayout>

    <!-- 音量控制卡片 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/info_card_background"
        android:padding="12dp"
        android:layout_marginBottom="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="🔊 音量"
                android:textStyle="bold"
                android:textSize="13sp"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/receiver_volume_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="80%"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:minWidth="40dp"
                android:gravity="end" />

        </LinearLayout>

        <SeekBar
            android:id="@+id/receiver_volume_seekbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:max="100"
            android:progress="80"
            android:progressTint="@color/primary_blue"
            android:thumbTint="@color/primary_blue" />

    </LinearLayout>

    <!-- 远程被控控制区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/info_card_background"
        android:padding="12dp"
        android:layout_marginBottom="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="🎮 远程被控"
                android:textStyle="bold"
                android:textSize="13sp"
                android:textColor="@color/text_primary" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/remote_control_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="false"
                android:thumbTint="@color/white"
                android:trackTint="@color/primary_blue" />

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启用后可通过端口7777接受远程控制连接"
            android:textSize="11sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="4dp" />

    </LinearLayout>



    <!-- 服务器状态 -->
    <TextView
        android:id="@+id/server_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/info_card_background"
        android:padding="8dp"
        android:text="✅ 服务器运行中，请在发送端连接此IP和端口"
        android:textAlignment="center"
        android:textColor="#4CAF50"
        android:textStyle="bold"
        android:textSize="14sp"
        android:gravity="center"
        android:visibility="gone" />

</LinearLayout>