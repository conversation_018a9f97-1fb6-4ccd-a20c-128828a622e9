package com.example.castapp.service

import android.app.*
import android.content.Intent
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.view.Surface
import androidx.core.content.edit
import com.example.castapp.manager.StateManager
import com.example.castapp.manager.AudioReceivingManager
import com.example.castapp.manager.VideoReceivingManager
import com.example.castapp.manager.MessageReceivingManager
import com.example.castapp.websocket.WebSocketServer
import com.example.castapp.websocket.ControlMessage
import com.example.castapp.utils.NotificationManager as AppNotificationManager
import com.example.castapp.utils.AppLog
import com.example.castapp.viewmodel.MainViewModel
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap

/**
 * 接收服务
 */
class ReceivingService : Service() {
    companion object {
        const val ACTION_START = "action_start"
        const val ACTION_STOP = "action_stop"


        const val ACTION_SET_AUDIO_OUTPUT_MODE = "action_set_audio_output_mode"
        const val ACTION_SET_RECEIVER_VOLUME = "action_set_receiver_volume"
        const val EXTRA_PORT = "port"
        const val EXTRA_SPEAKER_MODE = "speaker_mode"
        const val EXTRA_VOLUME = "volume"

        private const val WEBSOCKET_PORT_OFFSET = 1
        private const val MEDIA_AUDIO_PORT_OFFSET = 2
        private const val MIC_AUDIO_PORT_OFFSET = 3


        @Volatile
        private var serviceInstanceRef: WeakReference<ReceivingService>? = null

        private fun setInstance(service: ReceivingService) {
            serviceInstanceRef = WeakReference(service)
        }

        fun getInstance(): ReceivingService? {
            val instance = serviceInstanceRef?.get()
            if (instance == null && serviceInstanceRef != null) {
                serviceInstanceRef = null
            }
            return instance
        }

        private fun clearInstance() {
            serviceInstanceRef?.clear()
            serviceInstanceRef = null
        }

        fun setSurfaceForConnection(connectionId: String, surface: Surface?) {
            getInstance()?.videoReceivingManager?.setSurfaceForConnection(connectionId, surface)
        }

        fun clearDisconnectionFlag(connectionId: String) {
            getInstance()?.videoReceivingManager?.clearDisconnectionFlag(connectionId)
        }

        /**
         * 从外部立即通知所有连接断开（供ViewModel调用）
         */
        fun immediateNotifyDisconnectFromExternal() {
            getInstance()?.immediateNotifyAllConnectionsDisconnect()
        }

        /**
         * 从外部立即停止所有MediaCodec解码器（供ViewModel调用，避免Surface清理冲突）
         */
        fun immediateStopAllDecodersFromExternal() {
            getInstance()?.videoReceivingManager?.immediateStopAllDecoders()
        }

        /**
         * 🗑️ 断开特定WebSocket连接（供窗口删除功能调用）
         */
        fun disconnectSpecificConnection(connectionId: String) {
            getInstance()?.webSocketServer?.disconnectSpecificConnection(connectionId)
        }

        /**
         * 🚀 优雅停止特定连接的MediaCodec解码器（供窗口删除功能调用）
         */
        fun gracefulStopDecoderForConnection(connectionId: String) {
            getInstance()?.videoReceivingManager?.gracefulStopDecoder(connectionId)
        }

        /**
         * 🎯 获取WebSocket服务器实例（供外部调用）
         */
        fun getWebSocketServer(): WebSocketServer? {
            return getInstance()?.webSocketServer
        }

    }

    private var webSocketServer: WebSocketServer? = null
    private var outputSurface: Surface? = null

    // 音频接收管理器
    private var audioReceivingManager: AudioReceivingManager? = null

    // 视频接收管理器
    private var videoReceivingManager: VideoReceivingManager? = null

    // 消息接收管理器
    private var messageReceivingManager: MessageReceivingManager? = null



    private var isRunning = false

    // 已处理的断开连接ID集合，防止重复处理
    private val processedDisconnections = ConcurrentHashMap.newKeySet<String>()

    // 状态管理器实例
    private lateinit var stateManager: StateManager

    override fun onCreate() {
        super.onCreate()
        AppNotificationManager.createNotificationChannel(this, AppNotificationManager.NotificationType.RECEIVING)
        stateManager = StateManager.getInstance(application)
        setInstance(this)

        // 初始化音频接收管理器
        initializeAudioReceivingManager()

        // 初始化视频接收管理器
        initializeVideoReceivingManager()

        // 初始化消息接收管理器
        initializeMessageReceivingManager()
    }

    /**
     * 初始化音频接收管理器
     */
    private fun initializeAudioReceivingManager() {
        audioReceivingManager = AudioReceivingManager(
            context = this,
            stateManager = stateManager,
            callback = object : AudioReceivingManager.AudioReceivingCallback {
                override fun onAudioPlayerCreated(connectionId: String, audioType: String) {
                    AppLog.service("音频播放器创建成功: $connectionId, 类型: $audioType")
                }

                override fun onAudioPlayerStopped(connectionId: String, audioType: String) {
                    AppLog.service("音频播放器已停止: $connectionId, 类型: $audioType")
                }

                override fun onAudioError(connectionId: String, audioType: String, error: String) {
                    AppLog.e("音频错误: $connectionId, 类型: $audioType, 错误: $error")
                }

                override fun onAudioStatistics(audioType: String, packetsProcessed: Int, bytesProcessed: Long, activeConnections: Int) {
                    AppLog.service("${audioType}处理统计: 处理包数=${packetsProcessed}个, 数据量=${bytesProcessed}字节, 活跃连接=${activeConnections}个")
                }
            }
        )

        // 加载保存的播放模式设置
        audioReceivingManager?.loadAudioOutputMode()
    }

    /**
     * 初始化视频接收管理器
     */
    private fun initializeVideoReceivingManager() {
        videoReceivingManager = VideoReceivingManager(
            stateManager = stateManager,
            callback = object : VideoReceivingManager.VideoReceivingCallback {
                override fun onNewVideoConnection(connectionId: String) {
                    AppLog.service("视频连接创建: $connectionId")

                    // 延迟一点时间，确保设备信息已经保存
                    Handler(Looper.getMainLooper()).postDelayed({
                        // 使用安全的WeakReference通知MainViewModel有新连接
                        MainViewModel.getInstance()?.handleNewConnection(connectionId)
                        AppLog.service("新连接事件: $connectionId")
                    }, 100) // 延迟100ms
                }

                override fun onVideoConnectionDisconnected(connectionId: String) {
                    AppLog.service("视频连接断开: $connectionId")
                    handleUIDisconnectNotification(connectionId)
                }

                override fun onVideoWindowRemoved(connectionId: String) {
                    AppLog.service("视频窗口移除: $connectionId")
                    // 只移除UI窗口，不清理音频组件，不断开连接
                    MainViewModel.getInstance()?.handleConnectionDisconnected(connectionId)
                    AppLog.service("【视频窗口移除】已通知MainViewModel移除窗口，保持音频连接: $connectionId")
                }

                override fun onScreenResolution(connectionId: String, width: Int, height: Int) {
                    AppLog.service("屏幕分辨率信息: $connectionId, ${width}x${height}")
                    // 使用安全的WeakReference通知MainViewModel调整投屏窗口尺寸
                    MainViewModel.getInstance()?.handleScreenResolution(connectionId, width, height)
                }

                override fun onVideoError(connectionId: String, error: String) {
                    AppLog.e("视频错误: $connectionId, 错误: $error")
                }

                override fun onResolutionAdjustmentComplete(connectionId: String, width: Int, height: Int, success: Boolean, error: String?) {
                    AppLog.service("分辨率调整完成: $connectionId, ${width}x${height}, 成功: $success, 错误: $error")
                }

                override fun onVideoOrientationChanged(connectionId: String, orientation: Int, videoWidth: Int, videoHeight: Int) {
                    AppLog.service("🎯 视频方向变化: $connectionId, 方向: $orientation, 分辨率: ${videoWidth}×${videoHeight}")
                    // 通知MainViewModel处理方向变化
                    MainViewModel.getInstance()?.handleVideoOrientationChanged(connectionId, orientation, videoWidth, videoHeight)
                }
            }
        )
    }

    /**
     * 初始化消息接收管理器
     */
    private fun initializeMessageReceivingManager() {
        messageReceivingManager = MessageReceivingManager(
            audioReceivingManager = audioReceivingManager!!,
            videoReceivingManager = videoReceivingManager!!,
            stateManager = stateManager,
            callback = object : MessageReceivingManager.MessageReceivingCallback {
                override fun onNewConnection(connectionId: String) {
                    // 延迟一点时间，确保设备信息已经保存
                    Handler(Looper.getMainLooper()).postDelayed({
                        // 使用安全的WeakReference通知MainViewModel有新连接
                        MainViewModel.getInstance()?.handleNewConnection(connectionId)
                        AppLog.service("新连接事件: $connectionId")
                    }, 100) // 延迟100ms
                }

                override fun onConnectionDisconnected(connectionId: String) {
                    handleUIDisconnectNotification(connectionId)
                }

                override fun onVideoWindowRemoved(connectionId: String) {
                    // 只移除UI窗口，不清理音频组件，不断开连接
                    MainViewModel.getInstance()?.handleConnectionDisconnected(connectionId)
                    AppLog.service("【视频窗口移除】已通知MainViewModel移除窗口，保持音频连接: $connectionId")
                }

                override fun onScreenResolution(connectionId: String, width: Int, height: Int) {
                    // 使用安全的WeakReference通知MainViewModel调整投屏窗口尺寸
                    MainViewModel.getInstance()?.handleScreenResolution(connectionId, width, height)
                }

                override fun markAsRemoteControlConnection(connectionId: String) {
                    // 遥控连接标记功能已迁移到RemoteReceiverService
                    AppLog.service("【连接类型】遥控连接标记功能已迁移: $connectionId")
                }

                override fun isRemoteControlConnection(connectionId: String): Boolean {
                    // 遥控连接检查功能已迁移到RemoteReceiverService
                    return false
                }

                override fun sendScreenResolutionToRemoteController(connectionId: String) {
                    // 屏幕分辨率发送功能已迁移到RemoteReceiverService
                    AppLog.service("【屏幕分辨率】功能已迁移到RemoteReceiverService: $connectionId")
                }

                override fun addProcessedDisconnection(connectionId: String): Boolean {
                    synchronized(processedDisconnections) {
                        if (processedDisconnections.contains(connectionId)) {
                            return false
                        }
                        processedDisconnections.add(connectionId)
                        return true
                    }
                }

                override fun getWebSocketServer(): WebSocketServer? {
                    return webSocketServer
                }
            }
        )
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START -> {
                val port = intent.getIntExtra(EXTRA_PORT, 0)
                if (port > 0) {
                    startReceiving(port)
                }
            }
            ACTION_STOP -> {
                stopReceiving()
            }


            ACTION_SET_AUDIO_OUTPUT_MODE -> {
                val speakerMode = intent.getBooleanExtra(EXTRA_SPEAKER_MODE, true)
                audioReceivingManager?.setAudioOutputMode(speakerMode)

                // 🔧 修复：同时更新SharedPreferences，确保状态持久化
                val sharedPrefs = getSharedPreferences("receiver_settings", MODE_PRIVATE)
                sharedPrefs.edit {
                    putBoolean("audio_output_speaker_mode", speakerMode)
                }

                AppLog.service("【音频输出模式】已更新: ${if (speakerMode) "扬声器" else "听筒"}，SharedPreferences已同步")
            }
            ACTION_SET_RECEIVER_VOLUME -> {
                val volume = intent.getIntExtra(EXTRA_VOLUME, 80)
                audioReceivingManager?.setReceiverVolume(volume)
            }

            // 🔄 新增：发送消息到所有遥控端
            "SEND_MESSAGE_TO_REMOTE_CONTROLLERS" -> {
                val messageJson = intent.getStringExtra("message_json")
                if (messageJson != null) {
                    sendMessageToAllRemoteControllers(messageJson)
                }
            }
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    /**
     * 开始接收
     */
    private fun startReceiving(port: Int) {
        if (isRunning) {
            AppLog.service("接收服务已在运行")
            return
        }

        // 适度的系统级性能优化，避免过度发热
        try {
            // 设置适中的进程优先级
            android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_DEFAULT)

            AppLog.service("已应用适度的系统级优化")
        } catch (e: Exception) {
            AppLog.w("系统级优化失败: ${e.message}")
        }

        try {
            // 启动前台服务
            val stopPendingIntent = AppNotificationManager.createServiceStopPendingIntent(
                this,
                ReceivingService::class.java,
                ACTION_STOP
            )
            val notification = AppNotificationManager.createReceivingNotification(
                this,
                "正在监听端口 $port",
                stopPendingIntent
            )
            startForeground(AppNotificationManager.NotificationType.RECEIVING.notificationId, notification)

            val webSocketPort = port + WEBSOCKET_PORT_OFFSET
            webSocketServer = WebSocketServer(
                port = webSocketPort,
                onMessageReceived = { controlMessage ->
                    messageReceivingManager?.handleMessage(controlMessage)
                },
                onConnectionRequest = { connectionId, clientIP, clientPort, deviceName ->
                    handleConnectionRequest(connectionId, clientIP, clientPort, deviceName)
                }
            )

            webSocketServer!!.start()

            // 启动视频接收服务
            if (!videoReceivingManager!!.startVideoReceiving(port, webSocketServer)) {
                throw Exception("启动视频接收服务失败")
            }

            // 启动音频接收器
            if (!audioReceivingManager!!.startAudioReceivers(port)) {
                throw Exception("启动音频接收器失败")
            }

            isRunning = true

            // 🔧 修复：通知StateManager服务状态已更新
            stateManager.updateServiceState("receiving", true, port)

            AppLog.service("接收服务启动成功: UDP端口$port, WebSocket端口$webSocketPort, 音频端口${port+MEDIA_AUDIO_PORT_OFFSET},${port+MIC_AUDIO_PORT_OFFSET} (固定端口7777WebSocket服务器需单独启用)")

        } catch (e: Exception) {
            AppLog.e("启动接收服务失败", e)
            stopReceiving()
        }
    }

    /**
     * 停止接收
     */
    private fun stopReceiving() {
        isRunning = false

        // 🚀 立即UI响应优化：移除此处的UI通知，改为在用户点击时立即响应
        // 原来的 notifyUIRemoveAllConnections() 已移至 ReceiverViewModel.stopServer()
        AppLog.service("【立即响应优化】跳过服务端UI通知，UI已在用户点击时立即响应")

        // 停止视频接收服务
        videoReceivingManager?.stopVideoReceiving()

        // 停止音频接收器
        audioReceivingManager?.stopAudioReceivers()

        // 停止WebSocket服务器（会自动通知所有发送端断开连接）
        webSocketServer?.stopServer()
        webSocketServer = null

        // 注意：固定端口WebSocket服务器现在独立管理，不在这里停止

        // 安全释放输出Surface
        try {
            outputSurface?.let { surface ->
                if (surface.isValid) {
                    surface.release()
                    AppLog.service("输出Surface已释放")
                } else {
                    AppLog.service("输出Surface已无效")
                }
            }
        } catch (e: Exception) {
            AppLog.w("释放输出Surface时发生异常", e)
        } finally {
            outputSurface = null
        }

        // 清理已处理的断开连接集合
        synchronized(processedDisconnections) {
            processedDisconnections.clear()
        }

        // 连接映射已移除，无需清理

        // 清理所有音频组件（通过AudioReceivingManager）
        audioReceivingManager?.cleanupAllAudioComponents()

        // 更新接收服务状态
        stateManager.updateServiceState("receiving", false)

        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()

        AppLog.service("接收服务已停止")
    }









    /**
     * 立即通知所有连接断开（供外部调用，在用户点击停止时立即执行）
     */
    fun immediateNotifyAllConnectionsDisconnect() {
        try {
            AppLog.service("【立即断开】开始立即通知所有连接断开")

            // 通知主WebSocket服务器的所有连接
            webSocketServer?.let { server ->
                val activeConnections = server.getActiveConnectionIds()
                AppLog.service("【立即断开】主WebSocket服务器有 ${activeConnections.size} 个活跃连接")

                activeConnections.forEach { connectionId ->
                    try {
                        val disconnectMessage = ControlMessage.createDisconnect(connectionId)
                        server.sendMessageToClient(connectionId, disconnectMessage)
                        AppLog.service("【立即断开】已向连接 $connectionId 发送断开通知")

                        // 🚀 修复多连接问题：只发送断开消息，不调用本地处理
                        // 让发送端收到消息后自然断开，避免防重复处理机制冲突
                        // handleWebSocketMessage(disconnectMessage) // 已移除
                        AppLog.service("【立即断开】等待发送端 $connectionId 确认断开")
                    } catch (e: Exception) {
                        AppLog.e("【立即断开】通知连接 $connectionId 断开失败", e)
                    }
                }

                // 标记断开通知已发送，避免在stopServer时重复发送
                server.markDisconnectNotificationSent()
            }

            // 🚀 架构修复：主服务器停止时不影响7777端口服务器
            // 7777端口服务器有独立的生命周期，由RemoteReceiverService单独管理
            AppLog.service("【架构分离】主服务器停止，7777端口服务器保持独立运行")

            AppLog.service("【立即断开】所有连接的断开通知已发送完成")
        } catch (e: Exception) {
            AppLog.e("【立即断开】立即通知所有连接断开失败", e)
        }
    }

    /**
     * 处理UI断开通知
     */
    private fun handleUIDisconnectNotification(connectionId: String) {
        AppLog.service("【UI断开通知】开始处理: $connectionId")

        try {
            // 使用统一的断开连接处理
            if (::stateManager.isInitialized) {
                stateManager.handleConnectionDisconnected(connectionId, "UI断开通知")
            }

            // 使用安全的WeakReference通知MainViewModel连接断开
            val viewModel = MainViewModel.getInstance()
            if (viewModel != null) {
                viewModel.handleConnectionDisconnected(connectionId)
                AppLog.service("【UI断开通知】已通知MainViewModel移除窗口: $connectionId")
            } else {
                AppLog.w("【UI断开通知】MainViewModel实例为null，无法通知窗口移除: $connectionId")
            }

            // 清理本地音频状态
            audioReceivingManager?.cleanupAudioComponents(connectionId)

        } catch (e: Exception) {
            AppLog.e("【UI断开通知】处理UI断开通知时发生异常: $connectionId", e)
        }
    }

    /**
     * 处理连接请求，自动添加发送端连接信息到StateManager
     */
    private fun handleConnectionRequest(connectionId: String, clientIP: String, clientPort: Int, deviceName: String?) {
        try {
            AppLog.service("【连接请求】处理连接请求: $connectionId")
            AppLog.service("【连接请求】发送端WebSocket地址: $clientIP:$clientPort")
            AppLog.service("【连接请求】设备名称: $deviceName")

            // 统一保存设备名称（无论连接是否存在）
            deviceName?.let {
                stateManager.updateConnectionDeviceName(connectionId, it)
                AppLog.service("【连接请求】已保存设备名称: $connectionId -> $it")

                // 统一在这里通知UI刷新，避免重复通知
                MainViewModel.getInstance()?.notifyDeviceInfoUpdated(connectionId)
            }

            // 🚀 屏幕分辨率发送功能已迁移到RemoteReceiverService
            AppLog.service("【屏幕分辨率】功能已迁移到RemoteReceiverService: $connectionId")

            // 检查连接是否存在，如果存在则更新端口信息
            val connection = stateManager.findConnectionById(connectionId)
            if (connection != null) {
                AppLog.service("【连接请求】连接已存在: ${connection.getDisplayText()}")

                // 检查端口是否需要更新
                if (connection.port != clientPort) {
                    AppLog.service("【连接请求】更新连接端口: ${connection.port} -> $clientPort")
                    val updatedConnection = connection.copy(
                        port = clientPort,
                        lastUpdateTime = System.currentTimeMillis()
                    )
                    stateManager.updateExistingConnection(updatedConnection)
                    AppLog.service("【连接请求】连接端口已更新: ${updatedConnection.getDisplayText()}")
                } else {
                    AppLog.service("【连接请求】连接端口无需更新: $clientPort")
                }
            } else {
                AppLog.service("【连接请求】连接尚未创建，将在消息处理中创建")
            }

        } catch (e: Exception) {
            AppLog.e("【连接请求】处理失败: $connectionId", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        AppLog.service("【Service生命周期】onDestroy被调用")
        clearInstance()

        // 🚀 架构分离：onDestroy时只清理主服务器资源
        // 7777端口服务器完全独立，不受主服务器生命周期影响
        try {
            // 只清理主WebSocket服务器（8889端口）
            webSocketServer?.let {
                AppLog.service("【Service生命周期】执行主WebSocket服务器安全停止")
                it.stopServer()
                webSocketServer = null
            }

            // 🚀 重要：7777端口服务器保持独立，由RemoteReceiverService管理
            AppLog.service("【架构分离】7777端口服务器保持独立运行，不受主服务器影响")

            AppLog.service("【Service生命周期】主服务器资源清理完成")
        } catch (e: Exception) {
            AppLog.e("【Service生命周期】资源清理时发生异常", e)
        }
    }

    /**
     * 🔄 发送消息到所有连接的遥控端
     */
    private fun sendMessageToAllRemoteControllers(messageJson: String) {
        try {
            AppLog.service("【双向同步】发送反向同步消息到所有遥控端")

            // 🔧 修复：使用新的独立远程控制服务器
            val remoteControlService = RemoteReceiverService.getInstance()
            val remoteControlServer = remoteControlService?.getRemoteControlServer()

            remoteControlServer?.let { server ->
                val message = ControlMessage.fromJson(messageJson)
                if (message != null) {
                    // 发送到所有连接的客户端
                    server.broadcastMessage(message)
                    AppLog.service("【双向同步】反向同步消息已通过独立远程控制服务器广播: ${message.type}")
                } else {
                    AppLog.e("【双向同步】消息解析失败: $messageJson")
                }
            } ?: run {
                AppLog.w("【双向同步】独立远程控制服务器未运行，无法发送反向同步消息")
            }

        } catch (e: Exception) {
            AppLog.e("【双向同步】发送反向同步消息失败", e)
        }
    }
}
