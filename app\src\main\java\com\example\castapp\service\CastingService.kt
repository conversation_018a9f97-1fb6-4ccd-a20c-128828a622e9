package com.example.castapp.service

import android.app.*
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.content.edit
import com.example.castapp.manager.MediaProjectionManager
import com.example.castapp.manager.ResolutionManager
import com.example.castapp.codec.VideoEncoder
import com.example.castapp.manager.StateManager
import com.example.castapp.manager.WebSocketManager
import com.example.castapp.rtp.RtpSender
import com.example.castapp.websocket.WebSocketClient
import com.example.castapp.websocket.ControlMessage
import com.example.castapp.utils.NotificationManager as AppNotificationManager
import com.example.castapp.utils.MemoryMonitor
import com.example.castapp.utils.PeriodicCleanupTask
import java.util.concurrent.ConcurrentHashMap
import com.example.castapp.utils.AppLog
import android.content.res.Configuration


/**
 * 投屏服务
 */
class CastingService : Service() {
    companion object {
        const val ACTION_START_CASTING = "action_start_casting"
        const val ACTION_STOP_CASTING = "action_stop_casting"
        const val ACTION_UPDATE_BITRATE = "action_update_bitrate"
        const val ACTION_UPDATE_RESOLUTION = "action_update_resolution"

        const val EXTRA_TARGET_IP = "target_ip"
        const val ACTION_STOP_DISPLAY_AUDIO = "STOP_DISPLAY_AUDIO"
        const val EXTRA_TARGET_PORT = "target_port"
        const val EXTRA_CONNECTION_ID = "connection_id"
        const val EXTRA_RESULT_CODE = "result_code"
        const val EXTRA_RESULT_DATA = "result_data"
        const val EXTRA_BITRATE = "bitrate"
        const val EXTRA_RESOLUTION_SCALE = "resolution_scale"
        const val EXTRA_RETRY_COUNT = "retry_count"
    }

    // 共享的视频源组件
    private var mediaProjectionManager: MediaProjectionManager? = null
    private var videoEncoder: VideoEncoder? = null

    // 多个RTP发送器，按连接ID管理（简化版）
    private val rtpSenders = ConcurrentHashMap<String, RtpSender>()

    // WebSocket客户端，按连接ID管理（简化版）
    private val webSocketClients = ConcurrentHashMap<String, WebSocketClient>()

    // 注意：音频流管理器已移除，音频功能现在由独立的AudioStreamingService处理

    // 视频源是否正在运行
    private var isVideoSourceRunning = false

    // 分辨率调整锁机制
    private var isResolutionAdjusting = false
    private val resolutionAdjustLock = Any()

    // 状态管理器实例
    private lateinit var stateManager: StateManager

    // 统一ID架构：不再需要ID转换，直接使用connectionId

    // 内存监控器
    private var memoryMonitor: MemoryMonitor? = null

    // 定期清理任务
    private var periodicCleanupTask: PeriodicCleanupTask? = null

    // 统一WebSocket管理器实例
    private val webSocketManager = WebSocketManager.getInstance()

    // 🎯 横竖屏适配：分离系统方向和视频源方向状态
    private var systemOrientation = Configuration.ORIENTATION_PORTRAIT // 系统屏幕方向
    private var videoSourceOrientation = Configuration.ORIENTATION_PORTRAIT // 当前视频源的实际方向

    // 🎯 横屏模式控制：各连接的横屏检测开关状态
    private val landscapeModeEnabled = ConcurrentHashMap<String, Boolean>()

    override fun onCreate() {
        super.onCreate()
        // 使用统一的通知管理器创建通知渠道
        AppNotificationManager.createNotificationChannel(this, AppNotificationManager.NotificationType.CASTING)
        stateManager = StateManager.getInstance(application)

        // 🎯 新增：设置WebSocketManager的ApplicationContext
        WebSocketManager.setApplicationContext(this)

        // 初始化内存监控器
        memoryMonitor = MemoryMonitor.getInstance().apply {
            addListener(object : MemoryMonitor.MemoryMonitorListener {
                override fun onMemoryWarning(snapshot: MemoryMonitor.MemorySnapshot) {
                    AppLog.w("投屏服务内存警告: ${snapshot.usedMemoryMB}MB/${snapshot.maxMemoryMB}MB")
                }

                override fun onMemoryCritical(snapshot: MemoryMonitor.MemorySnapshot) {
                    AppLog.e("投屏服务内存危险: ${snapshot.usedMemoryMB}MB/${snapshot.maxMemoryMB}MB")
                    // 在内存危险时强制GC
                    forceGarbageCollection()
                }

                override fun onMemoryLeakSuspected(growthMB: Long, consecutiveGrowths: Long) {
                    AppLog.e("疑似内存泄漏: 增长${growthMB}MB, 连续增长${consecutiveGrowths}次")
                }

                override fun onMemoryStable(snapshot: MemoryMonitor.MemorySnapshot) {
                    AppLog.service("内存使用稳定: ${snapshot.usedMemoryMB}MB")
                }
            })
            startMonitoring()
        }

        // 初始化定期清理任务
        periodicCleanupTask = PeriodicCleanupTask.getInstance().apply {
            // 注册投屏服务的清理任务
            registerCleanupTask("casting_service", object : PeriodicCleanupTask.CleanupTask {
                override fun cleanup() {
                    // 清理断开的连接
                    cleanupDisconnectedConnections()
                }

                override fun getTaskName(): String = "投屏服务常规清理"
            })

            registerDeepCleanupTask("casting_service_deep", object : PeriodicCleanupTask.DeepCleanupTask {
                override fun deepCleanup() {
                    // 深度清理：检查所有连接状态，清理僵尸连接
                    deepCleanupConnections()
                }

                override fun getTaskName(): String = "投屏服务深度清理"
            })

            startPeriodicCleanup()
        }

        // 🎯 横竖屏适配：初始化当前方向状态
        initCurrentOrientation()

        // 🎯 横屏模式控制：每次启动都从默认关闭状态开始，不恢复之前的设置

        // 初始化音频流管理器（延迟初始化，因为需要mediaProjectionManager）
        AppLog.service("投屏服务创建")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_CASTING -> {
                val targetIp = intent.getStringExtra(EXTRA_TARGET_IP) ?: return START_NOT_STICKY
                val targetPort = intent.getIntExtra(EXTRA_TARGET_PORT, 0)
                val connectionId = intent.getStringExtra(EXTRA_CONNECTION_ID) ?: return START_NOT_STICKY
                val resultData = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    intent.getParcelableExtra(EXTRA_RESULT_DATA, Intent::class.java)
                } else {
                    @Suppress("DEPRECATION")
                    intent.getParcelableExtra(EXTRA_RESULT_DATA)
                }

                if (resultData != null) {
                    startCasting(targetIp, targetPort, connectionId, resultData)
                }
            }
            ACTION_STOP_CASTING -> {
                val connectionId = intent.getStringExtra(EXTRA_CONNECTION_ID)
                stopCasting(connectionId) // 如果connectionId为null，则停止所有连接
            }
            ACTION_UPDATE_BITRATE -> {
                val newBitRate = intent.getIntExtra(EXTRA_BITRATE, 0)
                if (newBitRate > 0) {
                    AppLog.service("收到码率调整Intent: ${newBitRate / 1_000_000}Mbps")
                    updateBitRate(newBitRate)
                } else {
                    AppLog.w("收到无效的码率调整请求: $newBitRate")
                }
            }
            ACTION_UPDATE_RESOLUTION -> {
                val newResolutionScale = intent.getIntExtra(EXTRA_RESOLUTION_SCALE, 0)
                val retryCount = intent.getIntExtra(EXTRA_RETRY_COUNT, 0)
                if (newResolutionScale > 0) {
                    AppLog.service("收到分辨率调整Intent: $newResolutionScale% (重试次数: $retryCount)")
                    updateResolution(newResolutionScale, retryCount)
                } else {
                    AppLog.w("收到无效的分辨率调整请求: $newResolutionScale")
                }
            }
            ACTION_STOP_DISPLAY_AUDIO -> {
                // 音频功能已移至独立的AudioStreamingService
                AppLog.service("音频功能已移至独立的AudioStreamingService，此服务不再处理音频Intent")
            }
            else -> {
                // 尝试让音频流管理器处理音频相关的Intent
                if (intent != null) {
                    handleAudioIntent()
                }
            }
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    /**
     * 🎯 横竖屏适配：监听配置变化（根源性修复版）
     */
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)

        AppLog.service("🎯 配置变化回调: 新系统方向=${getOrientationName(newConfig.orientation)}, 当前系统方向=${getOrientationName(systemOrientation)}, 当前视频源方向=${getOrientationName(videoSourceOrientation)}")

        // 始终更新系统方向状态
        systemOrientation = newConfig.orientation

        // 🎯 最终修复：当横屏模式启用时，允许自动处理系统方向变化
        if (isVideoSourceRunning && newConfig.orientation != videoSourceOrientation && hasLandscapeModeEnabled()) {
            AppLog.service("🎯 检测到系统方向变化且横屏模式已启用: 视频源方向${getOrientationName(videoSourceOrientation)} -> ${getOrientationName(newConfig.orientation)}")
            videoSourceOrientation = newConfig.orientation
            handleOrientationChange(newConfig.orientation)
        } else if (isVideoSourceRunning && newConfig.orientation != videoSourceOrientation) {
            AppLog.service("🎯 系统方向变化但横屏模式未启用，视频源方向保持: ${getOrientationName(videoSourceOrientation)}")
        }
    }

    private fun handleAudioIntent() {
        AppLog.service("音频功能已移至独立的AudioStreamingService")
    }

    /**
     * 开始投屏
     */
    private fun startCasting(targetIp: String, targetPort: Int, connectionId: String, resultData: Intent) {
        try {
            AppLog.service("开始启动投屏到: $targetIp:$targetPort, 连接ID: $connectionId")

            if (rtpSenders.containsKey(connectionId)) {
                AppLog.w("连接 $connectionId 已存在，跳过重复启动")
                return
            }

            val connection = stateManager.findConnectionById(connectionId)
                ?: throw Exception("未找到连接信息: $connectionId")

            val ssrc = connection.getSSRC()
            val rtpSender = RtpSender(targetIp, targetPort, ssrc)
            if (!rtpSender.start()) {
                throw Exception("启动RTP发送器失败: 无法绑定到 $targetIp:$targetPort")
            }
            rtpSenders[connectionId] = rtpSender

            webSocketManager.setMessageListener(connection.connectionId) { controlMessage ->
                handleWebSocketMessage(connectionId, controlMessage)
            }

            webSocketManager.setConnectionStateListener(connection.connectionId) { isConnected ->
                AppLog.service("WebSocket连接状态变化: $connectionId, 连接: $isConnected")
            }

            val webSocketClient = webSocketManager.getWebSocketClient(connection.connectionId)
            if (webSocketClient != null) {
                webSocketClients[connectionId] = webSocketClient
                sendBasicConnectionInfo(connectionId)
            } else {
                throw Exception("统一WebSocket管理器中未找到连接: ${connection.connectionId}")
            }

            if (!isVideoSourceRunning) {
                startVideoSource(resultData)
            } else {
                sendInitializationDataToConnection(connectionId)
            }

            updateNotification()
            stateManager.updateConnection(connection.connectionId) { conn ->
                conn.withCasting(true)
            }

        } catch (e: Exception) {
            AppLog.e("启动投屏失败: ${e.message}", e)
            rtpSenders.remove(connectionId)?.stop()
            webSocketClients.remove(connectionId)?.disconnect()
            if (rtpSenders.isEmpty()) {
                stopVideoSource()
            }
            stateManager.handleConnectionDisconnected(connectionId, "投屏启动失败: ${e.message}")
        }
    }

    private fun startVideoSource(resultData: Intent) {
        // 🚀 修复投屏时音频卡顿：不设置低优先级，避免影响音频线程
        // 注释掉原来的低优先级设置，让投屏服务使用默认优先级
        // try {
        //     android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_BACKGROUND + 1)
        // } catch (e: Exception) {
        //     AppLog.w("设置进程优先级失败: ${e.message}")
        // }
        AppLog.service("投屏服务使用默认优先级，避免影响音频处理")

        val notification = AppNotificationManager.createCastingNotification(this, "正在投屏")
        startForeground(AppNotificationManager.NotificationType.CASTING.notificationId, notification)

        mediaProjectionManager = MediaProjectionManager.getInstance(this)

        val resolutionManager = ResolutionManager.getInstance(this)
        val currentScale = resolutionManager.getCurrentResolutionScale()
        val outputSize = resolutionManager.calculateCaptureResolution(currentScale)

        val sharedPrefs = getSharedPreferences("cast_settings", MODE_PRIVATE)
        val bitrateMbps = sharedPrefs.getInt("bitrate_mbps", 20)
        val bitrateInBps = bitrateMbps * 1_000_000

        var lastLogTime = 0L
        var frameCount = 0L

        videoEncoder = VideoEncoder(
            width = outputSize.first,   // 使用当前设定的输出分辨率
            height = outputSize.second, // 使用当前设定的输出分辨率
            bitRate = bitrateInBps,
            onEncodedData = { buffer, size ->
                frameCount++
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastLogTime > 120000) {
                    AppLog.service("投屏统计: 发送器数量=${rtpSenders.size}, 帧计数=$frameCount")
                    lastLogTime = currentTime
                }
                rtpSenders.forEach { (connectionId, sender) ->
                    try {
                        sender.sendH264Data(buffer, size)
                    } catch (e: Exception) {
                        AppLog.e("发送数据到连接 $connectionId 失败", e)
                    }
                }
            },
            onConfigurationData = { spsData, ppsData ->
                // 🚀 兼容性回调：使用旧方法
                webSocketClients.values.forEach { client ->
                    client.sendH264Config(spsData, ppsData)
                }
            },
            onConfigurationDataWithResolution = { spsData, ppsData, width, height ->
                // 🚀 新增：使用包含分辨率信息的方法
                webSocketClients.values.forEach { client ->
                    client.sendH264ConfigWithResolution(spsData, ppsData, width, height)
                }
            },
            onConfigurationDataWithOrientation = { spsData, ppsData, width, height, orientation ->
                // 🎯 横竖屏适配：使用包含分辨率和方向信息的方法
                webSocketClients.values.forEach { client ->
                    client.sendH264ConfigWithResolutionAndOrientation(spsData, ppsData, width, height, orientation)
                }
            }
        )

        val inputSurface = videoEncoder?.start()
            ?: throw Exception("启动视频编码器失败: 无法创建输入Surface")

        // 🎯 横竖屏适配：设置VideoEncoder的当前视频源方向
        videoEncoder?.setOrientation(videoSourceOrientation)

        val (encoderName, encoderType) = videoEncoder?.getEncoderInfo() ?: ("未知" to "未知")
        val isHardwareAccelerated = videoEncoder?.isUsingHardwareAcceleration() ?: false

        AppLog.service("视频编码器启动: $encoderName ($encoderType), 硬件加速: $isHardwareAccelerated")
        AppLog.service("分辨率: ${outputSize.first}x${outputSize.second}@30fps, 码率: ${bitrateMbps}Mbps")
        AppLog.service("🎯 当前视频源方向: ${getOrientationName(videoSourceOrientation)}")

        val captureSuccess = mediaProjectionManager?.startScreenCapture(resultData, inputSurface) ?: false
        if (!captureSuccess) {
            throw Exception("启动屏幕捕获失败: MediaProjection权限可能无效")
        }

        isVideoSourceRunning = true

        // 🎯 横竖屏适配：投屏开始时更新当前方向状态
        updateCurrentOrientationState()

        sendInitializationDataToAllConnections()
    }

    private fun stopVideoSource() {
        isVideoSourceRunning = false
        mediaProjectionManager?.stopScreenCapture()
        mediaProjectionManager = null
        videoEncoder?.stop()
        videoEncoder = null
    }

    private fun sendBasicConnectionInfo(connectionId: String) {
        try {
            webSocketClients[connectionId] ?: return
            sendScreenResolutionToConnectionViaWebSocket(connectionId)
        } catch (e: Exception) {
            AppLog.e("向连接 $connectionId 发送基本连接信息失败", e)
        }
    }

    private fun sendInitializationDataToAllConnections() {
        Thread {
            try {
                Thread.sleep(100)
                webSocketClients.forEach { (connectionId, _) ->
                    sendSensitiveDataToConnectionInternal(connectionId)
                }
            } catch (e: Exception) {
                AppLog.e("发送敏感数据失败", e)
            }
        }.start()
    }

    private fun sendInitializationDataToConnection(connectionId: String) {
        Thread {
            try {
                Thread.sleep(100)
                sendSensitiveDataToConnectionInternal(connectionId)
            } catch (e: Exception) {
                AppLog.e("向连接 $connectionId 发送敏感数据失败", e)
            }
        }.start()
    }

    private fun sendSensitiveDataToConnectionInternal(connectionId: String) {
        try {
            val webSocketClient = webSocketClients[connectionId] ?: return

            sendScreenResolutionToConnectionViaWebSocket(connectionId)

            val ssrc = rtpSenders[connectionId]?.let {
                connectionId.hashCode().toLong() and 0xFFFFFFFFL
            } ?: 0L

            webSocketClient.sendSsrcMapping(ssrc)
            webSocketClient.sendCastingState(true, "投屏开始")
            sendH264ConfigWithRetry(connectionId, webSocketClient, 0)

        } catch (e: Exception) {
            AppLog.e("向连接 $connectionId 发送敏感数据失败", e)
        }
    }

    private fun sendH264ConfigWithRetry(connectionId: String, webSocketClient: WebSocketClient, retryCount: Int) {
        try {
            // 🚀 优先使用包含分辨率信息的新方法
            videoEncoder?.sendCachedConfigurationDataViaWebSocketWithResolution { spsData, ppsData, width, height ->
                if (spsData != null && ppsData != null) {
                    webSocketClient.sendH264ConfigWithResolution(spsData, ppsData, width, height)
                } else if (retryCount < 5) {
                    Thread {
                        Thread.sleep(500 * (retryCount + 1).toLong())
                        sendH264ConfigWithRetry(connectionId, webSocketClient, retryCount + 1)
                    }.start()
                }
            }
        } catch (e: Exception) {
            AppLog.e("发送H.264配置数据失败: $connectionId", e)
        }
    }

    private fun sendScreenResolutionToConnectionViaWebSocket(connectionId: String) {
        try {
            val webSocketClient = webSocketClients[connectionId] ?: return
            val resolutionManager = ResolutionManager.getInstance(this)
            val (originalWidth, originalHeight) = resolutionManager.getOriginalScreenResolution()
            webSocketClient.sendScreenResolution(originalWidth, originalHeight)
        } catch (e: Exception) {
            AppLog.e("通过WebSocket发送屏幕分辨率信息失败", e)
        }
    }

    private fun stopCasting(connectionId: String? = null) {
        if (connectionId != null) {
            rtpSenders[connectionId]?.let { sender ->
                sender.stop()
                rtpSenders.remove(connectionId)
            }

            val connection = stateManager.findConnectionById(connectionId)
            if (connection != null) {
                webSocketManager.sendCastingState(connection.connectionId, false, "投屏停止")
                webSocketManager.stopFunction(connection, WebSocketManager.FUNCTION_VIDEO)
                webSocketClients.remove(connectionId)
                stateManager.updateConnection(connection.connectionId) { conn -> conn.withCasting(false) }
            }
        } else {
            val connectionIds = rtpSenders.keys.toList()
            rtpSenders.values.forEach { sender ->
                try {
                    sender.cleanup()
                } catch (e: Exception) {
                    AppLog.e("清理RtpSender资源时发生异常", e)
                    try {
                        sender.stop()
                    } catch (stopException: Exception) {
                        AppLog.e("停止RtpSender时也发生异常", stopException)
                    }
                }
            }
            rtpSenders.clear()

            connectionIds.forEach { connId ->
                val connection = stateManager.findConnectionById(connId)
                if (connection != null) {
                    webSocketManager.sendCastingState(connection.connectionId, false, "投屏服务停止")
                    webSocketManager.stopFunction(connection, WebSocketManager.FUNCTION_VIDEO)
                    webSocketManager.removeListeners(connection.connectionId)
                    stateManager.updateConnection(connection.connectionId) { conn -> conn.withCasting(false) }
                }
            }
            webSocketClients.clear()
        }

        val hasActiveConnections = rtpSenders.isNotEmpty()

        if (!hasActiveConnections && isVideoSourceRunning) {
            stopVideoSource()
        }

        if (!hasActiveConnections) {
            stopForeground(STOP_FOREGROUND_REMOVE)
            stopSelf()
        } else {
            updateNotification()
        }
    }

    private fun updateNotification() {
        val castingCount = rtpSenders.size
        val content = when (castingCount) {
            0 -> "投屏服务运行中"
            1 -> "正在投屏到 1 个接收端"
            else -> "正在投屏到 $castingCount 个接收端"
        }

        val config = AppNotificationManager.NotificationConfig(
            title = "投屏服务",
            content = content
        )
        val notification = AppNotificationManager.createNotification(this, AppNotificationManager.NotificationType.CASTING, config)
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as android.app.NotificationManager
        notificationManager.notify(AppNotificationManager.NotificationType.CASTING.notificationId, notification)
    }

    private fun handleWebSocketMessage(connectionId: String, controlMessage: ControlMessage) {
        when (controlMessage.type) {
            ControlMessage.TYPE_DISCONNECT -> {
                stopCasting(connectionId)
            }
            ControlMessage.TYPE_BITRATE_CONTROL -> {
                val bitrate = controlMessage.getIntData("bitrate") ?: 0
                if (bitrate > 0) {
                    updateBitRate(bitrate)
                }
            }
            ControlMessage.TYPE_RESOLUTION_ADJUSTMENT_COMPLETE -> {
                val width = controlMessage.getIntData("width") ?: 0
                val height = controlMessage.getIntData("height") ?: 0
                val success = controlMessage.getBooleanData("success") ?: false
                val error = controlMessage.getStringData("error")
                handleResolutionAdjustmentComplete(connectionId, width, height, success, error)
            }
            ControlMessage.TYPE_LANDSCAPE_MODE_CONTROL -> {
                val enabled = controlMessage.getBooleanData("enabled") ?: false
                handleLandscapeModeControl(connectionId, enabled)
            }
        }
    }

    private fun handleResolutionAdjustmentComplete(connectionId: String, width: Int, @Suppress("UNUSED_PARAMETER") height: Int, success: Boolean, error: String?) {
        try {
            synchronized(resolutionAdjustLock) {
                isResolutionAdjusting = false
            }

            val resolutionManager = ResolutionManager.getInstance(this)
            val (originalWidth, originalHeight) = resolutionManager.getOriginalScreenResolution()
            val scalePercent = if (originalWidth > 0 && originalHeight > 0) {
                ((width.toFloat() / originalWidth) * 100).toInt()
            } else {
                100
            }

            com.example.castapp.viewmodel.SenderViewModel.notifyResolutionAdjustmentComplete(
                scalePercent, success, error
            )
        } catch (e: Exception) {
            AppLog.e("处理分辨率调整完成通知失败: $connectionId", e)
        }
    }

    private fun updateBitRate(newBitRate: Int) {
        try {
            val bitrateMbps = newBitRate / 1_000_000

            if (videoEncoder == null) {
                AppLog.w("视频编码器未初始化，无法调整码率")
                return
            }

            videoEncoder?.updateBitRate(newBitRate)
            Thread.sleep(200)

            val currentBitRate = videoEncoder?.getCurrentBitRate() ?: 0
            if (currentBitRate != newBitRate) {
                videoEncoder?.forceUpdateBitRate(newBitRate)
                Thread.sleep(200)
            }

            val sharedPrefs = getSharedPreferences("cast_settings", MODE_PRIVATE)
            sharedPrefs.edit { putInt("bitrate_mbps", bitrateMbps) }

            webSocketClients.forEach { (_, client) ->
                client.sendBitrateControl(newBitRate)
            }

        } catch (e: Exception) {
            AppLog.e("调整码率失败", e)
        }
    }

    private fun updateResolution(scalePercent: Int, retryCount: Int = 0) {
        synchronized(resolutionAdjustLock) {
            try {
                if (isResolutionAdjusting) return
                if (scalePercent < 1 || scalePercent > 150) return

                if (rtpSenders.isEmpty() || !isVideoSourceRunning) {
                    val resolutionManager = ResolutionManager.getInstance(this)
                    resolutionManager.setResolutionScale(scalePercent)
                    return
                }

                isResolutionAdjusting = true
                val resolutionManager = ResolutionManager.getInstance(this)
                if (!resolutionManager.setResolutionScale(scalePercent)) {
                    isResolutionAdjusting = false
                    return
                }

                val (newOutputWidth, newOutputHeight) = calculateOrientationAwareResolution(videoSourceOrientation, scalePercent)
                val restartSuccess = restartVideoSourceWithNewResolution(newOutputWidth, newOutputHeight)

                if (restartSuccess) {
                    notifyReceiversResolutionChange(newOutputWidth, newOutputHeight)
                } else {
                    isResolutionAdjusting = false
                    if (retryCount < 1) {
                        Thread {
                            Thread.sleep(2000)
                            updateResolution(scalePercent, retryCount + 1)
                        }.start()
                    }
                }

            } catch (e: Exception) {
                AppLog.e("调整分辨率失败", e)
                isResolutionAdjusting = false
                try {
                    val resolutionManager = ResolutionManager.getInstance(this)
                    resolutionManager.setResolutionScale(scalePercent)
                } catch (saveException: Exception) {
                    AppLog.e("保存分辨率设置也失败", saveException)
                }
                if (retryCount < 1) {
                    Thread {
                        Thread.sleep(3000)
                        updateResolution(scalePercent, retryCount + 1)
                    }.start()
                }
            }
        }
    }

    private fun restartVideoSourceWithNewResolution(newWidth: Int, newHeight: Int): Boolean {
        return try {
            val currentMediaProjectionManager = mediaProjectionManager ?: return false

            val sharedPrefs = getSharedPreferences("cast_settings", MODE_PRIVATE)
            val bitrateMbps = sharedPrefs.getInt("bitrate_mbps", 20)
            val bitrateInBps = bitrateMbps * 1_000_000

            val oldResources = videoEncoder?.ultraFastStop()

            videoEncoder = VideoEncoder(
                width = newWidth,
                height = newHeight,
                bitRate = bitrateInBps,
                onEncodedData = { buffer, size ->
                    rtpSenders.forEach { (connectionId, sender) ->
                        try {
                            sender.sendH264Data(buffer, size)
                        } catch (e: Exception) {
                            AppLog.e("发送数据到连接 $connectionId 失败", e)
                        }
                    }
                },
                onConfigurationData = { spsData, ppsData ->
                    // 🚀 兼容性回调：使用旧方法
                    webSocketClients.values.forEach { client ->
                        client.sendH264Config(spsData, ppsData)
                    }
                },
                onConfigurationDataWithResolution = { spsData, ppsData, width, height ->
                    // 🚀 新增：使用包含分辨率信息的方法
                    webSocketClients.values.forEach { client ->
                        client.sendH264ConfigWithResolution(spsData, ppsData, width, height)
                    }
                },
                onConfigurationDataWithOrientation = { spsData, ppsData, width, height, orientation ->
                    // 🎯 横竖屏适配：使用包含分辨率和方向信息的方法
                    webSocketClients.values.forEach { client ->
                        client.sendH264ConfigWithResolutionAndOrientation(spsData, ppsData, width, height, orientation)
                    }
                }
            )

            val inputSurface = videoEncoder?.start()

            // 🎯 横竖屏适配：设置VideoEncoder的当前视频源方向
            videoEncoder?.setOrientation(videoSourceOrientation)

            oldResources?.let { (oldCodec, oldSurface) ->
                Thread {
                    try {
                        oldCodec?.let { codec ->
                            try {
                                codec.stop()
                                codec.release()
                            } catch (e: Exception) {
                                AppLog.w("释放旧MediaCodec时发生异常", e)
                            }
                        }
                        oldSurface?.let { surface ->
                            try {
                                if (surface.isValid) {
                                    surface.release()
                                }
                            } catch (e: Exception) {
                                AppLog.w("释放旧Surface时发生异常", e)
                            }
                        }
                    } catch (e: Exception) {
                        AppLog.w("后台释放旧编码器资源时发生异常", e)
                    }
                }.apply {
                    name = "VideoEncoder-BackgroundRelease"
                    priority = Thread.MIN_PRIORITY
                    start()
                }
            }

            if (inputSurface == null) return false

            val captureSuccess = currentMediaProjectionManager.restartScreenCaptureWithNewResolution(inputSurface, newWidth, newHeight)
            if (!captureSuccess) {
                videoEncoder?.stop()
                videoEncoder = null
                return false
            }

            true
        } catch (e: Exception) {
            AppLog.e("重启VideoEncoder失败", e)
            false
        }
    }

    private fun notifyReceiversResolutionChange(newWidth: Int, newHeight: Int) {
        try {
            var successCount = 0
            rtpSenders.keys.forEach { connectionId ->
                try {
                    val resolutionChangeMessage = ControlMessage.createResolutionChange(connectionId, newWidth, newHeight)
                    val webSocketClient = webSocketClients[connectionId]
                    if (webSocketClient?.sendMessage(resolutionChangeMessage) == true) {
                        successCount++
                    }
                } catch (e: Exception) {
                    AppLog.w("通知客户端分辨率变化失败: $connectionId", e)
                }
            }
        } catch (e: Exception) {
            AppLog.e("通知接收端分辨率变化失败", e)
        }
    }

    private fun cleanupDisconnectedConnections() {
        try {
            val disconnectedConnections = mutableListOf<String>()
            webSocketClients.entries.removeAll { (connectionId, client) ->
                if (!client.isOpen) {
                    disconnectedConnections.add(connectionId)
                    true
                } else {
                    false
                }
            }
        } catch (e: Exception) {
            AppLog.e("清理断开连接时发生异常", e)
        }
    }

    private fun deepCleanupConnections() {
        try {
            forceGarbageCollection()
        } catch (e: Exception) {
            AppLog.e("深度清理连接时发生异常", e)
        }
    }

    private fun forceGarbageCollection() {
        System.gc()
        System.runFinalization()
        System.gc()
    }

    override fun onDestroy() {
        super.onDestroy()
        AppLog.service("开始清理CastingService资源...")

        // 🚀 根源优化：按依赖关系顺序清理，避免重复清理
        try {
            // 1. 停止投屏服务（最高优先级）
            stopCasting()
            AppLog.service("✓ 投屏服务已停止")
        } catch (e: Exception) {
            AppLog.e("停止投屏服务时发生异常", e)
        }

        try {
            // 2. 清理MediaProjection资源
            mediaProjectionManager?.release()
            mediaProjectionManager = null
            AppLog.service("✓ MediaProjection资源已清理")
        } catch (e: Exception) {
            AppLog.e("清理MediaProjection资源时发生异常", e)
        }



        try {
            // 3. 清理定期清理任务实例，然后清理单例引用（避免重复）
            periodicCleanupTask?.cleanup()
            periodicCleanupTask = null
            PeriodicCleanupTask.clearInstance() // 现在只清理引用，不重复调用cleanup
            AppLog.service("✓ 定期清理任务已清理")
        } catch (e: Exception) {
            AppLog.e("清理定期清理任务时发生异常", e)
        }

        try {
            // 4. 清理内存监控实例，然后清理单例引用（避免重复）
            memoryMonitor?.cleanup()
            memoryMonitor = null
            MemoryMonitor.clearInstance() // 现在只清理引用，不重复调用cleanup
            AppLog.service("✓ 内存监控已清理")
        } catch (e: Exception) {
            AppLog.e("清理内存监控时发生异常", e)
        }

        try {
            // 5. 执行垃圾回收（最低优先级）
            forceGarbageCollection()
            AppLog.service("✓ 垃圾回收已执行")
        } catch (e: Exception) {
            AppLog.e("执行垃圾回收时发生异常", e)
        }

        AppLog.service("CastingService资源清理完成")
    }

    // ========== 🎯 横竖屏适配功能 ==========



    /**
     * 🎯 横竖屏适配：初始化方向状态（根源性修复版）
     */
    private fun initCurrentOrientation() {
        try {
            // 获取当前屏幕方向
            val currentSystemOrientation = resources.configuration.orientation
            systemOrientation = currentSystemOrientation

            // 🎯 关键修复：视频源方向应该初始化为当前系统方向，而不是硬编码为竖屏
            // 这样可以确保在横屏状态下开始投屏时，视频源方向就是正确的横屏状态
            videoSourceOrientation = currentSystemOrientation

            AppLog.service("🎯 初始化方向状态 - 系统方向: ${getOrientationName(systemOrientation)}, 视频源方向: ${getOrientationName(videoSourceOrientation)}")
        } catch (e: Exception) {
            AppLog.e("🎯 初始化方向状态失败", e)
        }
    }

    /**
     * 处理屏幕方向变化
     */
    private fun handleOrientationChange(newOrientation: Int) {
        try {
            AppLog.service("🎯 开始处理方向变化: ${getOrientationName(newOrientation)}")

            // 根据新方向计算分辨率
            val resolutionManager = ResolutionManager.getInstance(this)
            val currentScale = resolutionManager.getCurrentResolutionScale()
            val (newWidth, newHeight) = calculateOrientationAwareResolution(newOrientation, currentScale)

            AppLog.service("🎯 方向适配分辨率: ${newWidth}x${newHeight} (缩放: ${currentScale}%)")

            // 使用现有的分辨率更新机制
            if (newWidth > 0 && newHeight > 0) {
                restartVideoSourceWithNewResolution(newWidth, newHeight)
                notifyReceiversResolutionChange(newWidth, newHeight)
                AppLog.service("🎯 方向适配完成: ${getOrientationName(newOrientation)}")
            } else {
                AppLog.w("🎯 计算的分辨率无效: ${newWidth}x${newHeight}")
            }

        } catch (e: Exception) {
            AppLog.e("🎯 处理方向变化失败", e)
        }
    }

    /**
     * 🎯 横竖屏适配：更新方向状态（在投屏开始时调用，根源性修复版）
     */
    private fun updateCurrentOrientationState() {
        try {
            val currentSystemOrientation = resources.configuration.orientation
            systemOrientation = currentSystemOrientation
            AppLog.service("🎯 投屏开始时更新方向状态: 系统方向=${getOrientationName(systemOrientation)}, 视频源方向=${getOrientationName(videoSourceOrientation)}")

            // 🎯 修复：投屏开始时不自动处理方向变化，避免意外触发横屏模式
            // 方向变化应该只通过横屏开关的显式控制来触发
            if (hasLandscapeModeEnabled() && systemOrientation != videoSourceOrientation) {
                AppLog.service("🎯 投屏开始时检测到方向差异，但不自动处理（需要通过横屏开关显式控制）: 系统方向${getOrientationName(systemOrientation)}, 视频源方向${getOrientationName(videoSourceOrientation)}")
            }
        } catch (e: Exception) {
            AppLog.e("🎯 更新方向状态失败", e)
        }
    }

    /**
     * 根据方向计算分辨率
     */
    private fun calculateOrientationAwareResolution(orientation: Int, scalePercent: Int): Pair<Int, Int> {
        return try {
            val resolutionManager = ResolutionManager.getInstance(this)
            val (originalWidth, originalHeight) = resolutionManager.getOriginalScreenResolution()

            // 根据方向决定是否交换宽高
            val (baseWidth, baseHeight) = when (orientation) {
                Configuration.ORIENTATION_LANDSCAPE -> {
                    // 横屏：交换宽高，如1080×2400变为2400×1080
                    Pair(originalHeight, originalWidth)
                }
                else -> {
                    // 竖屏：保持原始宽高
                    Pair(originalWidth, originalHeight)
                }
            }

            // 应用缩放
            val scaledWidth = (baseWidth * scalePercent / 100.0).toInt()
            val scaledHeight = (baseHeight * scalePercent / 100.0).toInt()

            // 确保偶数（H.264编码要求）
            val evenWidth = scaledWidth and 0xFFFFFFFE.toInt()
            val evenHeight = scaledHeight and 0xFFFFFFFE.toInt()

            AppLog.service("🎯 分辨率计算: 原始${originalWidth}x${originalHeight} -> 基础${baseWidth}x${baseHeight} -> 最终${evenWidth}x${evenHeight}")

            Pair(evenWidth, evenHeight)
        } catch (e: Exception) {
            AppLog.e("🎯 计算方向感知分辨率失败", e)
            Pair(0, 0)
        }
    }

    /**
     * 获取方向名称（用于日志）
     */
    private fun getOrientationName(orientation: Int): String {
        return when (orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> "横屏"
            Configuration.ORIENTATION_PORTRAIT -> "竖屏"
            else -> "未知($orientation)"
        }
    }

    // ========== 🎯 横屏模式控制功能 ==========

    /**
     * 处理横屏模式控制消息
     * 🎯 优化：开启时立即检测，关闭时强制竖屏
     */
    private fun handleLandscapeModeControl(connectionId: String, enabled: Boolean) {
        try {
            AppLog.service("🎯 收到横屏模式控制消息: $connectionId -> $enabled")

            // 更新连接的横屏模式状态（不持久化）
            landscapeModeEnabled[connectionId] = enabled

            AppLog.service("🎯 横屏模式状态已更新: $connectionId -> $enabled")

            // 🎯 优化：根据开关状态执行相应操作
            if (enabled) {
                // 开启横屏模式：立即触发方向检测（带强制处理和重试机制）
                triggerOrientationDetectionWithRetry()
                AppLog.service("🎯 横屏模式已开启，触发立即方向检测（含重试机制）")
            } else {
                // 关闭横屏模式：强制切换到竖屏模式
                forcePortraitMode()
                AppLog.service("🎯 横屏模式已关闭，强制切换到竖屏模式")
            }

        } catch (e: Exception) {
            AppLog.e("🎯 处理横屏模式控制失败: $connectionId", e)
        }
    }

    /**
     * 检查是否有连接启用了横屏模式
     */
    private fun hasLandscapeModeEnabled(): Boolean {
        return landscapeModeEnabled.values.any { it }
    }

    /**
     * 🎯 带重试机制的方向检测（最终修复版）
     * 解决横屏开关时序问题：强制同步当前系统方向
     */
    private fun triggerOrientationDetectionWithRetry() {
        triggerOrientationDetectionWithRetry(maxRetries = 5)
    }

    /**
     * 🎯 带重试机制的方向检测（内部实现，支持重试次数限制）
     */
    private fun triggerOrientationDetectionWithRetry(maxRetries: Int) {
        try {
            if (!isVideoSourceRunning) {
                if (maxRetries > 0) {
                    AppLog.service("🎯 视频源未运行，延迟重试方向检测（剩余重试次数: $maxRetries）")
                    // 🎯 关键修复：视频源未运行时，延迟重试而不是直接跳过
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        triggerOrientationDetectionWithRetry(maxRetries - 1)
                    }, 500) // 延迟500ms重试
                } else {
                    AppLog.service("🎯 视频源未运行，重试次数已用完，跳过方向检测")
                }
                return
            }

            // 🎯 关键修复：强制同步当前实际的系统方向
            val currentActualOrientation = resources.configuration.orientation
            AppLog.service("🎯 横屏开关触发时状态检查: 缓存系统方向=${getOrientationName(systemOrientation)}, 实际系统方向=${getOrientationName(currentActualOrientation)}, 视频源方向=${getOrientationName(videoSourceOrientation)}")

            if (systemOrientation != currentActualOrientation) {
                AppLog.service("🎯 强制同步系统方向: ${getOrientationName(systemOrientation)} -> ${getOrientationName(currentActualOrientation)}")
                systemOrientation = currentActualOrientation
            } else {
                AppLog.service("🎯 系统方向状态已同步，无需更新")
            }

            // 执行方向检测
            val success = performOrientationDetection(forceProcess = false)

            if (!success) {
                // 如果没有检测到方向差异，说明当前方向已经正确，无需处理
                AppLog.service("🎯 当前方向已正确，横屏开关已启用，等待系统方向变化")
            } else {
                AppLog.service("🎯 检测到方向差异，已处理方向变化")
            }

        } catch (e: Exception) {
            AppLog.e("🎯 带重试机制的方向检测失败", e)
        }
    }

    /**
     * 🎯 执行方向检测的核心逻辑（时序修复版）
     * @param forceProcess 是否强制处理，即使方向相同也要重新应用设置
     * @return 是否执行了方向处理
     */
    private fun performOrientationDetection(forceProcess: Boolean): Boolean {
        try {
            // 🎯 修复：使用已维护的systemOrientation状态，避免重新读取导致的时序问题
            AppLog.service("🎯 方向检测: 当前系统方向=${getOrientationName(systemOrientation)}, 当前视频源方向=${getOrientationName(videoSourceOrientation)}, 强制处理=$forceProcess")

            // 检查是否需要处理方向变化（基于视频源方向而不是系统方向）
            val needsProcessing = systemOrientation != videoSourceOrientation || forceProcess

            if (needsProcessing) {
                if (systemOrientation != videoSourceOrientation) {
                    AppLog.service("🎯 检测到方向差异，处理视频源方向变化: ${getOrientationName(videoSourceOrientation)} -> ${getOrientationName(systemOrientation)}")
                } else {
                    AppLog.service("🎯 方向相同但强制处理: ${getOrientationName(systemOrientation)}")
                }

                // 更新视频源方向状态为当前系统方向
                videoSourceOrientation = systemOrientation
                handleOrientationChange(systemOrientation)
                return true
            } else {
                AppLog.service("🎯 当前系统方向与视频源方向一致，且未要求强制处理")
                return false
            }

        } catch (e: Exception) {
            AppLog.e("🎯 执行方向检测失败", e)
            return false
        }
    }

    /**
     * 🎯 强制切换到竖屏模式（根源性修复版）
     * 当横屏模式关闭时，无论当前实际方向如何，都强制切换到竖屏模式
     */
    private fun forcePortraitMode() {
        try {
            if (!isVideoSourceRunning) {
                AppLog.service("🎯 视频源未运行，跳过强制竖屏切换")
                return
            }

            AppLog.service("🎯 强制切换到竖屏模式: 当前视频源方向=${getOrientationName(videoSourceOrientation)}")

            // 强制设置为竖屏方向并处理变化
            val portraitOrientation = Configuration.ORIENTATION_PORTRAIT
            if (videoSourceOrientation != portraitOrientation) {
                AppLog.service("🎯 执行强制竖屏切换: 视频源方向${getOrientationName(videoSourceOrientation)} -> ${getOrientationName(portraitOrientation)}")
                videoSourceOrientation = portraitOrientation
                handleOrientationChange(portraitOrientation)
            } else {
                AppLog.service("🎯 视频源已是竖屏模式，无需重新处理")
            }

        } catch (e: Exception) {
            AppLog.e("🎯 强制竖屏模式切换失败", e)
        }
    }




}
