package com.example.castapp.rtp

import java.nio.ByteBuffer

/**
 * Payload视图类 - 零拷贝优化核心
 * 
 * 通过视图方式访问payload数据，避免不必要的数据拷贝
 * 支持ByteBuffer和ByteArray两种数据源
 */
class PayloadView {
    companion object {
        // 空视图单例，避免重复创建
        val EMPTY = PayloadView().apply {
            setEmpty()
        }
    }
    
    // 🚀 智能零拷贝：统一使用SmartDataView数据源
    private var sourceSmartDataView: com.example.castapp.network.SmartDataView? = null

    // 🚀 零拷贝优化：支持ByteArray直接数据源
    private var sourceByteArrayDataView: com.example.castapp.network.DataView? = null

    // 视图范围
    private var offset: Int = 0
    private var length: Int = 0

    // 视图状态
    private var isEmpty: Boolean = true
    

    




    /**
     * 从SmartDataView创建视图（智能零拷贝）
     */
    fun setView(smartDataView: com.example.castapp.network.SmartDataView, offset: Int, length: Int) {
        require(offset >= 0) { "offset不能为负数: $offset" }
        require(length >= 0) { "length不能为负数: $length" }
        require(offset + length <= smartDataView.size) {
            "视图范围超出SmartDataView: offset=$offset, length=$length, smartDataView.size=${smartDataView.size}"
        }

        this.sourceSmartDataView = smartDataView
        this.sourceByteArrayDataView = null // 清除ByteArray视图
        this.offset = offset
        this.length = length
        this.isEmpty = length == 0
    }

    /**
     * 🚀 零拷贝优化：直接从ByteArray设置视图
     * 避免SmartBuffer的中间拷贝步骤
     */
    fun setViewFromByteArray(byteArrayDataView: com.example.castapp.network.DataView, offset: Int, length: Int) {
        require(offset >= 0) { "offset不能为负数: $offset" }
        require(length >= 0) { "length不能为负数: $length" }
        require(offset + length <= byteArrayDataView.size) {
            "视图范围超出ByteArrayDataView: offset=$offset, length=$length, dataView.size=${byteArrayDataView.size}"
        }

        this.sourceByteArrayDataView = byteArrayDataView
        this.sourceSmartDataView = null // 清除SmartDataView
        this.offset = offset
        this.length = length
        this.isEmpty = length == 0
    }
    
    /**
     * 设置为空视图
     */
    fun setEmpty() {
        // 🚀 智能缓冲区管理：释放SmartDataView引用
        this.sourceSmartDataView?.release()

        this.sourceSmartDataView = null
        this.sourceByteArrayDataView = null // 清除ByteArray视图
        this.offset = 0
        this.length = 0
        this.isEmpty = true
    }
    
    /**
     * 获取视图长度
     */
    fun size(): Int = length
    
    /**
     * 检查是否为空
     */
    fun isEmpty(): Boolean = isEmpty
    
    /**
     * 检查是否非空
     */
    fun isNotEmpty(): Boolean = !isEmpty
    
    /**
     * 🚀 零拷贝写入：统一优化，移除向后兼容分支
     * 完全消除临时数组创建，直接内存操作
     */
    fun writeTo(targetBuffer: ByteBuffer) {
        if (isEmpty) return

        // 🚀 优先处理ByteArray数据源（最常用路径）
        sourceByteArrayDataView?.let { byteArrayDataView ->
            if (byteArrayDataView is com.example.castapp.codec.VideoDecoder.ByteArrayDataView) {
                val sourceBuffer = byteArrayDataView.getDirectByteBuffer()
                targetBuffer.put(sourceBuffer)
            } else {
                // 直接数组访问，避免临时拷贝
                val data = byteArrayDataView.toByteArray()
                targetBuffer.put(data)
            }
            return
        }

        // SmartDataView数据源处理
        sourceSmartDataView?.let { smartDataView ->
            if (smartDataView.isValid()) {
                val sourceBuffer = smartDataView.getDirectByteBuffer()
                if (sourceBuffer != null) {
                    // 直接ByteBuffer传输，最高效
                    val originalPosition = sourceBuffer.position()
                    val originalLimit = sourceBuffer.limit()

                    sourceBuffer.position(offset)
                    sourceBuffer.limit(offset + length)
                    targetBuffer.put(sourceBuffer)

                    // 恢复源缓冲区状态
                    sourceBuffer.position(originalPosition)
                    sourceBuffer.limit(originalLimit)
                } else {
                    // 直接数组访问回退
                    writeToDirectArray(targetBuffer, smartDataView)
                }
            }
        }
    }

    /**
     * 🚀 直接数组访问：移除临时数组创建
     */
    private fun writeToDirectArray(targetBuffer: ByteBuffer, smartDataView: com.example.castapp.network.SmartDataView) {
        val sourceArray = smartDataView.getDirectByteArray()
        if (sourceArray != null) {
            // 直接创建ByteBuffer视图，零拷贝
            val sourceBuffer = ByteBuffer.wrap(sourceArray, offset, length)
            targetBuffer.put(sourceBuffer)
        } else {
            // 极端回退：逐字节访问
            for (i in 0 until length) {
                targetBuffer.put(smartDataView.getByte(offset + i))
            }
        }
    }


    
    /**
     * 获取指定位置的字节（用于解析协议头）
     * 在RtpPacket.parseFuAInfo()中使用，用于解析FU-A头部信息
     */
    fun getByte(index: Int): Byte {
        require(index >= 0 && index < length) {
            "索引超出范围: index=$index, length=$length"
        }

        return sourceSmartDataView?.getByte(offset + index)
            ?: throw IllegalStateException("SmartDataView未初始化")
    }
    
    /**
     * 创建子视图（零拷贝）
     * 在RtpPacket.getFuANalDataView()中使用，用于创建FU-A NAL数据的子视图
     */
    fun subView(subOffset: Int, subLength: Int): PayloadView {
        require(subOffset >= 0) { "子视图offset不能为负数: $subOffset" }
        require(subLength >= 0) { "子视图length不能为负数: $subLength" }
        require(subOffset + subLength <= length) {
            "子视图范围超出当前视图: subOffset=$subOffset, subLength=$subLength, currentLength=$length"
        }

        val subView = PayloadView()
        sourceSmartDataView?.let { smartDataView ->
            subView.setView(smartDataView, offset + subOffset, subLength)
        } ?: subView.setEmpty()
        return subView
    }
    
    /**
     * 🚀 简化ByteArray创建：移除向后兼容，统一处理
     * 注意：此方法会产生数据拷贝，优先使用writeTo方法
     */
    internal fun toByteArray(): ByteArray {
        if (isEmpty) return ByteArray(0)

        // ByteArray数据源：直接返回
        sourceByteArrayDataView?.let { return it.toByteArray() }

        // SmartDataView数据源：高效读取
        sourceSmartDataView?.let { smartDataView ->
            val result = ByteArray(length)
            for (i in 0 until length) {
                result[i] = smartDataView.getByte(offset + i)
            }
            return result
        }

        return ByteArray(0)
    }
    
    /**
     * 调试用字符串表示
     */
    override fun toString(): String {
        return "PayloadView(length=$length, isEmpty=$isEmpty, " +
                "source=${if (sourceSmartDataView != null) "SmartDataView" else "None"})"
    }
}
