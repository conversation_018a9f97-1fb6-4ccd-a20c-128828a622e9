package com.example.castapp.audio

import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import com.example.castapp.utils.AppLog

/**
 * 音频缓冲区池 - 零拷贝优化核心
 * 
 * 通过缓冲区重用减少GC压力和内存分配开销
 * 支持多种缓冲区大小的智能管理
 */
class AudioBufferPool private constructor() {
    companion object {        
        // 缓冲区大小配置
        private const val SMALL_BUFFER_SIZE = 1024      // 1KB - 小音频帧
        private const val MEDIUM_BUFFER_SIZE = 4096     // 4KB - 中等音频帧
        private const val LARGE_BUFFER_SIZE = 16384     // 16KB - 大音频帧
        
        // 池大小配置
        private const val SMALL_POOL_SIZE = 32
        private const val MEDIUM_POOL_SIZE = 16
        private const val LARGE_POOL_SIZE = 8
        
        // 单例实例
        @Volatile
        private var INSTANCE: AudioBufferPool? = null
        
        fun getInstance(): AudioBufferPool {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AudioBufferPool().also { INSTANCE = it }
            }
        }
    }
    
    // 不同大小的缓冲区池
    private val smallBufferPool = ConcurrentLinkedQueue<ByteArray>()
    private val mediumBufferPool = ConcurrentLinkedQueue<ByteArray>()
    private val largeBufferPool = ConcurrentLinkedQueue<ByteArray>()
    
    // 统计信息
    private val smallPoolHits = AtomicLong(0)
    private val mediumPoolHits = AtomicLong(0)
    private val largePoolHits = AtomicLong(0)
    private val poolMisses = AtomicLong(0)
    private val buffersCreated = AtomicLong(0)
    private val buffersReturned = AtomicLong(0)
    
    // 当前池大小
    private val smallPoolSize = AtomicInteger(0)
    private val mediumPoolSize = AtomicInteger(0)
    private val largePoolSize = AtomicInteger(0)
    
    init {
        // 预填充缓冲区池
        preAllocateBuffers()
        AppLog.audio("音频缓冲区池初始化完成")
    }
    
    /**
     * 预分配缓冲区
     */
    private fun preAllocateBuffers() {
        // 预分配小缓冲区
        repeat(SMALL_POOL_SIZE / 2) {
            smallBufferPool.offer(ByteArray(SMALL_BUFFER_SIZE))
            smallPoolSize.incrementAndGet()
        }
        
        // 预分配中等缓冲区
        repeat(MEDIUM_POOL_SIZE / 2) {
            mediumBufferPool.offer(ByteArray(MEDIUM_BUFFER_SIZE))
            mediumPoolSize.incrementAndGet()
        }
        
        // 预分配大缓冲区
        repeat(LARGE_POOL_SIZE / 2) {
            largeBufferPool.offer(ByteArray(LARGE_BUFFER_SIZE))
            largePoolSize.incrementAndGet()
        }
        
        AppLog.audio("预分配缓冲区: 小=${smallPoolSize.get()}, 中=${mediumPoolSize.get()}, 大=${largePoolSize.get()}")
    }
    
    /**
     * 获取缓冲区
     */
    fun getBuffer(size: Int): ByteArray {
        return when {
            size <= SMALL_BUFFER_SIZE -> {
                smallBufferPool.poll()?.also {
                    smallPoolHits.incrementAndGet()
                    smallPoolSize.decrementAndGet()
                } ?: createNewBuffer(SMALL_BUFFER_SIZE)
            }
            size <= MEDIUM_BUFFER_SIZE -> {
                mediumBufferPool.poll()?.also {
                    mediumPoolHits.incrementAndGet()
                    mediumPoolSize.decrementAndGet()
                } ?: createNewBuffer(MEDIUM_BUFFER_SIZE)
            }
            size <= LARGE_BUFFER_SIZE -> {
                largeBufferPool.poll()?.also {
                    largePoolHits.incrementAndGet()
                    largePoolSize.decrementAndGet()
                } ?: createNewBuffer(LARGE_BUFFER_SIZE)
            }
            else -> {
                // 超大缓冲区，直接创建
                poolMisses.incrementAndGet()
                createNewBuffer(size)
            }
        }
    }
    
    /**
     * 归还缓冲区
     */
    fun returnBuffer(buffer: ByteArray) {
        if (buffer.isEmpty()) return
        
        val returned = when (buffer.size) {
            SMALL_BUFFER_SIZE -> {
                if (smallPoolSize.get() < SMALL_POOL_SIZE) {
                    smallBufferPool.offer(buffer)
                    smallPoolSize.incrementAndGet()
                    true
                } else false
            }
            MEDIUM_BUFFER_SIZE -> {
                if (mediumPoolSize.get() < MEDIUM_POOL_SIZE) {
                    mediumBufferPool.offer(buffer)
                    mediumPoolSize.incrementAndGet()
                    true
                } else false
            }
            LARGE_BUFFER_SIZE -> {
                if (largePoolSize.get() < LARGE_POOL_SIZE) {
                    largeBufferPool.offer(buffer)
                    largePoolSize.incrementAndGet()
                    true
                } else false
            }
            else -> false // 非标准大小，不归还
        }
        
        if (returned) {
            buffersReturned.incrementAndGet()
        }
    }
    
    /**
     * 创建新缓冲区
     */
    private fun createNewBuffer(size: Int): ByteArray {
        poolMisses.incrementAndGet()
        buffersCreated.incrementAndGet()
        return ByteArray(size)
    }
    
    /**
     * 清理缓冲区池
     */
    fun cleanup() {
        smallBufferPool.clear()
        mediumBufferPool.clear()
        largeBufferPool.clear()

        smallPoolSize.set(0)
        mediumPoolSize.set(0)
        largePoolSize.set(0)

        AppLog.audio("音频缓冲区池已清理")
    }
}
