package com.example.castapp.ui.dialog

import android.content.Context
import android.graphics.Color
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.SeekBar
import android.widget.TextView
import android.view.View
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.SwitchCompat
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.ui.adapter.CustomColorPaletteAdapter
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.ColorPaletteManager
import com.example.castapp.utils.ColorUtils
import com.skydoves.colorpickerview.ColorPickerView
import com.skydoves.colorpickerview.listeners.ColorListener

/**
 * 🎨 HSV色轮颜色选择对话框
 * 使用 skydoves/ColorPickerView 实现专业级颜色选择
 * 支持自定义色板、透明度控制和数值输入
 */
class ColorPickerDialog(
    private val context: Context,
    private val currentColor: Int = "#6B6B6B".toColorInt(),
    private val title: String = "🎨 边框颜色",
    private val isStrokeMode: Boolean = false,
    currentStrokeWidth: Float = 4.0f,
    currentStrokeEnabled: Boolean = false,
    private val isWindowColorMode: Boolean = false,
    currentWindowColorEnabled: Boolean = false,
    private val onColorSelected: (Int) -> Unit,
    private val onStrokeConfigChanged: ((Boolean, Float, Int) -> Unit)? = null,
    private val onWindowColorConfigChanged: ((Boolean, Int) -> Unit)? = null
) {

    private var selectedColor: Int = currentColor
    private lateinit var colorPaletteManager: ColorPaletteManager
    private lateinit var customPaletteAdapter: CustomColorPaletteAdapter

    // UI控件引用
    private lateinit var tvDialogTitle: TextView
    private lateinit var colorPickerView: ColorPickerView
    private lateinit var viewColorPreview: View
    private lateinit var tvHexValue: TextView
    private lateinit var tvRgbValue: TextView
    private lateinit var seekbarAlpha: SeekBar
    private lateinit var tvAlphaValue: TextView
    private lateinit var etHexInput: EditText
    private lateinit var etRInput: EditText
    private lateinit var etGInput: EditText
    private lateinit var etBInput: EditText
    private lateinit var btnSaveColor: Button
    private lateinit var btnDeleteColor: Button

    // 描边控件
    private lateinit var strokeControlContainer: LinearLayout
    private lateinit var switchStrokeEnabled: SwitchCompat
    private lateinit var strokeWidthContainer: LinearLayout
    private lateinit var etStrokeWidth: EditText

    // 窗口颜色控件
    private lateinit var windowColorControlContainer: LinearLayout
    private lateinit var switchWindowColorEnabled: SwitchCompat

    private var isUpdatingFromCode = false // 防止循环更新
    private var isUserInputting = false // 标记用户正在输入

    // 描边状态
    private var strokeEnabled = currentStrokeEnabled
    private var strokeWidth = currentStrokeWidth

    // 窗口颜色状态
    private var windowColorEnabled = currentWindowColorEnabled

    fun show() {
        try {
            val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_color_picker, null)

            // 初始化颜色管理器
            colorPaletteManager = ColorPaletteManager(context)

            // 获取控件引用
            initializeViews(dialogView)

            // 验证ColorPickerView是否正确加载
            if (!::colorPickerView.isInitialized) {
                AppLog.e("🎨 ColorPickerView控件为null，可能是库加载失败")
                return
            }

            // 初始化自定义色板
            initializeCustomPalette(dialogView)

            // 初始化透明度控制
            initializeAlphaControl()

            // 初始化数值输入
            initializeValueInputs()

            // 初始化颜色预览和显示
            updateAllColorDisplays(currentColor)

            // 设置颜色选择监听器
            colorPickerView.setColorListener(ColorListener { color, _ ->
                if (!isUpdatingFromCode) {
                    // 保持当前透明度，只更新RGB
                    val currentAlpha = Color.alpha(selectedColor)
                    selectedColor = ColorUtils.setColorAlpha(color, currentAlpha / 255.0f)
                    updateAllColorDisplays(selectedColor)
                }
            })

            // 设置初始选中颜色
            selectedColor = currentColor

            val dialog = AlertDialog.Builder(context)
                .setView(dialogView)
                .setCancelable(true)
                .create()

            val btnCancel = dialogView.findViewById<Button>(R.id.btn_cancel)
            val btnConfirm = dialogView.findViewById<Button>(R.id.btn_confirm)

            btnCancel.setOnClickListener {
                dialog.dismiss()
            }

            btnConfirm.setOnClickListener {
                if (isStrokeMode) {
                    // 描边模式：调用描边配置回调
                    onStrokeConfigChanged?.invoke(strokeEnabled, strokeWidth, selectedColor)
                } else if (isWindowColorMode) {
                    // 窗口颜色模式：调用窗口颜色配置回调
                    onWindowColorConfigChanged?.invoke(windowColorEnabled, selectedColor)
                } else {
                    // 普通颜色模式：调用颜色选择回调
                    onColorSelected(selectedColor)
                }
                dialog.dismiss()
            }

            // 立即显示对话框，确保用户能看到
            AppLog.d("🎨 立即显示对话框")
            dialog.show()

            // 延迟设置HSV调色板和颜色，减少颜色跳动
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    AppLog.d("🎨 开始延迟设置ColorPickerView")
                    colorPickerView.setHsvPaletteDrawable()
                    colorPickerView.selectByHsvColor(currentColor)
                    AppLog.d("🎨 ColorPickerView延迟设置成功")
                } catch (e: Exception) {
                    AppLog.e("🎨 ColorPickerView延迟设置失败", e)
                }
            }, 100)
        } catch (e: Exception) {
            AppLog.e("🎨 HSV ColorPickerDialog显示失败", e)
        }
    }

    /**
     * 🎨 初始化控件引用
     */
    private fun initializeViews(dialogView: View) {
        tvDialogTitle = dialogView.findViewById(R.id.tv_dialog_title)
        colorPickerView = dialogView.findViewById(R.id.colorPickerView)
        viewColorPreview = dialogView.findViewById(R.id.view_color_preview)
        tvHexValue = dialogView.findViewById(R.id.tv_hex_value)
        tvRgbValue = dialogView.findViewById(R.id.tv_rgb_value)
        seekbarAlpha = dialogView.findViewById(R.id.seekbar_alpha)
        tvAlphaValue = dialogView.findViewById(R.id.tv_alpha_value)
        etHexInput = dialogView.findViewById(R.id.et_hex_input)
        etRInput = dialogView.findViewById(R.id.et_r_input)
        etGInput = dialogView.findViewById(R.id.et_g_input)
        etBInput = dialogView.findViewById(R.id.et_b_input)
        btnSaveColor = dialogView.findViewById(R.id.btn_save_color)
        btnDeleteColor = dialogView.findViewById(R.id.btn_delete_color)

        // 描边控件
        strokeControlContainer = dialogView.findViewById(R.id.stroke_control_container)
        switchStrokeEnabled = dialogView.findViewById(R.id.switch_stroke_enabled)
        strokeWidthContainer = dialogView.findViewById(R.id.stroke_width_container)
        etStrokeWidth = dialogView.findViewById(R.id.et_stroke_width)

        // 窗口颜色控件
        windowColorControlContainer = dialogView.findViewById(R.id.window_color_control_container)
        switchWindowColorEnabled = dialogView.findViewById(R.id.switch_window_color_enabled)

        // 设置动态标题
        tvDialogTitle.text = title

        // 根据模式显示/隐藏特殊控件
        if (isStrokeMode) {
            strokeControlContainer.visibility = View.VISIBLE
            windowColorControlContainer.visibility = View.GONE
            initializeStrokeControls()
        } else if (isWindowColorMode) {
            strokeControlContainer.visibility = View.GONE
            windowColorControlContainer.visibility = View.VISIBLE
            initializeWindowColorControls()
        } else {
            strokeControlContainer.visibility = View.GONE
            windowColorControlContainer.visibility = View.GONE
        }
    }

    /**
     * 🎨 初始化自定义色板
     */
    private fun initializeCustomPalette(dialogView: View) {
        val rvCustomPalette = dialogView.findViewById<RecyclerView>(R.id.rv_custom_palette)

        // 设置网格布局管理器（4列，适配右侧较窄空间）
        val gridLayoutManager = GridLayoutManager(context, 4)
        rvCustomPalette.layoutManager = gridLayoutManager

        // 设置适当的间距避免重叠
        rvCustomPalette.setPadding(2, 2, 2, 2)
        rvCustomPalette.clipToPadding = false

        // 初始化适配器
        customPaletteAdapter = CustomColorPaletteAdapter(
            colors = colorPaletteManager.getCustomColors().toMutableList(),
            onColorSelected = { color ->
                selectedColor = color
                updateAllColorDisplays(color)
                updateColorPickerSelection(color)
            },
            onColorLongClick = { color ->
                showDeleteColorConfirmation(color)
            }
        )

        rvCustomPalette.adapter = customPaletteAdapter

        // 如果没有自定义颜色，显示推荐颜色
        if (customPaletteAdapter.itemCount == 0) {
            customPaletteAdapter.updateColors(colorPaletteManager.getRecommendedColors())
        }

        // 设置保存按钮
        btnSaveColor.setOnClickListener {
            saveCurrentColor()
        }

        // 设置删除按钮
        btnDeleteColor.setOnClickListener {
            deleteSelectedColor()
        }

        updatePaletteButtons()
    }

    /**
     * 🎨 初始化透明度控制
     */
    private fun initializeAlphaControl() {
        // 设置初始透明度
        val initialAlpha = ColorUtils.getAlphaPercentage(currentColor)
        seekbarAlpha.progress = initialAlpha
        tvAlphaValue.text = context.getString(R.string.alpha_percentage, initialAlpha)

        seekbarAlpha.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser && !isUpdatingFromCode) {
                    tvAlphaValue.text = context.getString(R.string.alpha_percentage, progress)
                    val alpha = progress / 100.0f
                    selectedColor = ColorUtils.setColorAlpha(selectedColor, alpha)
                    updateAllColorDisplays(selectedColor)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }

    /**
     * 🎨 初始化数值输入
     */
    private fun initializeValueInputs() {
        // HEX输入监听 - 使用焦点变化和延迟处理
        var hexInputHandler: Handler? = null
        etHexInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (!isUpdatingFromCode) {
                    isUserInputting = true // 标记用户正在输入
                }
            }
            override fun afterTextChanged(s: Editable?) {
                if (!isUpdatingFromCode) {
                    // 取消之前的延迟任务
                    hexInputHandler?.removeCallbacksAndMessages(null)
                    // 延迟500ms处理，避免输入过程中频繁更新
                    val handler = Handler(Looper.getMainLooper())
                    hexInputHandler = handler
                    handler.postDelayed({
                        val hexString = s.toString()
                        if (hexString.length >= 7) { // 至少#RRGGBB格式
                            val color = ColorUtils.parseHexColor(hexString)
                            if (color != null) {
                                selectedColor = color
                                updateAllColorDisplays(color, skipHexInput = true)
                                updateColorPickerSelection(color)
                            }
                        }
                        isUserInputting = false // 处理完成，清除输入状态
                    }, 500)
                }
            }
        })

        // HEX输入失去焦点时立即处理
        etHexInput.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus && !isUpdatingFromCode) {
                isUserInputting = false // 失去焦点时清除输入状态
                val hexString = etHexInput.text.toString()
                val color = ColorUtils.parseHexColor(hexString)
                if (color != null) {
                    selectedColor = color
                    updateAllColorDisplays(color, skipHexInput = true)
                    updateColorPickerSelection(color)
                }
            } else if (hasFocus) {
                isUserInputting = true // 获得焦点时标记为输入状态
            }
        }

        // RGB输入监听 - 使用延迟处理
        var rgbInputHandler: Handler? = null
        val rgbTextWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (!isUpdatingFromCode) {
                    isUserInputting = true // 标记用户正在输入
                }
            }
            override fun afterTextChanged(s: Editable?) {
                if (!isUpdatingFromCode) {
                    // 取消之前的延迟任务
                    rgbInputHandler?.removeCallbacksAndMessages(null)
                    // 延迟300ms处理RGB输入
                    val handler = Handler(Looper.getMainLooper())
                    rgbInputHandler = handler
                    handler.postDelayed({
                        updateColorFromRgbInputs()
                        isUserInputting = false // 处理完成，清除输入状态
                    }, 300)
                }
            }
        }

        etRInput.addTextChangedListener(rgbTextWatcher)
        etGInput.addTextChangedListener(rgbTextWatcher)
        etBInput.addTextChangedListener(rgbTextWatcher)

        // RGB输入失去焦点时立即处理
        val rgbFocusListener = View.OnFocusChangeListener { _, hasFocus ->
            if (!hasFocus && !isUpdatingFromCode) {
                isUserInputting = false // 失去焦点时清除输入状态
                updateColorFromRgbInputs()
            } else if (hasFocus) {
                isUserInputting = true // 获得焦点时标记为输入状态
            }
        }

        etRInput.onFocusChangeListener = rgbFocusListener
        etGInput.onFocusChangeListener = rgbFocusListener
        etBInput.onFocusChangeListener = rgbFocusListener
    }

    /**
     * 🎨 更新所有颜色显示
     */
    private fun updateAllColorDisplays(color: Int, skipHexInput: Boolean = false, skipRgbInputs: Boolean = false) {
        isUpdatingFromCode = true

        try {
            // 更新颜色预览
            viewColorPreview.setBackgroundColor(color)

            // 更新HEX值显示
            val hexValue = ColorUtils.colorToArgbHex(color)
            tvHexValue.text = hexValue
            if (!skipHexInput && !isUserInputting) {
                etHexInput.setText(hexValue)
            }

            // 更新RGB值显示
            tvRgbValue.text = ColorUtils.colorToRgbString(color)
            if (!skipRgbInputs && !isUserInputting) {
                etRInput.setText(Color.red(color).toString())
                etGInput.setText(Color.green(color).toString())
                etBInput.setText(Color.blue(color).toString())
            }

            // 更新透明度
            val alphaPercent = ColorUtils.getAlphaPercentage(color)
            seekbarAlpha.progress = alphaPercent
            tvAlphaValue.text = context.getString(R.string.alpha_percentage, alphaPercent)

            // 更新自定义色板选中状态
            customPaletteAdapter.setSelectedColor(color)

            // 更新按钮状态
            updatePaletteButtons()

            AppLog.d("🎨 颜色显示更新: $hexValue")
        } finally {
            isUpdatingFromCode = false
        }
    }

    /**
     * 🎨 从RGB输入框更新颜色
     */
    private fun updateColorFromRgbInputs() {
        try {
            val rText = etRInput.text.toString().trim()
            val gText = etGInput.text.toString().trim()
            val bText = etBInput.text.toString().trim()

            // 只有当所有输入都不为空且长度合理时才处理
            if (rText.isNotEmpty() && gText.isNotEmpty() && bText.isNotEmpty() &&
                rText.length <= 3 && gText.length <= 3 && bText.length <= 3) {

                val r = rText.toInt()
                val g = gText.toInt()
                val b = bText.toInt()

                // 验证RGB值范围
                if (r in 0..255 && g in 0..255 && b in 0..255) {
                    val currentAlpha = Color.alpha(selectedColor)
                    val color = ColorUtils.createArgbColor(currentAlpha, r, g, b)
                    if (color != null) {
                        selectedColor = color
                        updateAllColorDisplays(color, skipRgbInputs = true)
                        updateColorPickerSelection(color)
                        AppLog.d("🎨 RGB输入更新颜色: R=$r, G=$g, B=$b")
                    }
                }
            }
        } catch (_: NumberFormatException) {
            // 忽略数字格式错误
            AppLog.d("🎨 RGB输入格式错误，忽略更新")
        } catch (e: Exception) {
            // 忽略其他无效输入
            AppLog.w("🎨 RGB输入处理异常", e)
        }
    }

    /**
     * 🎨 更新色轮选择器位置
     */
    private fun updateColorPickerSelection(color: Int) {
        try {
            colorPickerView.selectByHsvColor(color)
        } catch (e: Exception) {
            AppLog.w("🎨 更新色轮选择器失败", e)
        }
    }

    /**
     * 🎨 初始化描边控件
     */
    private fun initializeStrokeControls() {
        try {
            // 设置初始状态
            switchStrokeEnabled.isChecked = strokeEnabled
            etStrokeWidth.setText(strokeWidth.toString())
            updateStrokeWidthContainerState(strokeEnabled)

            // 描边开关监听器
            switchStrokeEnabled.setOnCheckedChangeListener { _, isChecked ->
                strokeEnabled = isChecked
                updateStrokeWidthContainerState(isChecked)
                AppLog.d("🎨 描边开关状态: $isChecked")
            }

            // 描边宽度输入监听器
            etStrokeWidth.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    try {
                        val widthText = s?.toString() ?: "4.0"
                        if (widthText.isNotEmpty()) {
                            val width = widthText.toFloatOrNull() ?: 4.0f
                            if (width >= 0.5f && width <= 20.0f) {
                                strokeWidth = width
                                AppLog.d("🎨 描边宽度更新: ${width}px")
                            }
                        }
                    } catch (e: Exception) {
                        AppLog.w("🎨 解析描边宽度失败", e)
                    }
                }
            })

            AppLog.d("🎨 描边控件初始化完成")
        } catch (e: Exception) {
            AppLog.e("🎨 初始化描边控件失败", e)
        }
    }

    /**
     * 🎨 更新描边宽度容器状态
     */
    private fun updateStrokeWidthContainerState(enabled: Boolean) {
        strokeWidthContainer.isEnabled = enabled
        etStrokeWidth.isEnabled = enabled
        val alpha = if (enabled) 1.0f else 0.5f
        strokeWidthContainer.alpha = alpha
    }

    /**
     * 🎨 初始化窗口颜色控件
     */
    private fun initializeWindowColorControls() {
        try {
            // 设置初始状态
            switchWindowColorEnabled.isChecked = windowColorEnabled

            // 窗口颜色开关监听器
            switchWindowColorEnabled.setOnCheckedChangeListener { _, isChecked ->
                windowColorEnabled = isChecked
                AppLog.d("🎨 窗口颜色开关状态: $isChecked")
            }

            AppLog.d("🎨 窗口颜色控件初始化完成")
        } catch (e: Exception) {
            AppLog.e("🎨 初始化窗口颜色控件失败", e)
        }
    }

    /**
     * 🎨 保存当前颜色
     */
    private fun saveCurrentColor() {
        val success = colorPaletteManager.saveColor(selectedColor)
        if (success) {
            customPaletteAdapter.addColor(selectedColor)
            updatePaletteButtons()
            AppLog.d("🎨 颜色保存成功: ${ColorUtils.colorToArgbHex(selectedColor)}")
        } else {
            AppLog.d("🎨 颜色已存在或保存失败")
        }
    }

    /**
     * 🎨 删除选中的颜色
     */
    private fun deleteSelectedColor() {
        if (customPaletteAdapter.getSelectedColor() != null) {
            val colorToDelete = customPaletteAdapter.getSelectedColor()!!
            showDeleteColorConfirmation(colorToDelete)
        }
    }

    /**
     * 🎨 显示删除颜色确认对话框
     */
    private fun showDeleteColorConfirmation(color: Int) {
        AlertDialog.Builder(context)
            .setTitle("删除颜色")
            .setMessage("确定要删除这个颜色吗？")
            .setPositiveButton("删除") { _, _ ->
                val success = colorPaletteManager.deleteColor(color)
                if (success) {
                    customPaletteAdapter.removeColor(color)
                    updatePaletteButtons()
                    AppLog.d("🎨 颜色删除成功: ${ColorUtils.colorToArgbHex(color)}")
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 🎨 更新色板按钮状态
     */
    private fun updatePaletteButtons() {
        val canSaveMore = colorPaletteManager.canSaveMoreColors()
        val isColorSaved = colorPaletteManager.isColorSaved(selectedColor)
        val hasSelectedColor = customPaletteAdapter.getSelectedColor() != null

        btnSaveColor.isEnabled = canSaveMore && !isColorSaved
        btnDeleteColor.isEnabled = hasSelectedColor && isColorSaved

        btnSaveColor.text = if (isColorSaved) "已保存" else "保存"
    }
}
