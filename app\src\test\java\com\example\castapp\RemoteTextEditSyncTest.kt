package com.example.castapp

import org.junit.Test
import org.junit.Assert.*
import com.example.castapp.websocket.ControlMessage

/**
 * 🎯 遥控端文字窗口编辑功能同步机制测试
 * 
 * 测试范围：
 * 1. 文字窗口编辑模式控制消息的创建和解析
 * 2. 文字格式同步消息的创建和解析
 * 3. 消息数据的完整性验证
 */
class RemoteTextEditSyncTest {

    @Test
    fun testTextWindowEditModeControlMessage() {
        // 测试编辑模式控制消息的创建
        val connectionId = "remote_control_123"
        val targetWindowId = "text_window_001"
        val isEnabled = true

        val message = ControlMessage.createTextWindowEditModeControl(
            connectionId = connectionId,
            targetWindowId = targetWindowId,
            isEnabled = isEnabled
        )

        // 验证消息类型
        assertEquals(ControlMessage.TYPE_TEXT_WINDOW_EDIT_MODE_CONTROL, message.type)
        
        // 验证连接ID
        assertEquals(connectionId, message.connectionId)
        
        // 验证数据内容
        assertEquals(targetWindowId, message.getStringData("target_window_id"))
        assertEquals(isEnabled, message.getBooleanData("edit_mode_enabled"))
        assertNotNull(message.data["timestamp"])
    }

    @Test
    fun testTextWindowFormatSyncMessage() {
        // 测试文字格式同步消息的创建
        val connectionId = "remote_control_123"
        val targetWindowId = "text_window_001"
        val textContent = "测试文字内容"
        val formatData = mapOf(
            "isBold" to true,
            "isItalic" to false,
            "fontSize" to 16
        )

        val message = ControlMessage.createTextWindowFormatSync(
            connectionId = connectionId,
            targetWindowId = targetWindowId,
            textContent = textContent,
            formatData = formatData
        )

        // 验证消息类型
        assertEquals(ControlMessage.TYPE_TEXT_WINDOW_FORMAT_SYNC, message.type)
        
        // 验证连接ID
        assertEquals(connectionId, message.connectionId)
        
        // 验证数据内容
        assertEquals(targetWindowId, message.getStringData("target_window_id"))
        assertEquals(textContent, message.getStringData("text_content"))
        
        // 验证格式数据
        val receivedFormatData = message.data["format_data"] as? Map<String, Any>
        assertNotNull(receivedFormatData)
        assertEquals(true, receivedFormatData?.get("isBold"))
        assertEquals(false, receivedFormatData?.get("isItalic"))
        assertEquals(16, receivedFormatData?.get("fontSize"))
        
        assertNotNull(message.data["timestamp"])
    }

    @Test
    fun testMessageDataIntegrity() {
        // 测试消息数据的完整性
        val formatData = mapOf(
            "textContent" to "Hello World",
            "isBold" to true,
            "isItalic" to true,
            "fontSize" to 18,
            "fontFamily" to "Arial",
            "textAlignment" to 1
        )

        val message = ControlMessage.createTextWindowFormatSync(
            connectionId = "test_connection",
            targetWindowId = "test_window",
            textContent = formatData["textContent"] as String,
            formatData = formatData
        )

        // 验证所有格式数据都被正确保存
        val receivedFormatData = message.data["format_data"] as? Map<String, Any>
        assertNotNull(receivedFormatData)
        
        formatData.forEach { (key, value) ->
            if (key != "textContent") { // textContent 单独存储
                assertEquals("格式数据 $key 不匹配", value, receivedFormatData?.get(key))
            }
        }
    }

    @Test
    fun testEditModeControlMessageDisabled() {
        // 测试编辑模式关闭消息
        val message = ControlMessage.createTextWindowEditModeControl(
            connectionId = "test_connection",
            targetWindowId = "test_window",
            isEnabled = false
        )

        assertEquals(false, message.getBooleanData("edit_mode_enabled"))
    }

    @Test
    fun testEmptyTextContentHandling() {
        // 测试空文本内容的处理
        val message = ControlMessage.createTextWindowFormatSync(
            connectionId = "test_connection",
            targetWindowId = "test_window",
            textContent = "",
            formatData = mapOf("isBold" to true)
        )

        assertEquals("", message.getStringData("text_content"))
        val formatData = message.data["format_data"] as? Map<String, Any>
        assertEquals(true, formatData?.get("isBold"))
    }
}
