package com.example.castapp.model

import java.util.UUID

/**
 * 远程连接数据模型
 * 用于管理远程控制发送端连接信息
 */
data class RemoteSenderConnection(
    val id: String,
    val ipAddress: String,
    val port: Int,
    val deviceName: String,
    val isConnected: Boolean = false,
    val lastConnectedTime: Long = 0L,
    val createdTime: Long = System.currentTimeMillis()
) {
    
    /**
     * 获取显示文本
     */
    fun getDisplayText(): String = "$ipAddress:$port"
    
    /**
     * 获取连接状态文本
     */
    fun getStatusText(): String = if (isConnected) "已连接" else "未连接"
    
    /**
     * 更新连接状态
     */
    fun withConnectionState(connected: Boolean): RemoteSenderConnection = copy(
        isConnected = connected,
        lastConnectedTime = if (connected) System.currentTimeMillis() else lastConnectedTime
    )

    companion object {
        /**
         * 生成连接ID
         */
        fun generateId(): String = UUID.randomUUID().toString()
        
        /**
         * 创建新的远程连接
         */
        fun create(ipAddress: String, port: Int, deviceName: String): RemoteSenderConnection {
            return RemoteSenderConnection(
                id = generateId(),
                ipAddress = ipAddress,
                port = port,
                deviceName = deviceName
            )
        }
        
        /**
         * 验证IP地址格式
         */
        fun isValidIpAddress(ip: String): Boolean {
            return try {
                val parts = ip.split(".")
                if (parts.size != 4) return false
                parts.all { part ->
                    val num = part.toIntOrNull()
                    num != null && num in 0..255
                }
            } catch (_: Exception) {
                false
            }
        }
        
        /**
         * 验证设备名称
         */
        fun isValidDeviceName(name: String): Boolean {
            return name.isNotBlank() && name.length <= 50
        }
    }
}
