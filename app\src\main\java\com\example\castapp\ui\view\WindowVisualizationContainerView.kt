package com.example.castapp.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.widget.FrameLayout
import androidx.core.graphics.toColorInt
import androidx.core.graphics.withSave
import com.example.castapp.model.WindowVisualizationData
import com.example.castapp.utils.AppLog
import kotlin.math.roundToInt

/**
 * 🪟 单个窗口容器可视化View
 * 使用View.clipBounds实现裁剪，与接收端CropManager保持一致
 * 🎯 修复：改为FrameLayout以支持裁剪覆盖层
 */
class WindowVisualizationContainerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // 窗口数据
    private var windowData: WindowVisualizationData? = null

    // 🎯 新增：裁剪模式相关
    var cropOverlay: CropOverlayView? = null // 公开访问，供父容器使用
        private set
    private var isCropping = false
    private var cropModeCallback: ((RectF?, Boolean) -> Unit)? = null

    // 🎯 修复：使用WeakReference避免内存泄漏和并发问题
    private var borderViewRef: java.lang.ref.WeakReference<View>? = null
    private var isDetached = false

    // 🎯 边框状态保存（用于裁剪模式恢复，参考接收端行为）
    private var borderStateBeforeCrop = false

    // 📝 文本窗口View相关
    private var textWindowView: TextWindowView? = null

    // 🎯 新增：遥控端编辑模式相关属性
    private var isRemoteEditMode = false
    private var remoteTextEditPanel: TextEditPanel? = null
    private var onRemoteEditStateChangeListener: ((Boolean) -> Unit)? = null
    private var onRemoteTextChangeListener: ((String) -> Unit)? = null
    
    // 绘制相关
    private val windowFillPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }
    


    /**
     * 设置窗口数据并更新显示
     */
    fun setWindowData(data: WindowVisualizationData) {
        setWindowDataInternal(data, forceRefresh = false)
    }

    /**
     * 内部窗口数据设置方法，支持强制刷新
     */
    private fun setWindowDataInternal(data: WindowVisualizationData, forceRefresh: Boolean = false) {
        this.windowData = data

        // 🎯 关键：使用View.clipBounds实现裁剪，与接收端保持一致
        applyClipBounds(data)

        // 🎯 关键修复：使用原始尺寸作为布局参数，让scaleX/scaleY负责所有缩放
        // 这样可以避免文字重新布局，实现纯粹的视觉缩放效果
        val originalWidth = data.originalWidth
        val originalHeight = data.originalHeight

        // 🎯 对文本窗口应用DPI密度调整到原始尺寸
        val (adjustedWidth, adjustedHeight) = if (data.isTextWindow() && data.textFormatData != null) {
            val (dpiAdjustedWidth, dpiAdjustedHeight) = applyDpiAdjustmentToWindowSize(originalWidth, originalHeight, data.textFormatData)
            AppLog.d("【DPI窗口调整】🎯 原始尺寸DPI调整:")
            AppLog.d("【DPI窗口调整】  原始尺寸: ${originalWidth}×${originalHeight}")
            AppLog.d("【DPI窗口调整】  DPI调整后: ${dpiAdjustedWidth}×${dpiAdjustedHeight}")
            Pair(dpiAdjustedWidth, dpiAdjustedHeight)
        } else {
            Pair(originalWidth, originalHeight)
        }

        // 设置View的位置（使用可视化位置）
        val bounds = data.getVisualizationBounds()

        // 🎯 修复：确保layoutParams不为null
        if (layoutParams == null) {
            layoutParams = ViewGroup.LayoutParams(adjustedWidth, adjustedHeight)
        } else {
            layoutParams = layoutParams.apply {
                width = adjustedWidth
                height = adjustedHeight
            }
        }

        // 设置位置
        x = bounds.left
        y = bounds.top

        // 设置旋转
        rotation = data.rotationAngle

        // 🎯 精确修复：根据文字窗口的缩放状态决定是否应用容器级缩放
        if (data.isTextWindow()) {
            // 检查接收端文字窗口是否被用户手动缩放过
            val isUserScaled = data.scaleFactor != 1.0f

            if (isUserScaled) {
                // 接收端文字窗口已被用户缩放，遥控端需要应用容器级缩放来同步显示
                applyContainerScale(data.scaleFactor, data.remoteControlScale)
                AppLog.d("【窗口容器View】🎯 文字窗口已被用户缩放，应用容器级缩放")
                AppLog.d("【窗口容器View】🎯   接收端缩放: ${data.scaleFactor}x")
                AppLog.d("【窗口容器View】🎯   远程控制缩放: ${data.remoteControlScale}x")
                AppLog.d("【窗口容器View】🎯   总缩放因子: ${(data.scaleFactor * data.remoteControlScale).toFloat()}x")
            } else {
                // 接收端文字窗口未被用户缩放（默认状态），遥控端不应用容器级缩放
                AppLog.d("【窗口容器View】🎯 文字窗口未被用户缩放，跳过容器级缩放")
                AppLog.d("【窗口容器View】🎯   接收端缩放: ${data.scaleFactor}x (默认值)")
                AppLog.d("【窗口容器View】🎯   远程控制缩放: ${data.remoteControlScale}x")
                AppLog.d("【窗口容器View】🎯   避免重复缩放，保持原始尺寸显示")
            }
        } else {
            // 非文字窗口（投屏窗口等）正常应用容器级缩放
            applyContainerScale(data.scaleFactor, data.remoteControlScale)
        }

        // 设置透明度
        alpha = data.alpha

        invalidate()

        AppLog.d("【窗口容器View】设置窗口数据: ${data.getShortConnectionId()}")
        AppLog.d("  位置: (${bounds.left}, ${bounds.top})")
        AppLog.d("  尺寸: ${bounds.width().roundToInt()}×${bounds.height().roundToInt()}")
        AppLog.d("  裁剪状态: ${data.cropRectRatio != null}")
        AppLog.d("  镜像状态: ${data.isMirrored}")
        AppLog.d("  截图数据: ${if (data.screenshotBitmap != null) "有截图 ${data.screenshotBitmap.width}×${data.screenshotBitmap.height}" else "无截图"}")

        // 🎯 添加边框状态调试日志
        AppLog.d("【窗口容器View】边框状态:")
        AppLog.d("  边框启用: ${data.isBorderEnabled}")
        AppLog.d("  边框颜色: ${String.format("#%08X", data.borderColor)}")
        AppLog.d("  边框宽度: ${data.borderWidth}dp")

        // 🎯 关键修复：使用独立边框View，与接收端保持一致
        updateBorderView(data)

        // 📝 处理文本窗口View的创建或更新
        handleTextWindowView(data)

        // 强制刷新（用于裁剪模式的完整重建）
        if (forceRefresh) {
            invalidate()
            requestLayout()

            // 刷新所有子View
            for (i in 0 until childCount) {
                getChildAt(i)?.let { child ->
                    child.invalidate()
                    child.requestLayout()
                }
            }

            AppLog.d("【窗口容器View】强制刷新完成: ${data.getShortConnectionId()}")
        }
    }

    /**
     * 🎯 新增：更新窗口位置（拖动时调用，同步更新边框位置）
     */
    fun updatePosition(newX: Float, newY: Float) {
        // 更新容器位置
        x = newX
        y = newY

        // 🎯 关键：同步更新边框View位置
        updateBorderPosition()

        AppLog.v("【窗口容器View】位置已更新: ($newX, $newY), 边框已同步")
    }


    
    /**
     * 🎯 应用View.clipBounds裁剪，与接收端CropManager保持一致
     */
    private fun applyClipBounds(data: WindowVisualizationData) {
        val cropRatio = data.cropRectRatio
        // 🎯 圆角缩放修复：根据是否应用容器级缩放决定圆角半径计算方式
        val cornerRadiusPx = if (data.isTextWindow() && data.scaleFactor != 1.0f) {
            // 文字窗口已被用户缩放，将应用容器级缩放，圆角半径不预先缩放
            dpToPx(data.cornerRadius)
        } else {
            // 其他情况：正常应用缩放因子到圆角半径
            dpToPx(data.cornerRadius * data.scaleFactor)
        }

        if (cropRatio != null) {
            // 🎯 统一方案：直接设置View的clipBounds（与接收端CropManager相同）
            val bounds = data.getVisualizationBounds()
            val clipBounds = Rect(
                (cropRatio.left * bounds.width()).toInt(),
                (cropRatio.top * bounds.height()).toInt(),
                (cropRatio.right * bounds.width()).toInt(),
                (cropRatio.bottom * bounds.height()).toInt()
            )

            this.clipBounds = clipBounds

            // 🎯 关键修复：裁剪状态下同时使用clipBounds和圆角裁剪（与接收端保持一致）
            // clipBounds负责矩形区域裁剪，outline负责圆角裁剪
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    // 🎯 关键：基于clipBounds区域设置圆角outline
                    outline.setRoundRect(
                        clipBounds.left,
                        clipBounds.top,
                        clipBounds.right,
                        clipBounds.bottom,
                        cornerRadiusPx
                    )
                }
            }
            clipToOutline = true // 🎯 启用圆角裁剪

            AppLog.d("【窗口容器View】应用View.clipBounds裁剪: ${data.getShortConnectionId()}")
            AppLog.d("  裁剪比例: left=${cropRatio.left}, top=${cropRatio.top}, right=${cropRatio.right}, bottom=${cropRatio.bottom}")
            AppLog.d("  裁剪区域: $clipBounds")
            AppLog.d("  圆角半径: ${data.cornerRadius}dp × ${data.scaleFactor} = ${data.cornerRadius * data.scaleFactor}dp (${cornerRadiusPx}px)")
        } else {
            // 清除裁剪，但保持圆角
            this.clipBounds = null

            // 🎯 未裁剪状态：设置基于整个容器的圆角outline
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.width, view.height, cornerRadiusPx)
                }
            }
            clipToOutline = true

            AppLog.d("【窗口容器View】移除裁剪: ${data.getShortConnectionId()}")
            if (data.isTextWindow() && data.scaleFactor != 1.0f) {
                AppLog.d("  圆角半径: ${data.cornerRadius}dp (文字窗口容器级缩放，不预先缩放圆角)")
            } else {
                AppLog.d("  圆角半径: ${data.cornerRadius}dp × ${data.scaleFactor} = ${data.cornerRadius * data.scaleFactor}dp (${cornerRadiusPx}px)")
            }
        }
    }

    /**
     * 📝 处理文本窗口View的创建或更新
     */
    private fun handleTextWindowView(data: WindowVisualizationData) {
        if (data.isTextWindow() && data.hasTextContent()) {
            // 文本窗口：创建或更新TextView
            if (textWindowView == null) {
                createTextWindowView(data)
            } else {
                // 更新现有的TextView内容
                updateTextWindowView(data)
            }
        } else {
            // 非文本窗口：移除TextView（如果存在）
            removeTextWindowView()
        }
    }

    /**
     * 📝 创建文本窗口View
     */
    private fun createTextWindowView(data: WindowVisualizationData) {
        try {
            // 防止重复创建
            if (textWindowView != null) {
                AppLog.w("【窗口容器View】文本窗口View已存在，跳过创建: ${data.getShortConnectionId()}")
                return
            }

            // 创建新的TextWindowView
            val newTextView = TextWindowView(context).apply {
                // 设置布局参数，填充整个容器
                layoutParams = FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )

                // 🎯 修改：遥控端支持编辑模式，根据当前编辑状态设置交互性
                isEnabled = isRemoteEditMode
                isFocusable = isRemoteEditMode
                isFocusableInTouchMode = isRemoteEditMode

                // 📝 先设置文本内容，再应用格式（富文本反序列化需要文本内容）
                data.textContent?.let { content ->
                    setText(content) // 设置文本内容
                }

                // 应用格式信息（包括富文本格式）
                data.textFormatData?.let { formatData ->
                    applyTextFormat(formatData)
                }

                // 🎯 新增：设置遥控端编辑状态监听器
                setOnEditStateChangeListener { isEditing ->
                    isRemoteEditMode = isEditing
                    onRemoteEditStateChangeListener?.invoke(isEditing)
                    AppLog.d("【窗口容器View】遥控端编辑状态变化: $isEditing")
                }

                // 🎯 新增：设置遥控端文本变化监听器
                setOnTextChangeListener { newText ->
                    onRemoteTextChangeListener?.invoke(newText)
                    AppLog.d("【窗口容器View】遥控端文本内容变化: $newText")
                }

                // 🎯 关键新增：设置选中文字变化监听器（与接收端一致）
                setOnSelectionChangeWithAllFormatsListener { bold, italic, textColor, strokeState ->
                    // 获取选中文字的完整格式状态（包含字号）
                    val formatState = getSelectionFormatStateWithFontSize()
                    val fontSize = formatState.third

                    // 获取选中文字的字体
                    val fontFamily = getSelectionFont()

                    // 获取选中文字的字间距
                    val letterSpacing = getSelectionLetterSpacing()

                    // 获取选中文字的行间距
                    val lineSpacing = getSelectionLineSpacing()

                    // 获取文本对齐方式
                    val textAlignment = getTextGravity()

                    // 更新编辑面板按钮状态（包含所有格式信息）
                    remoteTextEditPanel?.updateButtonStatesFromSelection(
                        bold = bold,
                        italic = italic,
                        fontSize = fontSize,
                        textColor = textColor,
                        strokeState = strokeState,
                        fontFamily = fontFamily,
                        letterSpacing = letterSpacing,
                        lineSpacing = lineSpacing,
                        textAlignment = textAlignment
                    )
                    AppLog.d("【窗口容器View】遥控端选中文字格式状态已更新: 加粗=$bold, 倾斜=$italic, 字号=${fontSize}sp")
                }

                AppLog.d("【窗口容器View】文本窗口View已创建: ${data.getShortConnectionId()}")
            }

            // 添加到容器
            addView(newTextView)
            textWindowView = newTextView

            AppLog.d("【窗口容器View】文本窗口View已添加到容器: ${data.getShortConnectionId()}")

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】创建文本窗口View失败: ${data.getShortConnectionId()}", e)
        }
    }

    /**
     * 📝 应用文本格式到TextWindowView
     */
    private fun TextWindowView.applyTextFormat(formatData: Map<String, Any>) {
        try {
            // 🎯 关键修复：应用字体缩放，确保换行位置一致
            val windowData = <EMAIL>
            val scaledFormatData = if (windowData != null) {
                applyFontScaleToFormatData(formatData, windowData.remoteControlScale)
            } else {
                formatData
            }

            // 🎯 新方案：检查是否有每行文本数据（最精准的换行同步方案）
            AppLog.d("【窗口容器View】🔍 调试完整formatData: ${scaledFormatData.keys}")
            AppLog.d("【窗口容器View】🔍 检查关键数据:")
            AppLog.d("【窗口容器View】🔍   actualLineTexts存在: ${scaledFormatData.containsKey("actualLineTexts")}")
            AppLog.d("【窗口容器View】🔍   receiverDpiInfo存在: ${scaledFormatData.containsKey("receiverDpiInfo")}")

            // 🎯 调试：检查DPI信息
            val receiverDpiData = scaledFormatData["receiverDpiInfo"]
            AppLog.d("【窗口容器View】🔍 DPI信息详情: $receiverDpiData")

            val actualLineTexts = scaledFormatData["actualLineTexts"] as? List<*>
            // 🎯 优化方案：优先使用自然换行，仅在必要时使用强制换行
            val useNaturalLineBreak = true // 🎯 启用自然换行优化

            if (!useNaturalLineBreak && actualLineTexts != null && actualLineTexts.isNotEmpty()) {
                // 转换为字符串列表
                val lineTexts = actualLineTexts.mapNotNull { it as? String }
                if (lineTexts.isNotEmpty()) {
                    AppLog.d("【窗口容器View】🎯 使用接收端实际每行文本进行精准换行同步: ${lineTexts.size}行")
                    lineTexts.forEachIndexed { index, lineText ->
                        AppLog.d("【窗口容器View】  第${index + 1}行: \"$lineText\"")
                    }

                    // 🎯 关键修复：检查是否为富文本，如果是富文本则需要保持格式
                    val hasRichText = scaledFormatData["hasRichText"] as? Boolean ?: false
                    if (hasRichText) {
                        // 富文本情况：先应用富文本格式，再调整换行
                        val richTextData = scaledFormatData["richTextData"] as? String
                        if (!richTextData.isNullOrBlank()) {
                            try {
                                val textFormatManager = com.example.castapp.utils.TextFormatManager(context)
                                val spannableString = textFormatManager.deserializeSpannableStringFromJson(text.toString(), richTextData)

                                if (spannableString != null) {
                                    // 🎯 创建新的SpannableString，按行重新组织但精确保持格式
                                    val combinedText = lineTexts.joinToString("\n")
                                    val newSpannable = android.text.SpannableString(combinedText)

                                    // 🎯 精确映射格式：根据原始文本和新文本的字符对应关系来映射格式
                                    val originalText = spannableString.toString()
                                    val spans = spannableString.getSpans(0, spannableString.length, Any::class.java)

                                    for (span in spans) {
                                        try {
                                            val spanStart = spannableString.getSpanStart(span)
                                            val spanEnd = spannableString.getSpanEnd(span)
                                            val spanFlags = spannableString.getSpanFlags(span)

                                            // 🎯 关键修复：精确映射span位置到新文本
                                            val mappedPositions = mapSpanPositionToNewText(
                                                originalText, combinedText, spanStart, spanEnd
                                            )

                                            if (mappedPositions != null) {
                                                val (newStart, newEnd) = mappedPositions
                                                if (newStart < newEnd && newEnd <= newSpannable.length) {
                                                    newSpannable.setSpan(span, newStart, newEnd, spanFlags)
                                                    AppLog.d("【窗口容器View】🎯 格式映射: ${span.javaClass.simpleName} 原始($spanStart-$spanEnd) -> 新($newStart-$newEnd)")
                                                }
                                            }
                                        } catch (e: Exception) {
                                            AppLog.w("【窗口容器View】应用格式span失败: ${span.javaClass.simpleName}", e)
                                        }
                                    }

                                    setRichTextContent(newSpannable)
                                    AppLog.d("【窗口容器View】🎯 富文本精准换行同步完成，格式已保持")
                                } else {
                                    // 富文本反序列化失败，使用纯文本
                                    setLineTexts(lineTexts)
                                    AppLog.d("【窗口容器View】🎯 富文本反序列化失败，使用纯文本精准换行")
                                }
                            } catch (e: Exception) {
                                AppLog.e("【窗口容器View】富文本精准换行处理失败，使用纯文本", e)
                                setLineTexts(lineTexts)
                            }
                        } else {
                            setLineTexts(lineTexts)
                        }
                    } else {
                        // 基本文本情况：直接设置每行文本
                        setLineTexts(lineTexts)
                        AppLog.d("【窗口容器View】🎯 基本文本精准换行同步完成")
                    }

                    // 应用其他格式（背景颜色、行间距、对齐等）
                    applyOtherFormats(scaledFormatData)

                    AppLog.d("【窗口容器View】🎯 精准换行同步完成")
                    return
                }
            }

            AppLog.d("【窗口容器View】🔍 未找到每行文本数据，使用传统方式处理")
            val hasRichText = scaledFormatData["hasRichText"] as? Boolean ?: false

            if (hasRichText) {
                // 📝 处理富文本格式
                val richTextData = scaledFormatData["richTextData"] as? String
                if (!richTextData.isNullOrBlank()) {
                    try {
                        // 使用TextFormatManager反序列化富文本
                        val textFormatManager = com.example.castapp.utils.TextFormatManager(context)
                        val spannableString = textFormatManager.deserializeSpannableStringFromJson(text.toString(), richTextData)

                        if (spannableString != null) {
                            setRichTextContent(spannableString)

                            // 🎨 富文本格式应用后，也要处理基本格式信息
                            val lineSpacing = when (val spacingValue = scaledFormatData["lineSpacing"]) {
                                is Float -> spacingValue
                                is Double -> spacingValue.toFloat()
                                is Number -> spacingValue.toFloat()
                                else -> 0.0f
                            }
                            val textAlignment = when (val alignmentValue = scaledFormatData["textAlignment"]) {
                                is Int -> alignmentValue
                                is Double -> alignmentValue.toInt()
                                is Float -> alignmentValue.toInt()
                                is Number -> alignmentValue.toInt()
                                else -> 0
                            }

                            // 应用行间距（与接收端保持一致的应用方式）
                            if (lineSpacing > 0.0f) {
                                val lineSpacingExtra = lineSpacing * resources.displayMetrics.density
                                setLineSpacing(lineSpacingExtra, 1.0f)
                                AppLog.d("【窗口容器View】富文本行间距已应用: ${lineSpacing}dp")
                            }

                            // 应用文本对齐（直接使用接收端传输的Gravity值）
                            gravity = textAlignment
                            AppLog.d("【窗口容器View】富文本对齐已应用: $textAlignment")

                            // 🎨 富文本格式应用后，也要处理窗口背景颜色
                            val windowColorEnabled = scaledFormatData["windowColorEnabled"] as? Boolean ?: false
                            val windowBackgroundColor = when (val colorValue = scaledFormatData["windowBackgroundColor"]) {
                                is Int -> colorValue
                                is Double -> colorValue.toInt()
                                is Float -> colorValue.toInt()
                                is Number -> colorValue.toInt()
                                else -> 0xFFFFFFFF.toInt()
                            }



                            setWindowBackgroundColor(windowColorEnabled, windowBackgroundColor)
                            AppLog.d("【窗口容器View】富文本格式已应用: 长度=${spannableString.length}, 行间距=${lineSpacing}dp, 对齐=$textAlignment, 窗口背景颜色: 启用=$windowColorEnabled")

                            // 🎯 新增：验证自然换行结果是否与接收端一致
                            verifyNaturalLineBreak(actualLineTexts, scaledFormatData)

                            return
                        } else {
                            AppLog.w("【窗口容器View】富文本反序列化失败，使用基本格式")
                        }
                    } catch (e: Exception) {
                        AppLog.e("【窗口容器View】富文本格式应用失败，使用基本格式", e)
                    }
                }
            }

            // 📝 后备方案：应用基本格式
            val isBold = scaledFormatData["isBold"] as? Boolean ?: false
            val isItalic = scaledFormatData["isItalic"] as? Boolean ?: false
            val fontSize = scaledFormatData["fontSize"] as? Int ?: 13
            val lineSpacing = when (val spacingValue = scaledFormatData["lineSpacing"]) {
                is Float -> spacingValue
                is Double -> spacingValue.toFloat()
                is Number -> spacingValue.toFloat()
                else -> 0.0f
            }
            val textAlignment = when (val alignmentValue = scaledFormatData["textAlignment"]) {
                is Int -> alignmentValue
                is Double -> alignmentValue.toInt()
                is Float -> alignmentValue.toInt()
                is Number -> alignmentValue.toInt()
                else -> android.view.Gravity.CENTER
            }
            val fontName = scaledFormatData["fontName"] as? String

            // 🎯 关键修复：设置原始文字内容，让其自然换行
            setText(text)

            // 使用批量格式应用方法
            applyFormatToAllText(isBold, isItalic, fontSize)

            // 应用行间距（TextView级别属性）
            if (lineSpacing > 0.0f) {
                val lineSpacingExtra = lineSpacing * resources.displayMetrics.density
                setLineSpacing(lineSpacingExtra, 1.0f)
            }

            // 应用文本对齐（TextView级别属性）
            gravity = textAlignment

            // 应用字体
            if (!fontName.isNullOrBlank() && fontName != "Roboto") {
                AppLog.d("【窗口容器View】应用字体: $fontName")
            }

            // 🎨 应用窗口背景颜色
            val windowColorEnabled = scaledFormatData["windowColorEnabled"] as? Boolean ?: false
            val windowBackgroundColor = when (val colorValue = scaledFormatData["windowBackgroundColor"]) {
                is Int -> colorValue
                is Double -> colorValue.toInt()
                is Float -> colorValue.toInt()
                is Number -> colorValue.toInt()
                else -> 0xFFFFFFFF.toInt()
            }



            setWindowBackgroundColor(windowColorEnabled, windowBackgroundColor)
            AppLog.d("【窗口容器View】窗口背景颜色已应用: 启用=$windowColorEnabled, 颜色=${String.format("#%08X", windowBackgroundColor)}")

            AppLog.d("【窗口容器View】基本格式已应用: 加粗=$isBold, 倾斜=$isItalic, 字号=${fontSize}sp, 行间距=${lineSpacing}dp")

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用文本格式失败", e)
        }
    }

    /**
     * 🎯 应用其他格式（背景颜色、行间距、对齐等）
     */
    private fun TextWindowView.applyOtherFormats(formatData: Map<String, Any>) {
        try {
            // 应用行间距
            val lineSpacing = when (val spacingValue = formatData["lineSpacing"]) {
                is Float -> spacingValue
                is Double -> spacingValue.toFloat()
                is Number -> spacingValue.toFloat()
                else -> 0.0f
            }
            if (lineSpacing > 0.0f) {
                val lineSpacingExtra = lineSpacing * resources.displayMetrics.density
                setLineSpacing(lineSpacingExtra, 1.0f)
                AppLog.d("【窗口容器View】行间距已应用: ${lineSpacing}dp")
            }

            // 应用文本对齐
            val textAlignment = when (val alignmentValue = formatData["textAlignment"]) {
                is Int -> alignmentValue
                is Double -> alignmentValue.toInt()
                is Float -> alignmentValue.toInt()
                is Number -> alignmentValue.toInt()
                else -> android.view.Gravity.CENTER
            }
            gravity = textAlignment
            AppLog.d("【窗口容器View】文本对齐已应用: $textAlignment")

            // 应用窗口背景颜色
            val windowColorEnabled = formatData["windowColorEnabled"] as? Boolean ?: false
            val windowBackgroundColor = when (val colorValue = formatData["windowBackgroundColor"]) {
                is Int -> colorValue
                is Double -> colorValue.toInt()
                is Float -> colorValue.toInt()
                is Number -> colorValue.toInt()
                else -> 0xFFFFFFFF.toInt()
            }
            setWindowBackgroundColor(windowColorEnabled, windowBackgroundColor)
            AppLog.d("【窗口容器View】窗口背景颜色已应用: 启用=$windowColorEnabled")

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用其他格式失败", e)
        }
    }

    /**
     * 📝 更新现有文本窗口View的内容
     * 🎯 修复：同时应用容器级缩放和字体级缩放，实现完整的同步效果
     */
    private fun updateTextWindowView(data: WindowVisualizationData) {
        try {
            textWindowView?.let { textView ->
                AppLog.d("【窗口容器View】🎯 开始更新文本窗口，缩放因子: ${data.scaleFactor}")

                // 📝 先设置文本内容，再应用格式（富文本反序列化需要文本内容）
                data.textContent?.let { content ->
                    textView.setText(content) // 设置文本内容
                    AppLog.d("【窗口容器View】🎯 文本内容已设置: ${content.take(20)}...")
                }

                // 更新格式信息（包括富文本格式）
                data.textFormatData?.let { formatData ->
                    // 🎯 关键修复：应用缩放后的字体格式，确保换行位置一致
                    val scaledFormatData = applyFontScaleToFormatData(formatData, data.remoteControlScale)
                    textView.applyTextFormat(scaledFormatData)
                    AppLog.d("【窗口容器View】🎯 字体格式已应用，remoteControlScale: ${data.remoteControlScale}")
                }

                // 🎯 新方案：验证统一缩放是否正确应用
                val expectedTotalScale = (data.scaleFactor * data.remoteControlScale).toFloat()
                AppLog.d("【窗口容器View】🎯 统一缩放验证:")
                AppLog.d("【窗口容器View】🎯   接收端缩放因子: ${data.scaleFactor}")
                AppLog.d("【窗口容器View】🎯   远程控制缩放: ${data.remoteControlScale}")
                AppLog.d("【窗口容器View】🎯   期望总缩放: ${expectedTotalScale}")
                AppLog.d("【窗口容器View】🎯   容器实际缩放: (${<EMAIL>}, ${<EMAIL>})")
                AppLog.d("【窗口容器View】🎯   这将产生纯粹的视觉缩放，文字相对位置保持不变")

                AppLog.d("【窗口容器View】文本窗口View内容已更新: ${data.getShortConnectionId()}")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】更新文本窗口View失败: ${data.getShortConnectionId()}", e)
        }
    }

    /**
     * 📝 移除文本窗口View
     */
    private fun removeTextWindowView() {
        try {
            textWindowView?.let { textView ->
                removeView(textView)
                textWindowView = null
                AppLog.d("【窗口容器View】文本窗口View已移除")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】移除文本窗口View失败", e)
        }
    }

    /**
     * 🎯 最终方案：对格式数据进行最小化处理，主要处理DPI差异
     * 🔧 修复：增加DPI密度处理，从根本上解决不同设备间的换行差异
     * 🎯 关键修复：所有视觉缩放由容器级统一缩放处理，避免文字重新布局
     */
    private fun applyFontScaleToFormatData(formatData: Map<String, Any>, remoteControlScale: Double): Map<String, Any> {
        return try {
            val scaledFormatData = formatData.toMutableMap()

            // 获取原始字体大小
            val originalFontSize = formatData["fontSize"] as? Int ?: 13

            // 🎯 关键修复：应用DPI密度调整
            val dpiAdjustedFontSize = applyDpiDensityAdjustment(originalFontSize, formatData)

            // 🎯 关键修复：检查是否应用了DPI调整
            val hasDpiAdjustment = dpiAdjustedFontSize != originalFontSize

            val finalFontSize = if (hasDpiAdjustment) {
                // 如果应用了DPI调整，不再应用其他缩放，保持DPI调整的效果
                dpiAdjustedFontSize
            } else {
                // 如果没有DPI调整，保持原始字体大小（所有缩放由容器级缩放处理）
                originalFontSize
            }

            scaledFormatData["fontSize"] = finalFontSize

            AppLog.d("【窗口容器View】🎯 字体处理完成:")
            AppLog.d("【窗口容器View】  原始字号: ${originalFontSize}sp")
            AppLog.d("【窗口容器View】  DPI调整后: ${dpiAdjustedFontSize}sp")
            AppLog.d("【窗口容器View】  最终字号: ${finalFontSize}sp")
            AppLog.d("【窗口容器View】  🎯 容器级统一缩放将处理所有视觉缩放效果")

            scaledFormatData
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用字体缩放失败，使用原始格式", e)
            formatData
        }
    }

    /**
     * 🎯 新增：应用DPI密度调整，解决不同设备间的换行差异
     */
    private fun applyDpiDensityAdjustment(originalFontSize: Int, formatData: Map<String, Any>): Int {
        return try {
            // 获取接收端DPI信息
            val receiverDpiInfo = getReceiverDpiInfo(formatData)
            if (receiverDpiInfo == null) {
                AppLog.d("【窗口容器View】🎯 未找到接收端DPI信息，跳过DPI调整")
                return originalFontSize
            }

            // 获取当前设备（遥控端）DPI信息
            val dpiManager = com.example.castapp.utils.DpiDensityManager(context)
            val currentDpiInfo = dpiManager.getCurrentDeviceDpiInfo()

            // 计算DPI密度比值
            val dpiRatio = dpiManager.calculateDpiRatio(receiverDpiInfo, currentDpiInfo)

            // 检查是否需要调整
            if (dpiManager.isDpiSimilar(receiverDpiInfo, currentDpiInfo, 0.05f)) {
                AppLog.d("【窗口容器View】🎯 设备DPI密度相近，无需调整")
                return originalFontSize
            }

            // 应用DPI调整
            val adjustedFontSize = dpiManager.adjustFontSizeForDpi(originalFontSize, dpiRatio)

            AppLog.d("【窗口容器View】🎯 DPI密度调整完成:")
            AppLog.d("【窗口容器View】  接收端DPI: ${receiverDpiInfo.getDensityCategory()}")
            AppLog.d("【窗口容器View】  遥控端DPI: ${currentDpiInfo.getDensityCategory()}")
            AppLog.d("【窗口容器View】  字号调整: ${originalFontSize}sp -> ${adjustedFontSize}sp")

            adjustedFontSize

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】DPI密度调整失败，使用原始字号", e)
            originalFontSize
        }
    }

    /**
     * 🎯 新增：从格式数据中获取接收端DPI信息
     */
    @Suppress("UNCHECKED_CAST")
    private fun getReceiverDpiInfo(formatData: Map<String, Any>): com.example.castapp.utils.DpiDensityManager.DpiInfo? {
        return try {
            // 🎯 优先从格式数据中获取DPI信息
            val receiverDpiData = formatData["receiverDpiInfo"] as? Map<String, Any>
            AppLog.d("【窗口容器View】🎯 检查格式数据中的DPI信息: ${receiverDpiData != null}")
            if (receiverDpiData != null && receiverDpiData.isNotEmpty()) {
                val dpiInfo = com.example.castapp.utils.DpiDensityManager.DpiInfo.fromMap(receiverDpiData)
                if (dpiInfo != null) {
                    AppLog.d("【窗口容器View】🎯 从格式数据中获取到接收端DPI信息: ${dpiInfo.getDensityCategory()}")
                    return dpiInfo
                } else {
                    AppLog.w("【窗口容器View】🎯 格式数据中的DPI信息解析失败")
                }
            } else {
                AppLog.d("【窗口容器View】🎯 格式数据中未包含DPI信息或为空")
            }

            // 🎯 后备方案：从RemoteReceiverManager缓存中获取DPI信息
            val windowData = this.windowData
            if (windowData != null) {
                val receiverManager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
                val cachedDpiInfo = receiverManager.getCachedReceiverDpiInfo(windowData.connectionId)
                if (cachedDpiInfo != null) {
                    AppLog.d("【窗口容器View】🎯 从缓存中获取到接收端DPI信息: ${cachedDpiInfo.getDensityCategory()}")
                    return cachedDpiInfo
                } else {
                    AppLog.d("【窗口容器View】🎯 缓存中未找到DPI信息，连接ID: ${windowData.connectionId}")
                }
            } else {
                AppLog.d("【窗口容器View】🎯 窗口数据为空，无法获取连接ID")
            }

            AppLog.d("【窗口容器View】🎯 未找到接收端DPI信息")
            null
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】获取接收端DPI信息失败", e)
            null
        }
    }

    init {
        // 🎯 设置为可绘制，因为FrameLayout默认不绘制
        setWillNotDraw(false)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 🎯 修复：标记为已分离，避免后续操作
        isDetached = true
        // 🎯 安全清理：使用post延迟清理，避免在dispatchDetachedFromWindow过程中修改View层级
        post {
            safeRemoveBorderView()
            // 📝 清理文本窗口View
            removeTextWindowView()
        }
        AppLog.d("【窗口容器View】已从窗口分离，边框View和文本窗口View将延迟清理")
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val data = windowData ?: return

        // 绘制窗口内容（不需要Canvas.clipRect，因为已经使用View.clipBounds）
        drawWindowContent(canvas, data)
    }
    
    /**
     * 绘制窗口内容
     */
    private fun drawWindowContent(canvas: Canvas, data: WindowVisualizationData) {
        val bounds = RectF(0f, 0f, width.toFloat(), height.toFloat())

        // 🎯 修复：移除Canvas边框绘制，改用独立边框View（与接收端保持一致）
        // 🎨 修复：文本窗口任何时候都不绘制默认填充背景
        if (!data.isTextWindow() || !data.hasTextContent()) {
            // 只有非文本窗口才绘制填充背景
            if (data.isBorderEnabled) {
                val fillColor = data.borderColor and 0x00FFFFFF or 0x1A000000 // 10%透明度
                windowFillPaint.apply {
                    color = fillColor
                }
                AppLog.d("【窗口容器View】设置自定义填充色: ${data.getShortConnectionId()}")
                AppLog.d("  填充颜色: ${String.format("#%08X", fillColor)}")
            } else {
                // 默认填充样式
                windowFillPaint.apply {
                    color = "#1A4CAF50".toColorInt() // 10%透明度的绿色
                }
                AppLog.d("【窗口容器View】设置默认填充色: ${data.getShortConnectionId()}")
            }

            // 绘制矩形填充（不带圆角，圆角由Outline处理）
            canvas.drawRect(bounds, windowFillPaint)
        } else {
            AppLog.d("【窗口容器View】🎨 文本窗口跳过默认填充绘制: ${data.getShortConnectionId()}")
        }

        // 📝 只有非文本窗口才绘制截图，文本窗口使用真实TextView
        if (!data.isTextWindow() || !data.hasTextContent()) {
            // 非文本窗口：绘制截图（如果有）
            data.screenshotBitmap?.let { bitmap ->
                drawScreenshot(canvas, bitmap, bounds, data)
            }
        }
        // 📝 文本窗口的TextView已在setWindowData时创建，不需要在这里绘制

        // 🎯 移除设备名称显示：根据用户要求，不在可视化窗口中心显示设备名称
        // drawDeviceName(canvas, data, bounds)
    }
    
    /**
     * 绘制截图
     */
    private fun drawScreenshot(canvas: Canvas, bitmap: Bitmap, bounds: RectF, data: WindowVisualizationData) {
        val paint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG)

        // 计算截图的绘制区域（保持宽高比）
        val bitmapAspectRatio = bitmap.width.toFloat() / bitmap.height.toFloat()
        val boundsAspectRatio = bounds.width() / bounds.height()

        val drawRect = if (bitmapAspectRatio > boundsAspectRatio) {
            // 截图更宽，以高度为准
            val drawWidth = bounds.height() * bitmapAspectRatio
            val offsetX = (bounds.width() - drawWidth) / 2f
            RectF(bounds.left + offsetX, bounds.top, bounds.left + offsetX + drawWidth, bounds.bottom)
        } else {
            // 截图更高，以宽度为准
            val drawHeight = bounds.width() / bitmapAspectRatio
            val offsetY = (bounds.height() - drawHeight) / 2f
            RectF(bounds.left, bounds.top + offsetY, bounds.right, bounds.top + offsetY + drawHeight)
        }

        // 🎯 修复：如果窗口开启了镜像，需要对截图应用镜像变换
        if (data.isMirrored) {
            canvas.withSave {
                // 计算镜像变换：以绘制区域中心为轴进行水平镜像
                val centerX = drawRect.centerX()
                canvas.translate(centerX, 0f)  // 移动到中心
                canvas.scale(-1f, 1f)          // 水平镜像
                canvas.translate(-centerX, 0f) // 移回原位置

                canvas.drawBitmap(bitmap, null, drawRect, paint)
            }

            AppLog.v("【窗口容器View】截图已应用镜像变换: ${data.getShortConnectionId()}")
        } else {
            canvas.drawBitmap(bitmap, null, drawRect, paint)
        }
    }


    
    /**
     * 🎯 新增：开始裁剪模式
     */
    fun startCropMode(originalCropRatio: RectF?, callback: (RectF?, Boolean) -> Unit) {
        if (isCropping) {
            AppLog.d("【窗口容器View】已经在裁剪模式中")
            return
        }

        isCropping = true
        cropModeCallback = callback

        // 参考接收端CropManager.startCropMode()逻辑，完整清理裁剪状态
        val currentData = windowData
        if (currentData != null && currentData.cropRectRatio != null) {
            // 如果当前有裁剪，临时清除裁剪以显示完整画面进行重新裁剪
            AppLog.d("【窗口容器View】临时清除裁剪显示，恢复完整画面")

            // 完全清除裁剪状态
            clipBounds = null
            clipToOutline = false
            outlineProvider = null

            // 重新设置窗口数据，触发完整的重建过程
            val tempData = currentData.copy(
                cropRectRatio = null,
                isCropping = false
            )
            setWindowDataInternal(tempData, forceRefresh = true)

            AppLog.d("【窗口容器View】完整清理裁剪状态完成")
        } else {
            // 没有裁剪状态，只需要基本清理
            clipBounds = null
            clipToOutline = false
            outlineProvider = null

            AppLog.d("【窗口容器View】基本清理完成")
        }

        // 🎯 修复：参考接收端行为，进入裁剪模式时完全移除边框View
        if (currentData != null && currentData.isBorderEnabled) {
            // 保存边框状态，用于退出裁剪模式时恢复
            borderStateBeforeCrop = true
            removeBorderView()
            AppLog.d("【窗口容器View】进入裁剪模式，移除边框View（参考接收端行为）")
        } else {
            borderStateBeforeCrop = false
            AppLog.d("【窗口容器View】进入裁剪模式，当前无边框")
        }

        // 🎯 同时清除圆角裁剪效果，恢复到完整窗口显示
        if (currentData != null) {
            // 🎯 圆角缩放修复：根据是否应用容器级缩放决定圆角半径计算方式
            val cornerRadiusPx = if (currentData.isTextWindow() && currentData.scaleFactor != 1.0f) {
                // 文字窗口已被用户缩放，将应用容器级缩放，圆角半径不预先缩放
                dpToPx(currentData.cornerRadius)
            } else {
                // 其他情况：正常应用缩放因子到圆角半径
                dpToPx(currentData.cornerRadius * currentData.scaleFactor)
            }
            // 设置基于整个容器的圆角outline（无裁剪状态）
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.width, view.height, cornerRadiusPx)
                }
            }
            clipToOutline = true
            AppLog.d("【窗口容器View】临时清除裁剪效果，恢复完整画面显示")
        }

        // 🎯 修复：创建简化的裁剪覆盖层，只负责裁剪框，不包含按钮
        cropOverlay = CropOverlayView(context).apply {
            layoutParams = FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )

            // 如果有原始裁剪区域，设置为初始裁剪框位置
            originalCropRatio?.let { ratio ->
                setCropRectRatio(ratio)
                AppLog.d("【窗口容器View】恢复之前的裁剪框位置: 比例: $ratio")
            }

            // 设置裁剪变化监听器
            setCropChangeListener(object : CropOverlayView.CropChangeListener {
                override fun onCropChanged(cropRect: RectF) {
                    // 实时更新裁剪区域
                    AppLog.v("【窗口容器View】裁剪区域变化: $cropRect")
                }
            })
        }

        // 添加裁剪覆盖层到窗口容器中
        addView(cropOverlay)
        cropOverlay?.bringToFront()

        AppLog.d("【窗口容器View】进入裁剪模式")
    }

    /**
     * 🎯 新增：结束裁剪模式
     */
    fun endCropMode(isCancel: Boolean) {
        if (!isCropping) return

        val finalCropRatio = if (!isCancel) {
            cropOverlay?.getCropRectRatio()
        } else {
            null
        }

        AppLog.d("【窗口容器View】结束裁剪模式，取消: $isCancel")
        AppLog.d("【窗口容器View】最终裁剪比例: $finalCropRatio")

        // 移除裁剪覆盖层
        cropOverlay?.let { overlay ->
            removeView(overlay)
        }
        cropOverlay = null

        // 🎯 裁剪修复：退出裁剪模式时不立即重建边框View
        // 边框View将在后续的setWindowData()调用中统一重建，避免重复创建
        if (borderStateBeforeCrop) {
            AppLog.d("【裁剪修复】退出裁剪模式，边框将在数据更新时重建")
        } else {
            AppLog.d("【裁剪修复】退出裁剪模式，进入前无边框，保持无边框状态")
        }

        // 🎯 重置边框状态标记，避免状态混乱
        borderStateBeforeCrop = false

        isCropping = false

        // 🎯 关键修复：如果不是取消操作，需要立即应用最终的裁剪效果
        if (!isCancel && finalCropRatio != null) {
            // 立即应用裁剪效果到View.clipBounds
            applyFinalCropBounds(finalCropRatio)
            AppLog.d("【窗口容器View】立即应用最终裁剪效果: $finalCropRatio")
        }

        // 回调结果
        cropModeCallback?.invoke(finalCropRatio, isCancel)
        cropModeCallback = null
    }

    /**
     * 🎯 新增：应用最终裁剪边界
     */
    private fun applyFinalCropBounds(cropRatio: RectF) {
        val data = windowData ?: return
        val bounds = data.getVisualizationBounds()
        // 🎯 圆角缩放修复：根据是否应用容器级缩放决定圆角半径计算方式
        val cornerRadiusPx = if (data.isTextWindow() && data.scaleFactor != 1.0f) {
            // 文字窗口已被用户缩放，将应用容器级缩放，圆角半径不预先缩放
            dpToPx(data.cornerRadius)
        } else {
            // 其他情况：正常应用缩放因子到圆角半径
            dpToPx(data.cornerRadius * data.scaleFactor)
        }

        val clipBounds = Rect(
            (cropRatio.left * bounds.width()).toInt(),
            (cropRatio.top * bounds.height()).toInt(),
            (cropRatio.right * bounds.width()).toInt(),
            (cropRatio.bottom * bounds.height()).toInt()
        )

        this.clipBounds = clipBounds

        // 🎯 关键修复：同时应用圆角效果
        outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View, outline: Outline) {
                outline.setRoundRect(
                    clipBounds.left,
                    clipBounds.top,
                    clipBounds.right,
                    clipBounds.bottom,
                    cornerRadiusPx
                )
            }
        }
        clipToOutline = true

        AppLog.d("【窗口容器View】应用最终裁剪边界: $clipBounds")
        if (data.isTextWindow() && data.scaleFactor != 1.0f) {
            AppLog.d("【窗口容器View】应用圆角效果: ${data.cornerRadius}dp (文字窗口容器级缩放，不预先缩放圆角) (${cornerRadiusPx}px)")
        } else {
            AppLog.d("【窗口容器View】应用圆角效果: ${data.cornerRadius}dp × ${data.scaleFactor} = ${data.cornerRadius * data.scaleFactor}dp (${cornerRadiusPx}px)")
        }
    }

    /**
     * 🎯 裁剪修复：更新独立边框View（强化边框清理，避免重复创建）
     */
    private fun updateBorderView(data: WindowVisualizationData) {
        val parentView = parent as? ViewGroup
        if (parentView == null) {
            AppLog.w("【窗口容器View】无法获取父容器，跳过边框创建")
            return
        }

        // 🎯 裁剪修复：强化边框清理，确保完全移除所有现有边框
        forceRemoveAllBorderViews(parentView)

        if (data.isBorderEnabled) {
            createBorderView(data, parentView)
        }
    }

    /**
     * 🎯 新增：创建独立边框View
     */
    private fun createBorderView(data: WindowVisualizationData, parentView: ViewGroup) {
        // 🎯 修复：边框宽度需要考虑接收端窗口缩放因子，与接收端保持一致
        val scaledBorderWidth = data.borderWidth * data.scaleFactor
        val borderWidthPx = dpToPx(scaledBorderWidth).toInt()
        val cornerRadiusPx = dpToPx(data.cornerRadius * data.scaleFactor)

        // 🎯 关键：根据是否裁剪计算边框位置和尺寸（与接收端逻辑一致）
        val (borderLeft, borderTop, borderWidth, borderHeight) = if (data.cropRectRatio != null && clipBounds != null) {
            // 裁剪状态：基于clipBounds计算边框位置
            val left = clipBounds.left - borderWidthPx
            val top = clipBounds.top - borderWidthPx
            val width = clipBounds.width() + borderWidthPx * 2
            val height = clipBounds.height() + borderWidthPx * 2
            arrayOf(left, top, width, height)
        } else {
            // 未裁剪状态：基于整个容器计算边框位置
            val left = -borderWidthPx
            val top = -borderWidthPx
            val width = this.width + borderWidthPx * 2
            val height = this.height + borderWidthPx * 2
            arrayOf(left, top, width, height)
        }

        // 创建边框Paint
        val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            style = Paint.Style.STROKE
            strokeWidth = borderWidthPx.toFloat()
            color = data.borderColor
        }

        // 创建边框RectF
        val borderRect = RectF()

        // 🎯 修复：检查是否已分离，避免在分离过程中创建新View
        if (isDetached) {
            AppLog.w("【窗口容器View】容器已分离，跳过边框创建")
            return
        }

        // 创建独立边框View
        val newBorderView = object : View(context) {
            override fun onDraw(canvas: Canvas) {
                super.onDraw(canvas)

                // 设置绘制区域
                borderRect.set(
                    borderWidthPx / 2f,
                    borderWidthPx / 2f,
                    width - borderWidthPx / 2f,
                    height - borderWidthPx / 2f
                )

                // 🎯 关键：边框圆角半径需要加上边框宽度的一半，确保边框内边缘与内容区域外边缘完全贴合
                val borderRadius = cornerRadiusPx + borderWidthPx / 2f
                canvas.drawRoundRect(borderRect, borderRadius, borderRadius, borderPaint)

                AppLog.v("【窗口容器View】独立边框已绘制: ${data.getShortConnectionId()}")
            }
        }.apply {
            // 🎯 裁剪修复：为边框View添加标记，便于识别和清理
            tag = "BorderView_${data.connectionId}"
            // 设置布局参数
            layoutParams = ViewGroup.MarginLayoutParams(borderWidth, borderHeight).apply {
                leftMargin = borderLeft
                topMargin = borderTop
            }

            // 🎯 关键：边框View与容器使用相同的坐标系统
            x = this@WindowVisualizationContainerView.x
            y = this@WindowVisualizationContainerView.y
            translationX = <EMAIL>
            translationY = <EMAIL>
            scaleX = <EMAIL>
            scaleY = <EMAIL>
            rotation = <EMAIL>

            // 🎯 关键修复：设置pivot点，确保旋转时边框与容器保持贴合
            // 边框视图pivot点 = 容器pivot点 - 边框视图的layout偏移（与接收端逻辑一致）
            pivotX = <EMAIL> - borderLeft
            pivotY = <EMAIL> - borderTop

            // 设置背景透明
            setBackgroundColor(Color.TRANSPARENT)
        }

        // 🎯 修复：使用WeakReference存储边框View引用
        borderViewRef = java.lang.ref.WeakReference(newBorderView)

        // 🎯 层级修复：延迟添加边框View，避免在窗口创建过程中打乱层级
        // 将边框View的添加操作延迟到下一个UI循环，确保所有窗口容器都已正确排序
        post {
            try {
                val containerIndex = parentView.indexOfChild(this@WindowVisualizationContainerView)
                if (containerIndex >= 0) {
                    // 🎯 关键修复：将边框View插入到窗口容器的紧邻位置，但不影响其他窗口的层级
                    parentView.addView(newBorderView, containerIndex + 1)
                    AppLog.d("【层级修复】边框View已延迟创建: ${data.getShortConnectionId()}, 容器索引=$containerIndex")
                } else {
                    // 如果找不到容器索引，使用默认添加方式
                    parentView.addView(newBorderView)
                    AppLog.d("【层级修复】边框View已创建（默认位置）: ${data.getShortConnectionId()}")
                }
                AppLog.d("  边框位置: ($borderLeft, $borderTop)")
                AppLog.d("  边框尺寸: ${borderWidth}×${borderHeight}")
                AppLog.d("  边框颜色: ${String.format("#%08X", data.borderColor)}")
            } catch (e: Exception) {
                AppLog.w("【层级修复】创建边框View时发生异常", e)
                borderViewRef = null
            }
        }
    }

    /**
     * 🎯 修复：安全移除边框View
     */
    private fun safeRemoveBorderView() {
        val borderView = borderViewRef?.get()
        if (borderView != null) {
            try {
                val parentView = parent as? ViewGroup
                if (parentView != null && borderView.parent == parentView) {
                    parentView.removeView(borderView)
                    AppLog.d("【窗口容器View】边框View已安全移除")
                } else {
                    AppLog.d("【窗口容器View】边框View已不在父容器中")
                }
            } catch (e: Exception) {
                AppLog.w("【窗口容器View】移除边框View时发生异常", e)
            }
        }
        borderViewRef = null
    }

    /**
     * 🎯 兼容：保留原有接口
     */
    private fun removeBorderView() {
        safeRemoveBorderView()
    }

    /**
     * 🎯 裁剪修复：强制移除所有边框View（解决裁剪模式下边框重复问题）
     */
    private fun forceRemoveAllBorderViews(parentView: ViewGroup) {
        try {
            // 首先移除WeakReference中的边框View
            safeRemoveBorderView()

            // 🎯 关键修复：遍历父容器，移除所有与当前窗口相关的边框View
            val windowData = this.windowData
            if (windowData != null) {
                val targetTag = "BorderView_${windowData.connectionId}"
                val viewsToRemove = mutableListOf<View>()

                for (i in 0 until parentView.childCount) {
                    val child = parentView.getChildAt(i)
                    // 通过tag识别边框View
                    if (child != this && child.tag == targetTag) {
                        viewsToRemove.add(child)
                    }
                }

                // 移除找到的边框View
                viewsToRemove.forEach { view ->
                    parentView.removeView(view)
                    AppLog.d("【裁剪修复】移除重复边框View: tag=${view.tag}")
                }

                if (viewsToRemove.isNotEmpty()) {
                    AppLog.d("【裁剪修复】强制清理完成，移除了 ${viewsToRemove.size} 个重复边框View")
                }
            }

        } catch (e: Exception) {
            AppLog.w("【裁剪修复】强制清理边框View时发生异常", e)
        }
    }

    /**
     * 🎯 修复：更新边框View位置（拖动时调用）
     */
    private fun updateBorderPosition() {
        val border = borderViewRef?.get() ?: return
        val data = windowData ?: return

        // 🎯 关键：边框View与容器完全同步变换状态
        border.apply {
            x = this@WindowVisualizationContainerView.x
            y = this@WindowVisualizationContainerView.y
            translationX = <EMAIL>
            translationY = <EMAIL>
            scaleX = <EMAIL>
            scaleY = <EMAIL>
            rotation = <EMAIL>

            // 🎯 关键修复：同步更新pivot点（与接收端逻辑一致）
            // 🎯 修复：边框宽度需要考虑接收端窗口缩放因子，与接收端保持一致
            val scaledBorderWidth = data.borderWidth * data.scaleFactor
            val borderWidthPx = dpToPx(scaledBorderWidth).toInt()
            val (borderLeft, borderTop) = if (data.cropRectRatio != null && clipBounds != null) {
                // 裁剪状态：基于clipBounds计算偏移
                Pair(clipBounds.left - borderWidthPx, clipBounds.top - borderWidthPx)
            } else {
                // 未裁剪状态：基于容器边缘计算偏移
                Pair(-borderWidthPx, -borderWidthPx)
            }

            // 边框视图pivot点 = 容器pivot点 - 边框视图的layout偏移
            pivotX = <EMAIL> - borderLeft
            pivotY = <EMAIL> - borderTop
        }

        AppLog.v("【窗口容器View】边框位置已同步: (${this.x}, ${this.y})")
    }

    /**
     * 🎯 层级修复：重写bringToFront方法，确保边框View同步调整层级
     */
    override fun bringToFront() {
        // 首先调用父类方法，将窗口容器移到前台
        super.bringToFront()

        // 然后将边框View也移到前台，确保边框始终在窗口上方
        bringBorderToFront()

        val data = windowData
        if (data != null) {
            AppLog.d("【层级修复】可视化窗口和边框已同步调整到前台: ${data.getShortConnectionId()}")
        }
    }

    /**
     * 🎯 层级修复：将边框View移到前台（层级管理时调用）
     */
    private fun bringBorderToFront() {
        val borderView = borderViewRef?.get() ?: return
        val parentView = parent as? ViewGroup ?: return

        try {
            // 🎯 关键修复：将边框View重新插入到窗口容器的下一个位置
            // 先移除边框View
            parentView.removeView(borderView)

            // 获取窗口容器的当前索引
            val containerIndex = parentView.indexOfChild(this)
            if (containerIndex >= 0) {
                // 将边框View重新插入到窗口容器的下一个位置
                parentView.addView(borderView, containerIndex + 1)
                AppLog.d("【层级修复】边框View已重新插入到窗口容器的下一个位置: 容器索引=$containerIndex, 边框索引=${containerIndex + 1}")
            } else {
                // 如果找不到容器索引，回退到原有方式
                parentView.addView(borderView)
                AppLog.w("【层级修复】未找到窗口容器索引，使用默认添加方式")
            }
        } catch (e: Exception) {
            AppLog.w("【层级修复】调整边框View层级时发生异常", e)
        }
    }

    /**
     * dp转px工具方法
     */
    private fun dpToPx(dp: Float): Float {
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, resources.displayMetrics)
    }

    /**
     * 🎯 精确映射格式span位置到新文本（用于富文本精准换行同步）
     * 将原始文本中的格式位置映射到按行重组后的新文本中
     * 🔧 修复：处理重复文本的精确位置映射
     */
    private fun mapSpanPositionToNewText(
        originalText: String,
        newText: String,
        originalStart: Int,
        originalEnd: Int
    ): Pair<Int, Int>? {
        return try {
            // 验证原始位置的有效性
            if (originalStart >= originalText.length || originalEnd > originalText.length || originalStart >= originalEnd) {
                AppLog.w("【窗口容器View】原始span位置无效: $originalStart-$originalEnd, 文本长度: ${originalText.length}")
                return null
            }

            val spanText = originalText.substring(originalStart, originalEnd)
            AppLog.d("【窗口容器View】🎯 映射span文本: \"$spanText\" (原始位置: $originalStart-$originalEnd)")

            // 🎯 关键修复：使用字符级别的精确映射，而不是简单的indexOf
            // 构建字符位置映射表：原始文本位置 -> 新文本位置
            val positionMap = buildCharacterPositionMap(originalText, newText)

            if (positionMap.isEmpty()) {
                AppLog.w("【窗口容器View】无法构建字符位置映射表")
                return null
            }

            // 映射起始和结束位置
            val newStart = positionMap[originalStart]
            val newEnd = positionMap[originalEnd - 1]?.let { it + 1 } // originalEnd是exclusive的，所以要映射originalEnd-1的位置

            if (newStart == null || newEnd == null) {
                AppLog.w("【窗口容器View】无法映射span位置: 原始($originalStart-$originalEnd) -> 新($newStart-$newEnd)")
                return null
            }

            // 验证映射结果
            if (newStart < newEnd && newEnd <= newText.length) {
                val mappedText = newText.substring(newStart, newEnd)
                if (mappedText == spanText) {
                    AppLog.d("【窗口容器View】🎯 span位置映射成功: \"$spanText\" $originalStart-$originalEnd -> $newStart-$newEnd")
                    return Pair(newStart, newEnd)
                } else {
                    AppLog.w("【窗口容器View】映射后文本不匹配: 期望\"$spanText\", 实际\"$mappedText\"")
                }
            } else {
                AppLog.w("【窗口容器View】映射后位置无效: $newStart-$newEnd")
            }

            return null

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】span位置映射失败", e)
            null
        }
    }

    /**
     * 🎯 构建字符位置映射表（原始文本位置 -> 新文本位置）
     * 处理换行符重新排列后的精确字符位置对应关系
     */
    private fun buildCharacterPositionMap(originalText: String, newText: String): Map<Int, Int> {
        return try {
            val positionMap = mutableMapOf<Int, Int>()

            // 移除所有换行符，获取纯字符内容
            val originalChars = originalText.replace("\n", "")
            val newChars = newText.replace("\n", "")

            // 验证字符内容是否一致（除了换行符）
            if (originalChars != newChars) {
                AppLog.w("【窗口容器View】原始文本和新文本的字符内容不一致")
                AppLog.w("  原始字符: \"$originalChars\"")
                AppLog.w("  新文本字符: \"$newChars\"")
                return emptyMap()
            }

            // 构建映射表：跳过换行符，映射实际字符位置
            var originalIndex = 0
            var newIndex = 0
            var charIndex = 0 // 在纯字符序列中的位置

            while (originalIndex < originalText.length && newIndex < newText.length) {
                val originalChar = originalText[originalIndex]
                val newChar = newText[newIndex]

                if (originalChar == '\n') {
                    // 原始文本中的换行符，跳过
                    originalIndex++
                    continue
                }

                if (newChar == '\n') {
                    // 新文本中的换行符，跳过
                    newIndex++
                    continue
                }

                // 两个都是非换行符，应该相同
                if (originalChar == newChar) {
                    positionMap[originalIndex] = newIndex
                    AppLog.v("【窗口容器View】字符映射: '$originalChar' 原始[$originalIndex] -> 新[$newIndex]")
                    originalIndex++
                    newIndex++
                    charIndex++
                } else {
                    AppLog.w("【窗口容器View】字符不匹配: 原始[$originalIndex]='$originalChar', 新[$newIndex]='$newChar'")
                    break
                }
            }

            AppLog.d("【窗口容器View】🎯 字符位置映射表构建完成: ${positionMap.size} 个字符")
            positionMap

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】构建字符位置映射表失败", e)
            emptyMap()
        }
    }

    // ==================== 🎯 遥控端编辑模式控制方法 ====================

    /**
     * 🎯 进入遥控端编辑模式
     */
    fun enterRemoteEditMode() {
        if (!isTextWindow()) {
            AppLog.w("【窗口容器View】非文本窗口，无法进入编辑模式")
            return
        }

        try {
            isRemoteEditMode = true

            // 启用文本窗口的交互功能
            textWindowView?.let { textView ->
                textView.isEnabled = true
                textView.isFocusable = true
                textView.isFocusableInTouchMode = true
                textView.enterEditMode()
                AppLog.d("【窗口容器View】文本窗口已启用交互功能")
            }

            // 显示编辑面板
            showRemoteEditPanel()

            AppLog.d("【窗口容器View】已进入遥控端编辑模式")

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】进入遥控端编辑模式失败", e)
        }
    }

    /**
     * 🎯 退出遥控端编辑模式
     */
    fun exitRemoteEditMode() {
        try {
            isRemoteEditMode = false

            // 禁用文本窗口的交互功能
            textWindowView?.let { textView ->
                textView.isEnabled = false
                textView.isFocusable = false
                textView.isFocusableInTouchMode = false
                textView.exitEditMode()
                AppLog.d("【窗口容器View】文本窗口已禁用交互功能")
            }

            // 隐藏编辑面板
            hideRemoteEditPanel()

            // 🎯 关键修复：触发编辑状态变化监听器，确保同步检查被执行
            onRemoteEditStateChangeListener?.invoke(false)

            AppLog.d("【窗口容器View】已退出遥控端编辑模式")

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】退出遥控端编辑模式失败", e)
        }
    }

    /**
     * 🎯 显示遥控端编辑面板
     */
    private fun showRemoteEditPanel() {
        try {
            // 获取主容器
            val mainContainer = getMainContainer()
            if (mainContainer == null) {
                AppLog.w("【窗口容器View】无法获取主容器，无法显示编辑面板")
                return
            }

            // 如果编辑面板已存在且正在显示，则不重复创建
            if (remoteTextEditPanel?.isShowing() == true) {
                AppLog.d("【窗口容器View】遥控端编辑面板已在显示中")
                return
            }

            // 创建编辑面板
            remoteTextEditPanel = TextEditPanel(context, mainContainer)

            // 🎯 设置完整的格式变化监听器（与接收端一致）
            remoteTextEditPanel?.setOnFormatChangeListener { bold, italic ->
                applyRemoteFormatChanges(bold, italic)
            }

            remoteTextEditPanel?.setOnFontSizeChangeListener { fontSize ->
                applyRemoteFontSizeChange(fontSize)
            }

            remoteTextEditPanel?.setOnFontFamilyChangeListener { fontFamily ->
                applyRemoteFontFamilyChange(fontFamily)
            }

            remoteTextEditPanel?.setOnLetterSpacingChangeListener { letterSpacing ->
                applyRemoteLetterSpacingChange(letterSpacing)
            }

            remoteTextEditPanel?.setOnLineSpacingChangeListener { lineSpacing ->
                applyRemoteLineSpacingChange(lineSpacing)
            }

            remoteTextEditPanel?.setOnTextAlignmentChangeListener { alignment ->
                applyRemoteTextAlignmentChange(alignment)
            }

            remoteTextEditPanel?.setOnColorChangeListener { color ->
                applyRemoteColorChange(color)
            }

            remoteTextEditPanel?.setOnStrokeChangeListener { enabled, width, color ->
                applyRemoteStrokeChange(enabled, width, color)
            }

            remoteTextEditPanel?.setOnWindowColorChangeListener { enabled, color ->
                applyRemoteWindowColorChange(enabled, color)
            }

            remoteTextEditPanel?.setOnClearFormatListener {
                applyRemoteClearFormat()
            }

            remoteTextEditPanel?.setOnCloseListener {
                exitRemoteEditMode()
            }

            // 获取当前文本格式状态并显示面板
            textWindowView?.let { textView ->
                val currentText = textView.text.toString()
                val formatState = textView.getSelectionFormatStateWithFontSize()
                val isBold = formatState.first
                val isItalic = formatState.second
                val fontSize = formatState.third

                // 🎯 获取完整的格式状态（与接收端一致）
                val fontFamily = textView.getSelectionFont()
                val letterSpacing = textView.getSelectionLetterSpacing()
                val lineSpacing = textView.getSelectionLineSpacing()
                val textAlignment = textView.getTextGravity()

                remoteTextEditPanel?.show(
                    currentText = currentText,
                    bold = isBold,
                    italic = isItalic,
                    fontSize = fontSize,
                    fontFamily = fontFamily,
                    letterSpacing = letterSpacing,
                    lineSpacing = lineSpacing,
                    textAlignment = textAlignment
                )
            }

            AppLog.d("【窗口容器View】遥控端编辑面板已显示")

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】显示遥控端编辑面板失败", e)
        }
    }

    /**
     * 🎯 隐藏遥控端编辑面板
     */
    private fun hideRemoteEditPanel() {
        try {
            remoteTextEditPanel?.hide()
            remoteTextEditPanel = null
            AppLog.d("【窗口容器View】遥控端编辑面板已隐藏")
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】隐藏遥控端编辑面板失败", e)
        }
    }

    /**
     * 🎯 应用遥控端格式变化（对选中文字生效）
     */
    private fun applyRemoteFormatChanges(bold: Boolean, italic: Boolean) {
        try {
            textWindowView?.let { textView ->
                // 🎯 关键修复：使用选中文字格式应用方法，而不是全局格式
                textView.applyBoldToSelection(bold)
                textView.applyItalicToSelection(italic)
                AppLog.d("【窗口容器View】遥控端格式已应用到选中文字: 加粗=$bold, 倾斜=$italic")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用遥控端格式变化失败", e)
        }
    }

    /**
     * 🎯 应用遥控端字号变化（对选中文字生效）
     */
    private fun applyRemoteFontSizeChange(fontSize: Int) {
        try {
            textWindowView?.let { textView ->
                // 🎯 关键修复：使用选中文字字号应用方法
                textView.applyFontSizeToSelection(fontSize)
                AppLog.d("【窗口容器View】遥控端字号已应用到选中文字: ${fontSize}sp")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用遥控端字号变化失败", e)
        }
    }

    /**
     * 🎯 应用遥控端字体变化（对选中文字生效）
     */
    private fun applyRemoteFontFamilyChange(fontFamily: com.example.castapp.utils.FontPresetManager.FontItem) {
        try {
            textWindowView?.let { textView ->
                // 🎯 关键修复：使用选中文字字体应用方法
                textView.applyFontFamilyToSelection(fontFamily)
                AppLog.d("【窗口容器View】遥控端字体已应用到选中文字: ${fontFamily.name}")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用遥控端字体变化失败", e)
        }
    }

    /**
     * 🎯 应用遥控端字间距变化
     */
    private fun applyRemoteLetterSpacingChange(letterSpacing: Float) {
        try {
            textWindowView?.let { textView ->
                textView.applyLetterSpacingToSelection(letterSpacing)
                AppLog.d("【窗口容器View】遥控端字间距已应用: ${letterSpacing}em")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用遥控端字间距变化失败", e)
        }
    }

    /**
     * 🎯 应用遥控端行间距变化
     */
    private fun applyRemoteLineSpacingChange(lineSpacing: Float) {
        try {
            textWindowView?.let { textView ->
                textView.applyLineSpacingToSelection(lineSpacing)
                AppLog.d("【窗口容器View】遥控端行间距已应用: ${lineSpacing}dp")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用遥控端行间距变化失败", e)
        }
    }

    /**
     * 🎯 应用遥控端文本对齐变化
     */
    private fun applyRemoteTextAlignmentChange(alignment: Int) {
        try {
            textWindowView?.let { textView ->
                textView.setTextGravity(alignment)
                AppLog.d("【窗口容器View】遥控端文本对齐已应用: $alignment")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用遥控端文本对齐变化失败", e)
        }
    }

    /**
     * 🎯 应用遥控端颜色变化（对选中文字生效）
     */
    private fun applyRemoteColorChange(color: Int) {
        try {
            textWindowView?.let { textView ->
                // 🎯 关键修复：使用选中文字颜色应用方法
                textView.applyColorToSelection(color)
                AppLog.d("【窗口容器View】遥控端文字颜色已应用到选中文字: ${String.format("#%08X", color)}")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用遥控端颜色变化失败", e)
        }
    }

    /**
     * 🎯 应用遥控端描边变化
     */
    private fun applyRemoteStrokeChange(enabled: Boolean, width: Float, color: Int) {
        try {
            textWindowView?.let { textView ->
                // 🎯 关键修复：使用选中文字描边应用方法
                textView.applyStrokeToSelection(enabled, width, color)
                AppLog.d("【窗口容器View】遥控端描边已应用到选中文字: 启用=$enabled, 宽度=${width}px, 颜色=${String.format("#%08X", color)}")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用遥控端描边变化失败", e)
        }
    }

    /**
     * 🎯 应用遥控端窗口颜色变化
     */
    private fun applyRemoteWindowColorChange(enabled: Boolean, color: Int) {
        try {
            textWindowView?.let { textView ->
                // 🎯 关键修复：使用窗口背景颜色设置方法
                textView.setWindowBackgroundColor(enabled, color)
                AppLog.d("【窗口容器View】遥控端窗口颜色已应用: 启用=$enabled, 颜色=${String.format("#%08X", color)}")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用遥控端窗口颜色变化失败", e)
        }
    }

    /**
     * 🎯 应用遥控端清除格式
     */
    private fun applyRemoteClearFormat() {
        try {
            textWindowView?.let { textView ->
                // 清除基础格式
                textView.setBoldEnabled(false)
                textView.setItalicEnabled(false)
                textView.setFontSize(13) // 重置为默认字号
                textView.setTextGravity(android.view.Gravity.CENTER) // 重置为居中对齐
                AppLog.d("【窗口容器View】遥控端格式已清除")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用遥控端清除格式失败", e)
        }
    }

    /**
     * 🎯 获取主容器
     */
    private fun getMainContainer(): ViewGroup? {
        return try {
            var parent = this.parent
            while (parent != null) {
                if (parent is android.app.Activity) {
                    return parent.findViewById(android.R.id.content)
                }
                if (parent is ViewGroup && parent.id == android.R.id.content) {
                    return parent
                }
                parent = parent.parent
            }
            null
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】获取主容器失败", e)
            null
        }
    }

    /**
     * 🎯 检查是否为文本窗口
     */
    private fun isTextWindow(): Boolean {
        return windowData?.isTextWindow() == true && windowData?.hasTextContent() == true
    }

    /**
     * 🎯 设置遥控端编辑状态变化监听器
     */
    fun setOnRemoteEditStateChangeListener(listener: (Boolean) -> Unit) {
        this.onRemoteEditStateChangeListener = listener
    }

    /**
     * 🎯 获取当前文本内容和格式数据（包含完整的富文本格式）
     */
    fun getCurrentTextFormatData(): Map<String, Any>? {
        return try {
            textWindowView?.let { textView ->
                val textContent = textView.text.toString()
                val currentText = textView.text

                // 🎯 关键修复：获取完整的富文本格式数据
                val richTextData = try {
                    // 检查text是否包含格式信息（Spannable接口）
                    if (currentText is android.text.Spannable && currentText.isNotEmpty()) {
                        // 检查是否有格式Span
                        val spans = currentText.getSpans(0, currentText.length, Any::class.java)
                        val hasFormatSpans = spans.any { span ->
                            span is android.text.style.StyleSpan ||
                            span is android.text.style.ForegroundColorSpan ||
                            span is android.text.style.AbsoluteSizeSpan ||
                            span is android.text.style.TypefaceSpan ||
                            span.javaClass.simpleName.contains("Stroke") ||
                            span.javaClass.simpleName.contains("Font")
                        }

                        if (hasFormatSpans) {
                            // 转换为SpannableString并序列化
                            val spannableString = if (currentText is android.text.SpannableString) {
                                currentText
                            } else {
                                android.text.SpannableString(currentText)
                            }

                            val textFormatManager = com.example.castapp.utils.TextFormatManager(textView.context)
                            val serializedData = textFormatManager.serializeSpannableStringToJson(spannableString)
                            AppLog.d("【窗口容器View】检测到富文本格式，Span数量: ${spans.size}, 序列化数据长度: ${serializedData.length}")
                            serializedData
                        } else {
                            AppLog.d("【窗口容器View】文本无格式Span，跳过富文本序列化")
                            null
                        }
                    } else {
                        AppLog.d("【窗口容器View】文本不是Spannable类型或为空，跳过富文本序列化")
                        null
                    }
                } catch (e: Exception) {
                    AppLog.e("【窗口容器View】获取富文本格式失败", e)
                    null
                }

                // 获取TextView级别的属性
                val textAlignment = textView.getTextGravity()
                val windowColorState = textView.getWindowBackgroundColorState()

                // 获取行间距（TextView级别属性）
                val lineSpacing = try {
                    // 从TextView获取当前行间距设置
                    val lineSpacingExtra = textView.lineSpacingExtra
                    val density = textView.resources.displayMetrics.density
                    lineSpacingExtra / density // 转换为dp值
                } catch (_: Exception) {
                    0.0f
                }

                // 🎯 关键修复：获取实际换行文本（用于精准换行同步）
                val actualLineTexts = try {
                    textView.getActualLineTexts()
                } catch (e: Exception) {
                    AppLog.e("【窗口容器View】获取实际换行文本失败", e)
                    emptyList()
                }

                // 🎯 关键修复：获取当前设备（接收端）的DPI密度信息
                val receiverDpiInfo = try {
                    val dpiManager = com.example.castapp.utils.DpiDensityManager(textView.context)
                    dpiManager.getCurrentDeviceDpiInfo().toMap()
                } catch (e: Exception) {
                    AppLog.e("【窗口容器View】获取DPI密度信息失败", e)
                    emptyMap()
                }

                // 🎯 新增：获取当前窗口尺寸信息
                val currentWidth = this.layoutParams?.width ?: this.width
                val currentHeight = this.layoutParams?.height ?: this.height

                mapOf(
                    "textContent" to textContent,
                    "richTextData" to (richTextData ?: ""),
                    "hasRichText" to (richTextData != null),
                    "lineSpacing" to lineSpacing,
                    "textAlignment" to textAlignment,
                    "windowColorEnabled" to windowColorState.first,
                    "windowColor" to windowColorState.second,
                    "actualLineTexts" to actualLineTexts, // 🎯 新增：实际换行文本
                    "receiverDpiInfo" to receiverDpiInfo, // 🎯 关键修复：接收端DPI密度信息
                    // 🎯 新增：窗口尺寸信息，用于同步遥控端的尺寸调整
                    "windowWidth" to currentWidth,
                    "windowHeight" to currentHeight
                )
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】获取文本格式数据失败", e)
            null
        }
    }

    /**
     * 🎯 新增：验证自然换行结果是否与接收端一致
     */
    @Suppress("UNUSED_PARAMETER")
    private fun verifyNaturalLineBreak(actualLineTexts: List<*>?, formatData: Map<String, Any>) {
        try {
            // 等待TextView布局完成
            post {
                try {
                    val textView = textWindowView
                    if (textView != null && actualLineTexts != null && actualLineTexts.isNotEmpty()) {
                        // 获取遥控端的实际换行结果
                        val remoteLineTexts = textView.getActualLineTexts()
                        val expectedLineTexts = actualLineTexts.mapNotNull { it as? String }

                        AppLog.d("【窗口容器View】🎯 自然换行验证:")
                        AppLog.d("【窗口容器View】  接收端换行: $expectedLineTexts")
                        AppLog.d("【窗口容器View】  遥控端换行: $remoteLineTexts")

                        // 检查换行是否一致
                        val isConsistent = remoteLineTexts.size == expectedLineTexts.size &&
                                remoteLineTexts.zip(expectedLineTexts).all { (remote, expected) ->
                                    remote.trim() == expected.trim()
                                }

                        if (isConsistent) {
                            AppLog.d("【窗口容器View】✅ 自然换行结果与接收端一致，DPI调整成功")
                        } else {
                            AppLog.w("【窗口容器View】⚠️ 自然换行结果与接收端不一致，可能需要进一步调整DPI参数")
                            AppLog.w("【窗口容器View】  差异详情:")
                            expectedLineTexts.forEachIndexed { index, expected ->
                                val remote = remoteLineTexts.getOrNull(index) ?: ""
                                if (remote.trim() != expected.trim()) {
                                    AppLog.w("【窗口容器View】    第${index + 1}行: 期望\"$expected\" 实际\"$remote\"")
                                }
                            }

                            // 🎯 如果自然换行不一致，可以考虑微调字体大小或使用强制换行作为后备
                            AppLog.d("【窗口容器View】🎯 考虑使用强制换行作为后备方案")
                        }
                    }
                } catch (e: Exception) {
                    AppLog.e("【窗口容器View】自然换行验证异常", e)
                }
            }
        } catch (e: Exception) {
            AppLog.e("【窗口容器View】自然换行验证失败", e)
        }
    }

    /**
     * 🎯 新增：应用DPI密度调整到窗口尺寸
     * 确保遥控端窗口尺寸能够容纳与接收端相同的文字内容，实现自然换行
     */
    private fun applyDpiAdjustmentToWindowSize(
        originalWidth: Int,
        originalHeight: Int,
        textFormatData: Map<String, Any>
    ): Pair<Int, Int> {
        return try {
            // 获取接收端DPI信息
            @Suppress("UNCHECKED_CAST")
            val receiverDpiData = textFormatData["receiverDpiInfo"] as? Map<String, Any>
            if (receiverDpiData == null || receiverDpiData.isEmpty()) {
                AppLog.d("【DPI窗口调整】未找到接收端DPI信息，跳过尺寸调整")
                return Pair(originalWidth, originalHeight)
            }

            val receiverDpiInfo = com.example.castapp.utils.DpiDensityManager.DpiInfo.fromMap(receiverDpiData)
            if (receiverDpiInfo == null) {
                AppLog.d("【DPI窗口调整】DPI信息解析失败，跳过尺寸调整")
                return Pair(originalWidth, originalHeight)
            }

            // 获取当前设备（遥控端）DPI信息
            val dpiManager = com.example.castapp.utils.DpiDensityManager(context)
            val currentDpiInfo = dpiManager.getCurrentDeviceDpiInfo()

            // 计算DPI密度比值
            val dpiRatio = dpiManager.calculateDpiRatio(receiverDpiInfo, currentDpiInfo)

            // 检查是否需要调整
            if (dpiManager.isDpiSimilar(receiverDpiInfo, currentDpiInfo, 0.05f)) {
                AppLog.d("【DPI窗口调整】设备DPI密度相近，无需尺寸调整")
                return Pair(originalWidth, originalHeight)
            }

            // 🎯 关键修复：应用DPI比值的倒数到窗口尺寸
            // 如果接收端DPI更高，遥控端需要更小的窗口来模拟接收端的换行效果
            val windowSizeRatio = 1.0f / dpiRatio
            val adjustedWidth = (originalWidth * windowSizeRatio).toInt().coerceAtLeast(1)
            val adjustedHeight = (originalHeight * windowSizeRatio).toInt().coerceAtLeast(1)

            AppLog.d("【DPI窗口调整】窗口尺寸DPI调整:")
            AppLog.d("【DPI窗口调整】  接收端DPI: ${receiverDpiInfo.getDensityCategory()}")
            AppLog.d("【DPI窗口调整】  遥控端DPI: ${currentDpiInfo.getDensityCategory()}")
            AppLog.d("【DPI窗口调整】  DPI比值: $dpiRatio")
            AppLog.d("【DPI窗口调整】  窗口尺寸比值: $windowSizeRatio (1/DPI比值)")
            AppLog.d("【DPI窗口调整】  原始尺寸: ${originalWidth}×${originalHeight}")
            AppLog.d("【DPI窗口调整】  调整后尺寸: ${adjustedWidth}×${adjustedHeight}")
            AppLog.d("【DPI窗口调整】  🎯 通过缩小窗口模拟接收端的换行效果")

            Pair(adjustedWidth, adjustedHeight)

        } catch (e: Exception) {
            AppLog.e("【DPI窗口调整】DPI尺寸调整失败", e)
            Pair(originalWidth, originalHeight)
        }
    }

    /**
     * 🎯 核心方法：应用容器级整体缩放（真正的放大镜效果）
     * 🎯 新方案：视图层统一处理所有缩放，避免文字重新布局
     * 这是实现遥控端与接收端缩放同步的核心方法
     */
    private fun applyContainerScale(scaleFactor: Float, remoteControlScale: Double) {
        try {
            // 🎯 关键：计算总缩放因子，包含远程控制缩放和接收端缩放
            val totalScale = (scaleFactor * remoteControlScale).toFloat()

            // 🎯 应用整体缩放到容器，模拟接收端的 container.scaleX/scaleY 效果
            scaleX = totalScale
            scaleY = totalScale

            // 🎯 设置缩放中心点为容器中心，确保缩放效果自然
            pivotX = width / 2f
            pivotY = height / 2f

            AppLog.d("【窗口容器View】🎯 应用统一缩放:")
            AppLog.d("【窗口容器View】🎯   接收端缩放: ${scaleFactor}x")
            AppLog.d("【窗口容器View】🎯   远程控制缩放: ${remoteControlScale}x")
            AppLog.d("【窗口容器View】🎯   总缩放因子: ${totalScale}x")
            AppLog.d("【窗口容器View】🎯   缩放中心点: (${pivotX}, ${pivotY})")
            AppLog.d("【窗口容器View】🎯   容器原始尺寸: ${width}×${height}")
            AppLog.d("【窗口容器View】🎯   这将产生纯粹的视觉缩放，文字相对位置不变")

            // 🎯 验证缩放是否正确应用
            if (scaleX == totalScale && scaleY == totalScale) {
                AppLog.d("【窗口容器View】✅ 统一缩放应用成功")
            } else {
                AppLog.w("【窗口容器View】❌ 统一缩放应用异常: 期望=${totalScale}, 实际=(${scaleX}, ${scaleY})")
            }

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】应用容器缩放失败", e)
        }
    }
}
