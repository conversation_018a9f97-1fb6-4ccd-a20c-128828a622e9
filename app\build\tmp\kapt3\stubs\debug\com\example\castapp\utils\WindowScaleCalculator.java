package com.example.castapp.utils;

/**
 * 🪟 投屏窗口容器缩放计算工具类
 * 负责计算远程接收端控制窗口中投屏窗口容器的可视化参数
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0014\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\b\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007H\u0002J\u000e\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bJR\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\r2\u0006\u0010\u0010\u001a\u00020\u00112.\b\u0002\u0010\u0012\u001a(\u0012\u0004\u0012\u00020\u0014\u0012\u001c\u0012\u001a\u0012\u0004\u0012\u00020\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u00130\u0015\u0018\u00010\u0013J*\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00040\u00152\u0006\u0010\u0017\u001a\u00020\u00042\u0006\u0010\u0018\u001a\u00020\u00042\u0006\u0010\u0010\u001a\u00020\u0011J\u0016\u0010\u0019\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u00042\u0006\u0010\u0010\u001a\u00020\u0011J*\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00040\u00152\u0006\u0010\u001c\u001a\u00020\u00042\u0006\u0010\u001d\u001a\u00020\u00042\u0006\u0010\u0010\u001a\u00020\u0011J\u0016\u0010\u001e\u001a\u00020\u00042\u0006\u0010\u001f\u001a\u00020\u00042\u0006\u0010\u0010\u001a\u00020\u0011J\u001e\u0010 \u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020\"\u00a8\u0006$"}, d2 = {"Lcom/example/castapp/utils/WindowScaleCalculator;", "", "()V", "applyBoundaryAlignment", "", "coordinate", "isHorizontal", "", "calculateActualDisplayBounds", "", "visualizationData", "Lcom/example/castapp/model/WindowVisualizationData;", "calculateWindowVisualizationData", "", "windowInfoList", "Lcom/example/castapp/model/CastWindowInfo;", "remoteControlScale", "", "textContentMap", "", "", "Lkotlin/Pair;", "convertActualToRemoteCoordinates", "actualX", "actualY", "convertActualToRemoteScaleFactor", "actualScaleFactor", "convertRemoteToActualCoordinates", "remoteX", "remoteY", "convertRemoteToActualScaleFactor", "remoteScaleFactor", "validateVisualizationData", "containerWidth", "", "containerHeight", "app_debug"})
public final class WindowScaleCalculator {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.utils.WindowScaleCalculator INSTANCE = null;
    
    private WindowScaleCalculator() {
        super();
    }
    
    /**
     * 批量计算投屏窗口容器的可视化数据
     *
     * @param windowInfoList 投屏窗口信息列表
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @param textContentMap 文本窗口内容映射表（可选）
     * @return 可视化数据列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.model.WindowVisualizationData> calculateWindowVisualizationData(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList, double remoteControlScale, @org.jetbrains.annotations.Nullable()
    java.util.Map<java.lang.String, ? extends kotlin.Pair<java.lang.String, ? extends java.util.Map<java.lang.String, ? extends java.lang.Object>>> textContentMap) {
        return null;
    }
    
    /**
     * 计算窗口容器在远程控制窗口中的实际显示区域
     * 考虑窗口旋转、缩放等变换
     *
     * @param visualizationData 可视化数据
     * @return 实际显示区域的边界矩形 (left, top, right, bottom)
     */
    @org.jetbrains.annotations.NotNull()
    public final float[] calculateActualDisplayBounds(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.WindowVisualizationData visualizationData) {
        return null;
    }
    
    /**
     * 验证可视化数据的有效性
     *
     * @param visualizationData 可视化数据
     * @param containerWidth 容器宽度
     * @param containerHeight 容器高度
     * @return 是否有效
     */
    public final boolean validateVisualizationData(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.WindowVisualizationData visualizationData, int containerWidth, int containerHeight) {
        return false;
    }
    
    /**
     * 🎯 将遥控端坐标转换为接收端实际屏幕坐标（边界对齐优化版）
     * @param remoteX 遥控端X坐标（远程控制窗口坐标系）
     * @param remoteY 遥控端Y坐标（远程控制窗口坐标系）
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @return 接收端实际屏幕坐标 Pair(actualX, actualY)
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Float, java.lang.Float> convertRemoteToActualCoordinates(float remoteX, float remoteY, double remoteControlScale) {
        return null;
    }
    
    /**
     * 🎯 新增：边界对齐算法
     * 检测坐标是否接近屏幕边界，如果是则进行精确对齐
     */
    private final float applyBoundaryAlignment(float coordinate, boolean isHorizontal) {
        return 0.0F;
    }
    
    /**
     * 🎯 将接收端实际屏幕坐标转换为遥控端坐标
     * @param actualX 接收端实际X坐标
     * @param actualY 接收端实际Y坐标
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @return 遥控端坐标 Pair(remoteX, remoteY)
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Float, java.lang.Float> convertActualToRemoteCoordinates(float actualX, float actualY, double remoteControlScale) {
        return null;
    }
    
    /**
     * 🎯 将遥控端缩放因子转换为接收端实际缩放因子
     * @param remoteScaleFactor 遥控端缩放因子（相对于可视化容器的缩放）
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @return 接收端实际缩放因子
     */
    public final float convertRemoteToActualScaleFactor(float remoteScaleFactor, double remoteControlScale) {
        return 0.0F;
    }
    
    /**
     * 🎯 将接收端实际缩放因子转换为遥控端缩放因子
     * @param actualScaleFactor 接收端实际缩放因子
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @return 遥控端缩放因子
     */
    public final float convertActualToRemoteScaleFactor(float actualScaleFactor, double remoteControlScale) {
        return 0.0F;
    }
}