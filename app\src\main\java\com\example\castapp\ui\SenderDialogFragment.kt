package com.example.castapp.ui

import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.Context
import androidx.core.content.edit
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.SeekBar
import android.widget.Switch
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.manager.PermissionManager
import com.example.castapp.manager.ResolutionManager
import com.example.castapp.manager.StateManager
import com.example.castapp.model.Connection
import com.example.castapp.network.NetworkUtils
import com.example.castapp.remote.RemoteSenderServer
import com.example.castapp.ui.adapter.ConnectionAdapter
import com.example.castapp.viewmodel.SenderViewModel
import java.util.concurrent.CopyOnWriteArrayList
import com.example.castapp.utils.AppLog

/**
 * 发送端设置对话框
 */
class SenderDialogFragment : DialogFragment() {

    private val viewModel: SenderViewModel by activityViewModels()

    private lateinit var connectionsRecyclerView: RecyclerView
    private lateinit var connectionCountText: TextView
    private lateinit var addReceiverButton: Button
    private lateinit var closeButton: ImageButton
    private lateinit var bitrateSeekBar: SeekBar
    private lateinit var bitrateValueText: TextView
    private lateinit var resolutionSeekBar: SeekBar
    private lateinit var resolutionValueText: TextView
    private lateinit var resolutionInfoText: TextView

    private lateinit var mediaAudioVolumeLayout: LinearLayout
    private lateinit var mediaAudioVolumeSeekBar: SeekBar
    private lateinit var mediaAudioVolumeText: TextView
    private lateinit var micAudioVolumeLayout: LinearLayout
    private lateinit var micAudioVolumeSeekBar: SeekBar
    private lateinit var micAudioVolumeText: TextView
    private lateinit var remoteControlLayout: LinearLayout
    private lateinit var remoteControlSwitch: Switch
    private lateinit var remoteControlStatusText: TextView
    private lateinit var connectionAdapter: ConnectionAdapter
    private val connections = CopyOnWriteArrayList<Connection>()
    private lateinit var permissionHelper: PermissionManager.PermissionHelper
    private lateinit var resolutionManager: ResolutionManager
    private lateinit var stateManager: StateManager
    private var isResolutionAdjusting = false
    private var lastResolutionAdjustTime = 0L
    private val resolutionAdjustMinInterval = 1000L
    private var pendingResolutionScale = -1

    // 远程被控相关
    private lateinit var remoteSenderServer: RemoteSenderServer
    private var localIpAddress: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.DialogTheme)
        permissionHelper = PermissionManager.PermissionHelper(this)
        resolutionManager = ResolutionManager.getInstance(requireContext())
        stateManager = StateManager.getInstance(requireActivity().application)
        remoteSenderServer = RemoteSenderServer.getInstance()
        localIpAddress = NetworkUtils.getLocalIpAddress()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_send, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setupRecyclerView()
        setupBitrateControl()
        setupResolutionControl()
        setupAudioVolumeControls()
        setupRemoteControlSettings()
        setupClickListeners()
        observeViewModel()
    }

    private fun initViews(view: View) {
        connectionsRecyclerView = view.findViewById(R.id.connections_container)
        connectionCountText = view.findViewById(R.id.connection_count_text)
        addReceiverButton = view.findViewById(R.id.start_screen_share_button)
        closeButton = view.findViewById(R.id.close_dialog_button)
        bitrateSeekBar = view.findViewById(R.id.bitrate_seekbar)
        bitrateValueText = view.findViewById(R.id.bitrate_value_text)
        resolutionSeekBar = view.findViewById(R.id.resolution_seekbar)
        resolutionValueText = view.findViewById(R.id.resolution_value_text)
        resolutionInfoText = view.findViewById(R.id.resolution_info_text)
        mediaAudioVolumeLayout = view.findViewById(R.id.media_audio_volume_layout)
        mediaAudioVolumeSeekBar = view.findViewById(R.id.media_audio_volume_seekbar)
        mediaAudioVolumeText = view.findViewById(R.id.media_audio_volume_text)
        micAudioVolumeLayout = view.findViewById(R.id.mic_audio_volume_layout)
        micAudioVolumeSeekBar = view.findViewById(R.id.mic_audio_volume_seekbar)
        micAudioVolumeText = view.findViewById(R.id.mic_audio_volume_text)
        remoteControlLayout = view.findViewById(R.id.remote_control_layout)
        remoteControlSwitch = view.findViewById(R.id.remote_control_switch)
        remoteControlStatusText = view.findViewById(R.id.remote_control_status_text)
    }

    private fun observeViewModel() {
        viewModel.connections.observe(viewLifecycleOwner) { connectionList ->
            updateConnectionsList(connectionList)
            connectionCountText.text = getString(R.string.connection_count_format, connectionList.size)
            // 🚀 移除全量刷新：不再调用syncCastingStates()，改用精准更新机制
        }
        // 🚀 移除全量刷新：不再监听castingConnections，改用精准更新机制
        viewModel.showAddReceiverDialog.observe(viewLifecycleOwner) { shouldShow ->
            if (shouldShow) {
                showAddReceiverDialog()
                viewModel.hideAddReceiverDialog()
            }
        }
        // 🔥 关键修复：移除Toast消息监听，让MainActivity统一处理
        // viewModel.toastMessage 现在由MainActivity监听处理

        // 🔥 关键修复：移除权限请求监听，让MainActivity统一处理
        // viewModel.requestMediaProjection 现在由MainActivity监听处理
        // 这样即使SenderDialogFragment关闭，权限请求也能正常工作
        viewModel.resolutionAdjustmentState.observe(viewLifecycleOwner) { conn ->
            handleResolutionAdjustmentState(conn)
        }

        // 观察远程控制UI更新
        viewModel.remoteControlUIUpdate.observe(viewLifecycleOwner) { update ->
            handleRemoteControlUIUpdate(update)
        }
    }

    private fun updateConnectionsList(newConnectionList: List<Connection>) {
        if (connections.isEmpty()) {
            connections.addAll(newConnectionList)
            connectionAdapter.notifyItemRangeInserted(0, newConnectionList.size)
            // 🚀 新增：为新连接注册精准状态监听器
            setupPreciseStateUpdateListenersForNewConnections(newConnectionList)
            return
        }
        val newConnectionMap = newConnectionList.associateBy { it.connectionId }
        val toRemove = mutableListOf<Int>()
        connections.forEachIndexed { index, existing ->
            if (!newConnectionMap.containsKey(existing.connectionId)) {
                toRemove.add(index)
            }
        }
        toRemove.reversed().forEach { index ->
            val removedConnection = connections.removeAt(index)
            connectionAdapter.notifyItemRemoved(index)
            // 🚀 新增：移除精准状态监听器
            stateManager.unregisterPreciseStateChangeListener(removedConnection.connectionId)
        }
        newConnectionList.forEach { newConnection ->
            val existingIndex = connections.indexOfFirst { it.connectionId == newConnection.connectionId }
            if (existingIndex == -1) {
                // 新连接，添加到列表
                connections.add(newConnection)
                connectionAdapter.notifyItemInserted(connections.size - 1)
                // 🚀 新增：为新连接注册精准状态监听器
                registerPreciseStateUpdateListener(newConnection.connectionId)
            } else {
                // 现有连接，检查是否需要更新
                val existingConnection = connections[existingIndex]
                if (existingConnection != newConnection) {
                    // 🚀 优化：使用精准更新而不是notifyItemChanged
                    connectionAdapter.updateConnectionPrecisely(newConnection.connectionId, newConnection)
                    AppLog.d("连接信息已精准更新: ${newConnection.getDisplayText()}")
                }
            }
        }
    }

    /**
     * 🚀 重构：移除全量刷新方法，改用精准更新机制
     * 原syncCastingStates()方法导致全量刷新，现在通过精准状态监听器实现局部更新
     */
    // private fun syncCastingStates() - 已移除，改用精准更新机制

    private fun setupRecyclerView() {
        connectionAdapter = ConnectionAdapter(
            connections = connections,
            stateManager = stateManager,
            onCastToggle = { connection, isEnabled -> handleCastToggle(connection, isEnabled) },
            onMediaAudioToggle = { connection, isEnabled ->
                handleAudioToggle(
                    connection,
                    isEnabled,
                    "media"
                )
            },
            onMicAudioToggle = { connection, isEnabled ->
                handleAudioToggle(
                    connection,
                    isEnabled,
                    "mic"
                )
            },
            onEditConnection = { connection -> showEditConnectionDialog(connection) },
            onConfirmRemoveConnection = { connection -> showRemoveConnectionDialog(connection) }
        )
        connectionsRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = connectionAdapter
        }

        // 🚀 新增：设置精准状态更新监听器
        setupPreciseStateUpdateListeners()
    }

    private fun setupBitrateControl() {
        val sharedPrefs = requireContext().getSharedPreferences("cast_settings", Context.MODE_PRIVATE)
        val savedBitrate = sharedPrefs.getInt("bitrate_mbps", 20)
        bitrateSeekBar.progress = (savedBitrate - 5).coerceAtLeast(0)
        updateBitrateDisplay(savedBitrate)

        val updateDelay = 300L
        var lastUpdateTime: Long
        var pendingBitrate: Int

        bitrateSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val bitrateMbps = progress + 5
                    updateBitrateDisplay(bitrateMbps)
                    val currentTime = System.currentTimeMillis()
                    pendingBitrate = bitrateMbps
                    lastUpdateTime = currentTime
                    bitrateSeekBar.postDelayed({
                        if (System.currentTimeMillis() - lastUpdateTime >= updateDelay && pendingBitrate == bitrateMbps) {
                            applyBitrateChange(bitrateMbps)
                            sharedPrefs.edit { putInt("bitrate_mbps", bitrateMbps) }
                        }
                    }, updateDelay)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val bitrateMbps = (seekBar?.progress ?: 15) + 5
                pendingBitrate = -1
                applyBitrateChange(bitrateMbps)
                sharedPrefs.edit { putInt("bitrate_mbps", bitrateMbps) }
            }
        })
    }

    private fun updateBitrateDisplay(bitrateMbps: Int) {
        bitrateValueText.text = getString(R.string.bitrate_format, bitrateMbps)
    }

    private fun applyBitrateChange(bitrateMbps: Int) {
        viewModel.updateBitRate(bitrateMbps)
        // 通知远程控制端UI更新
        notifyRemoteControlClients("bitrate_mbps", bitrateMbps)
    }

    /**
     * 只设置码率控制监听器，不重新设置值
     * 用于远程同步后恢复监听器
     */
    private fun setupBitrateControlListener() {
        val sharedPrefs = requireContext().getSharedPreferences("cast_settings", Context.MODE_PRIVATE)
        val updateDelay = 300L
        var lastUpdateTime: Long
        var pendingBitrate: Int

        bitrateSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val bitrateMbps = progress + 5
                    updateBitrateDisplay(bitrateMbps)
                    val currentTime = System.currentTimeMillis()
                    pendingBitrate = bitrateMbps
                    lastUpdateTime = currentTime
                    bitrateSeekBar.postDelayed({
                        if (System.currentTimeMillis() - lastUpdateTime >= updateDelay && pendingBitrate == bitrateMbps) {
                            applyBitrateChange(bitrateMbps)
                            sharedPrefs.edit { putInt("bitrate_mbps", bitrateMbps) }
                        }
                    }, updateDelay)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val bitrateMbps = (seekBar?.progress ?: 15) + 5
                pendingBitrate = -1
                applyBitrateChange(bitrateMbps)
                sharedPrefs.edit { putInt("bitrate_mbps", bitrateMbps) }
            }
        })
    }



    private fun setupResolutionControl() {
        // 🔥 修复：优先从SharedPreferences恢复分辨率状态，确保与远程控制状态一致
        val sharedPrefs = requireContext().getSharedPreferences("cast_settings", Context.MODE_PRIVATE)
        val savedScale = sharedPrefs.getInt("resolution_scale", -1)
        val currentScale = if (savedScale > 0) {
            // 如果SharedPreferences中有保存的值，使用它并同步到ResolutionManager
            resolutionManager.setResolutionScale(savedScale)
            savedScale
        } else {
            // 否则使用ResolutionManager的当前值
            resolutionManager.getCurrentResolutionScale()
        }

        resolutionSeekBar.progress = currentScale - 1
        updateResolutionDisplay(currentScale)

        resolutionSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val scalePercent = progress + 1
                    updateResolutionDisplay(scalePercent)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val scalePercent = (seekBar?.progress ?: 99) + 1
                applyResolutionChange(scalePercent)
            }
        })
    }

    @SuppressLint("SetTextI18n")
    private fun updateResolutionDisplay(scalePercent: Int) {
        resolutionValueText.text = "${scalePercent}%"
        val (currentWidth, currentHeight) = resolutionManager.calculateResolution(scalePercent)
        val (originalWidth, originalHeight) = resolutionManager.getOriginalScreenResolution()
        resolutionInfoText.text = "原始: ${originalWidth}×${originalHeight} | 当前: ${currentWidth}×${currentHeight}"
    }

    /**
     * 应用分辨率变化（带防抖和重试机制）
     */
    private fun applyResolutionChange(scalePercent: Int) {
        val currentTime = System.currentTimeMillis()

        // 防抖检查：如果正在调整或距离上次调整时间太短，则延迟处理
        if (isResolutionAdjusting) {
            AppLog.d("分辨率正在调整中，缓存新请求: $scalePercent%")
            pendingResolutionScale = scalePercent
            return
        }

        if (currentTime - lastResolutionAdjustTime < resolutionAdjustMinInterval) {
            val remainingTime = resolutionAdjustMinInterval - (currentTime - lastResolutionAdjustTime)
            AppLog.d("分辨率调整间隔太短，延迟${remainingTime}ms后执行: $scalePercent%")
            pendingResolutionScale = scalePercent

            resolutionSeekBar.postDelayed({
                if (pendingResolutionScale == scalePercent && !isResolutionAdjusting) {
                    applyResolutionChangeInternal(scalePercent)
                }
            }, remainingTime)
            return
        }

        applyResolutionChangeInternal(scalePercent)
    }

    /**
     * 内部分辨率调整实现
     */
    private fun applyResolutionChangeInternal(scalePercent: Int) {
        if (!resolutionManager.setResolutionScale(scalePercent)) {
            AppLog.w("分辨率缩放设置失败: $scalePercent%")
            showToast("分辨率设置超出有效范围")
            return
        }

        // 设置调整状态
        isResolutionAdjusting = true
        lastResolutionAdjustTime = System.currentTimeMillis()
        pendingResolutionScale = -1

        // 禁用滑动条，防止重复调整
        resolutionSeekBar.isEnabled = false

        // 显示调整中的状态提示
        resolutionInfoText.text = "正在调整分辨率... 请稍候"
        resolutionInfoText.setTextColor(resources.getColor(android.R.color.holo_orange_dark, null))

        AppLog.d("开始分辨率调整: $scalePercent%")
        viewModel.updateResolutionScale(scalePercent)

        // 通知远程控制端UI更新
        notifyRemoteControlClients("resolution_scale", scalePercent)
    }

    /**
     * 处理分辨率调整状态变化
     */
    private fun handleResolutionAdjustmentState(state: SenderViewModel.ResolutionAdjustmentState) {
        when (state) {
            is SenderViewModel.ResolutionAdjustmentState.InProgress -> {
                // 调整进行中
                isResolutionAdjusting = true
                resolutionSeekBar.isEnabled = false
                resolutionInfoText.text = "正在调整分辨率... 请稍候"
                resolutionInfoText.setTextColor(resources.getColor(android.R.color.holo_orange_dark, null))
                AppLog.d("分辨率调整进行中: ${state.scalePercent}%")
            }

            is SenderViewModel.ResolutionAdjustmentState.Success -> {
                // 调整成功
                isResolutionAdjusting = false
                resolutionSeekBar.isEnabled = true

                val (currentWidth, currentHeight) = resolutionManager.calculateResolution(state.scalePercent)
                val (originalWidth, originalHeight) = resolutionManager.getOriginalScreenResolution()
                resolutionInfoText.text = getString(R.string.resolution_info_format, originalWidth, originalHeight, currentWidth, currentHeight)
                resolutionInfoText.setTextColor(resources.getColor(android.R.color.darker_gray, null))

                AppLog.d("分辨率调整成功: ${state.scalePercent}%")

                // 检查是否有待处理的调整请求
                if (pendingResolutionScale > 0 && pendingResolutionScale != state.scalePercent) {
                    AppLog.d("执行待处理的分辨率调整: $pendingResolutionScale%")
                    val pendingScale = pendingResolutionScale
                    pendingResolutionScale = -1
                    resolutionSeekBar.postDelayed({
                        applyResolutionChangeInternal(pendingScale)
                    }, 500) // 短暂延迟确保状态稳定
                }
            }

            is SenderViewModel.ResolutionAdjustmentState.Failed -> {
                // 调整失败，启用重试机制
                AppLog.w("分辨率调整失败: ${state.scalePercent}%, 错误: ${state.error}")

                if (state.retryCount < 2) { // 最多重试2次
                    AppLog.d("准备重试分辨率调整: ${state.scalePercent}%, 重试次数: ${state.retryCount + 1}")
                    resolutionInfoText.text = getString(R.string.resolution_retry_format, state.retryCount + 1, 2)

                    resolutionSeekBar.postDelayed({
                        viewModel.updateResolutionScale(state.scalePercent, state.retryCount + 1)
                    }, 1000) // 1秒后重试
                } else {
                    // 重试次数用完，恢复UI状态
                    isResolutionAdjusting = false
                    resolutionSeekBar.isEnabled = true

                    val (currentWidth, currentHeight) = resolutionManager.calculateResolution(state.scalePercent)
                    val (originalWidth, originalHeight) = resolutionManager.getOriginalScreenResolution()
                    resolutionInfoText.text = getString(R.string.resolution_info_format, originalWidth, originalHeight, currentWidth, currentHeight)
                    resolutionInfoText.setTextColor(resources.getColor(android.R.color.holo_red_dark, null))

                    showToast("分辨率调整失败: ${state.error}")

                    // 恢复到之前的分辨率设置
                    resolutionSeekBar.postDelayed({
                        resolutionInfoText.setTextColor(resources.getColor(android.R.color.darker_gray, null))
                    }, 3000)
                }
            }
        }
    }

    /**
     * 只设置分辨率控制监听器，不重新设置值
     * 用于远程同步后恢复监听器
     */
    private fun setupResolutionControlListener() {
        resolutionSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val scalePercent = progress + 1
                    updateResolutionDisplay(scalePercent)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val scalePercent = (seekBar?.progress ?: 99) + 1
                applyResolutionChange(scalePercent)
            }
        })
    }

    private fun setupAudioVolumeControls() {
        val sharedPrefs = requireContext().getSharedPreferences("cast_settings", Context.MODE_PRIVATE)
        setupVolumeControl(mediaAudioVolumeSeekBar, mediaAudioVolumeText, "media_audio_volume", sharedPrefs) { volume ->
            viewModel.updateMediaAudioVolume(volume)
        }
        setupVolumeControl(micAudioVolumeSeekBar, micAudioVolumeText, "mic_audio_volume", sharedPrefs) { volume ->
            viewModel.updateMicAudioVolume(volume)
        }
    }

    companion object {
        private const val DEFAULT_AUDIO_VOLUME = 80
    }

    private fun setupVolumeControl(
        seekBar: SeekBar,
        textView: TextView,
        prefKey: String,
        sharedPrefs: android.content.SharedPreferences,
        applyChange: (Int) -> Unit
    ) {
        val savedVolume = sharedPrefs.getInt(prefKey, DEFAULT_AUDIO_VOLUME)
        seekBar.progress = savedVolume
        textView.text = getString(R.string.volume_format, savedVolume)

        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) textView.text = getString(R.string.volume_format, progress)
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val volume = seekBar?.progress ?: DEFAULT_AUDIO_VOLUME
                applyChange(volume)
                sharedPrefs.edit { putInt(prefKey, volume) }

                // 通知远程控制端UI更新
                val volumeType = when (prefKey) {
                    "media_audio_volume" -> "media_volume"
                    "mic_audio_volume" -> "mic_volume"
                    else -> prefKey
                }
                notifyRemoteControlClients(volumeType, volume)
            }
        })
    }

    /**
     * 只设置音量控制监听器，不重新设置值
     * 用于远程同步后恢复监听器
     */
    private fun setupAudioVolumeControlListeners() {
        val sharedPrefs = requireContext().getSharedPreferences("cast_settings", Context.MODE_PRIVATE)

        // 只设置监听器，不改变当前值
        setupVolumeControlListener(mediaAudioVolumeSeekBar, mediaAudioVolumeText, "media_audio_volume", sharedPrefs) { volume ->
            viewModel.updateMediaAudioVolume(volume)
        }
        setupVolumeControlListener(micAudioVolumeSeekBar, micAudioVolumeText, "mic_audio_volume", sharedPrefs) { volume ->
            viewModel.updateMicAudioVolume(volume)
        }
    }

    private fun setupVolumeControlListener(
        seekBar: SeekBar,
        textView: TextView,
        prefKey: String,
        sharedPrefs: android.content.SharedPreferences,
        applyChange: (Int) -> Unit
    ) {
        // 只设置监听器，不改变当前的progress和text值
        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) textView.text = getString(R.string.volume_format, progress)
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val volume = seekBar?.progress ?: DEFAULT_AUDIO_VOLUME
                applyChange(volume)
                sharedPrefs.edit { putInt(prefKey, volume) }

                // 🔥 关键修复：通知远程控制端UI更新
                val volumeType = when (prefKey) {
                    "media_audio_volume" -> "media_volume"
                    "mic_audio_volume" -> "mic_volume"
                    else -> prefKey
                }
                notifyRemoteControlClients(volumeType, volume)
            }
        })
    }

    private fun setupRemoteControlSettings() {
        val sharedPrefs = requireContext().getSharedPreferences("cast_settings", Context.MODE_PRIVATE)

        // 恢复远程被控开关状态
        val isRemoteControlEnabled = sharedPrefs.getBoolean("remote_control_enabled", false)
        remoteControlSwitch.isChecked = isRemoteControlEnabled

        // 更新IP地址显示
        updateRemoteControlStatusText()

        // 🔥 关键修复：如果之前是开启状态，检查服务器是否已运行，避免重复启动
        if (isRemoteControlEnabled) {
            if (!remoteSenderServer.isServerRunning()) {
                AppLog.d("【SenderDialogFragment】远程被控开关已开启，但服务器未运行，启动服务器")
                startRemoteControlServer()
            } else {
                AppLog.d("【SenderDialogFragment】远程被控服务器已在运行，跳过重复启动")
                // 服务器已运行，只需要更新UI状态
                updateRemoteControlStatusText()
            }
        }

        // 设置开关监听器
        remoteControlSwitch.setOnCheckedChangeListener { _, isChecked ->
            sharedPrefs.edit { putBoolean("remote_control_enabled", isChecked) }

            if (isChecked) {
                startRemoteControlServer()
            } else {
                stopRemoteControlServer()
            }

            updateRemoteControlStatusText()
        }

        // 设置远程控制服务器状态监听器
        remoteSenderServer.setOnConnectionStateChangedListener { isRunning ->
            activity?.runOnUiThread {
                updateRemoteControlStatusText()
                if (!isRunning && remoteControlSwitch.isChecked) {
                    // 服务器意外停止，显示错误信息
                    Toast.makeText(requireContext(), getString(R.string.remote_control_server_start_failed), Toast.LENGTH_SHORT).show()
                }
            }
        }

        // 注意：远程控制回调已在SenderViewModel中设置，确保即使窗口关闭也能响应远程控制
        // 这里不再设置UI更新回调，避免与ViewModel中的回调冲突
        AppLog.d("远程控制回调已由SenderViewModel管理，UI层不再重复设置")
    }

    private fun startRemoteControlServer() {
        // 🔥 关键修复：检查服务器是否已在运行，避免重复启动
        if (remoteSenderServer.isServerRunning()) {
            AppLog.d("【SenderDialogFragment】远程被控服务器已在运行，跳过重复启动")
            Toast.makeText(requireContext(), "远程被控服务器已在运行", Toast.LENGTH_SHORT).show()
            return
        }

        AppLog.d("【SenderDialogFragment】启动远程被控服务器")
        if (remoteSenderServer.startServer()) {
            Toast.makeText(requireContext(), getString(R.string.remote_control_server_started), Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(requireContext(), getString(R.string.remote_control_server_start_failed), Toast.LENGTH_SHORT).show()
            remoteControlSwitch.isChecked = false
        }
    }

    private fun stopRemoteControlServer() {
        remoteSenderServer.stopServer()
        Toast.makeText(requireContext(), getString(R.string.remote_control_server_stopped), Toast.LENGTH_SHORT).show()
    }

    private fun updateRemoteControlStatusText() {
        val ipAddress = localIpAddress ?: "未知IP"
        remoteControlStatusText.text = getString(R.string.remote_control_address_format, ipAddress)
    }

    private fun setupClickListeners() {
        addReceiverButton.setOnClickListener { viewModel.showAddReceiverDialog() }
        closeButton.setOnClickListener { dismiss() }
    }

    /**
     * 显示添加接收端对话框
     */
    private fun showAddReceiverDialog() {
        val view = layoutInflater.inflate(R.layout.dialog_add_receiver, null)

        val dialogTitle = view.findViewById<TextView>(R.id.dialog_title)
        val ipAddressInput = view.findViewById<EditText>(R.id.ip_address_input)
        val portInput = view.findViewById<EditText>(R.id.port_input)

        // 设置添加接收端的标题
        dialogTitle.text = "添加接收端"

        AlertDialog.Builder(requireContext())
            .setView(view)
            .setPositiveButton("添加") { _, _ ->
                val ipAddress = ipAddressInput.text.toString().trim()
                val portText = portInput.text.toString().trim()

                if (validateInput(ipAddress, portText)) {
                    val port = portText.toInt()
                    viewModel.addConnection(ipAddress, port)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示编辑连接对话框
     */
    private fun showEditConnectionDialog(connection: Connection) {
        val view = layoutInflater.inflate(R.layout.dialog_add_receiver, null)

        val dialogTitle = view.findViewById<TextView>(R.id.dialog_title)
        val ipAddressInput = view.findViewById<EditText>(R.id.ip_address_input)
        val portInput = view.findViewById<EditText>(R.id.port_input)

        // 设置编辑连接的标题
        dialogTitle.text = "编辑连接"

        // 预填充当前连接信息
        ipAddressInput.setText(connection.ipAddress)
        portInput.setText(connection.port.toString())

        AlertDialog.Builder(requireContext())
            .setView(view)
            .setPositiveButton("确认") { _, _ ->
                val newIpAddress = ipAddressInput.text.toString().trim()
                val newPortText = portInput.text.toString().trim()

                if (validateInput(newIpAddress, newPortText)) {
                    val newPort = newPortText.toInt()

                    // 检查是否有实际变化
                    if (newIpAddress != connection.ipAddress || newPort != connection.port) {
                        updateConnection(connection, newIpAddress, newPort)
                    } else {
                        showToast("连接信息未发生变化")
                    }
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示删除连接确认对话框
     */
    private fun showRemoveConnectionDialog(connection: Connection) {
        AlertDialog.Builder(requireContext())
            .setTitle("确认删除")
            .setMessage("确定要删除连接 ${connection.getDisplayText()} 吗？\n\n此操作不可撤销。")
            .setPositiveButton("删除") { _, _ ->
                viewModel.removeConnection(connection)
            }
            .setNegativeButton("取消", null)
            .setIcon(android.R.drawable.ic_dialog_alert)
            .show()
    }



    // 注意：不再需要onActivityResult方法
    // 新的Activity Result API会自动处理权限结果

    /**
     * 验证输入
     */
    private fun validateInput(ipAddress: String, portText: String): Boolean {
        if (ipAddress.isEmpty()) {
            showToast("请输入IP地址")
            return false
        }

        if (!NetworkUtils.isValidIpAddress(ipAddress)) {
            showToast("IP地址格式不正确")
            return false
        }

        if (portText.isEmpty()) {
            showToast("请输入端口号")
            return false
        }

        val port = try {
            portText.toInt()
        } catch (_: NumberFormatException) {
            showToast("端口号格式不正确")
            return false
        }

        if (!NetworkUtils.isValidPort(port)) {
            showToast("端口号必须在1024-65535之间")
            return false
        }

        return true
    }

    /**
     * 更新连接信息
     */
    private fun updateConnection(originalConnection: Connection, newIpAddress: String, newPort: Int) {
        try {
            // 创建更新后的连接对象，保持connectionId和所有状态不变
            val updatedConnection = originalConnection.copy(
                ipAddress = newIpAddress,
                port = newPort,
                lastUpdateTime = System.currentTimeMillis()
            )

            // 使用StateManager更新连接
            stateManager.updateExistingConnection(updatedConnection)

            // 通过ViewModel广播连接更新事件
            viewModel.broadcastConnectionUpdated(updatedConnection)

            showToast("连接已更新: $newIpAddress:$newPort")
            AppLog.d("连接编辑成功: ${originalConnection.getDisplayText()} -> ${updatedConnection.getDisplayText()}")

        } catch (e: Exception) {
            AppLog.e("更新连接失败", e)
            showToast("更新连接失败: ${e.message}")
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    private fun handleCastToggle(connection: Connection, isEnabled: Boolean) {
        viewModel.toggleCasting(connection, isEnabled)
        if (isEnabled) scheduleConnectionFailureCheck(connection, "投屏")
    }

    private fun handleAudioToggle(connection: Connection, isEnabled: Boolean, audioType: String) {
        when (audioType) {
            "media" -> viewModel.toggleMediaAudio(connection, isEnabled)
            "mic" -> viewModel.toggleMicAudio(connection, isEnabled)
        }
        if (isEnabled) {
            val functionType = if (audioType == "media") "媒体音频" else "麦克风音频"
            scheduleConnectionFailureCheck(connection, functionType)
        }
    }

    private fun scheduleConnectionFailureCheck(connection: Connection, functionType: String) {
        val connectionId = connection.connectionId
        view?.postDelayed({
            val currentConnection = connections.find { it.connectionId == connectionId }
            if (currentConnection != null) {
                val stateConnection = stateManager.getConnection(connectionId)
                val shouldRefresh = when (functionType) {
                    "投屏" -> !(stateConnection?.isCasting ?: false) &&
                            !(viewModel.castingConnections.value?.contains(stateConnection?.connectionId ?: "") ?: false)
                    "媒体音频" -> !(stateConnection?.isMediaAudioEnabled ?: false)
                    "麦克风音频" -> !(stateConnection?.isMicAudioEnabled ?: false)
                    else -> false
                }
                if (shouldRefresh) {
                    AppLog.d("${functionType}连接失败检查：需要刷新UI - ${connection.getDisplayText()}")
                    stateConnection?.let { forceRefreshConnectionState(it) }
                } else {
                    AppLog.d("${functionType}连接成功，跳过UI刷新 - ${connection.getDisplayText()}")
                }
            }
        }, 500)
    }

    private fun forceRefreshConnectionState(connection: Connection) {
        // 🚀 优化：使用精准更新而不是notifyItemChanged
        connectionAdapter.updateConnectionPrecisely(connection.connectionId, connection)
        AppLog.d("强制精准刷新连接状态: ${connection.getDisplayText()}")
    }

    /**
     * 从远程控制更新码率（双向同步）
     */
    private fun updateBitrateFromRemote(bitrateMbps: Int) {
        // 防止循环更新：暂时移除监听器
        bitrateSeekBar.setOnSeekBarChangeListener(null)

        // 更新UI显示
        val progress = bitrateMbps - 5 // 转换为SeekBar的progress值
        bitrateSeekBar.progress = progress.coerceIn(0, bitrateSeekBar.max)
        updateBitrateDisplay(bitrateMbps)

        // 保存到SharedPreferences，确保状态一致
        val sharedPrefs = requireContext().getSharedPreferences("cast_settings", Context.MODE_PRIVATE)
        sharedPrefs.edit { putInt("bitrate_mbps", bitrateMbps) }

        // 🔥 关键修复：不再调用applyBitrateChange，避免重复调整
        // 业务逻辑已经在SenderViewModel中执行过了，这里只需要同步UI状态

        // 只恢复监听器，不重新设置值
        setupBitrateControlListener()

        AppLog.d("远程同步码率UI: ${bitrateMbps}Mbps，仅更新UI显示")
        Toast.makeText(requireContext(), "远程同步码率: ${bitrateMbps}Mbps", Toast.LENGTH_SHORT).show()
    }

    /**
     * 从远程控制更新分辨率（双向同步）
     */
    private fun updateResolutionFromRemote(scalePercent: Int) {
        // 防止循环更新：暂时移除监听器
        resolutionSeekBar.setOnSeekBarChangeListener(null)

        // 更新UI显示
        val progress = scalePercent - 1 // 转换为SeekBar的progress值
        resolutionSeekBar.progress = progress.coerceIn(0, resolutionSeekBar.max)
        updateResolutionDisplay(scalePercent)

        // 保存到SharedPreferences，确保状态一致
        val sharedPrefs = requireContext().getSharedPreferences("cast_settings", Context.MODE_PRIVATE)
        sharedPrefs.edit { putInt("resolution_scale", scalePercent) }

        // 🔥 关键修复：不再调用applyResolutionChange，避免重复调整
        // 业务逻辑已经在SenderViewModel中执行过了，这里只需要同步UI状态

        // 只恢复监听器，不重新设置值
        setupResolutionControlListener()

        AppLog.d("远程同步分辨率UI: ${scalePercent}%，仅更新UI显示")
        Toast.makeText(requireContext(), "远程同步分辨率: ${scalePercent}%", Toast.LENGTH_SHORT).show()
    }

    /**
     * 从远程控制更新音量（双向同步）
     */
    private fun updateVolumeFromRemote(volumeType: String, volume: Int) {
        val sharedPrefs = requireContext().getSharedPreferences("cast_settings", Context.MODE_PRIVATE)

        when (volumeType) {
            "media" -> {
                mediaAudioVolumeSeekBar.setOnSeekBarChangeListener(null)
                mediaAudioVolumeSeekBar.progress = volume
                mediaAudioVolumeText.text = getString(R.string.volume_format, volume)

                // 保存到SharedPreferences，确保状态一致
                sharedPrefs.edit { putInt("media_audio_volume", volume) }

                // 🔥 关键修复：不再调用viewModel.updateMediaAudioVolume，避免重复调整
                // 业务逻辑已经在SenderViewModel中执行过了，这里只需要同步UI状态

                // 只恢复监听器，不重新设置值
                setupAudioVolumeControlListeners()
                AppLog.d("远程同步媒体音量UI: ${volume}%，仅更新UI显示")
                Toast.makeText(requireContext(), "远程同步媒体音量: ${volume}%", Toast.LENGTH_SHORT).show()
            }
            "mic" -> {
                micAudioVolumeSeekBar.setOnSeekBarChangeListener(null)
                micAudioVolumeSeekBar.progress = volume
                micAudioVolumeText.text = getString(R.string.volume_format, volume)

                // 保存到SharedPreferences，确保状态一致
                sharedPrefs.edit { putInt("mic_audio_volume", volume) }

                // 🔥 关键修复：不再调用viewModel.updateMicAudioVolume，避免重复调整
                // 业务逻辑已经在SenderViewModel中执行过了，这里只需要同步UI状态

                // 只恢复监听器，不重新设置值
                setupAudioVolumeControlListeners()
                AppLog.d("远程同步麦克风音量UI: ${volume}%，仅更新UI显示")
                Toast.makeText(requireContext(), "远程同步麦克风音量: ${volume}%", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 从远程控制更新连接切换（双向同步）
     */
    private fun updateConnectionToggleFromRemote(functionType: String, enabled: Boolean) {
        AppLog.d("远程同步连接切换: $functionType = $enabled")
        Toast.makeText(requireContext(), "远程同步连接切换: $functionType = $enabled", Toast.LENGTH_SHORT).show()

        // TODO: 实现连接切换的UI同步
        // 这需要找到对应的连接并更新其开关状态
    }

    /**
     * 处理远程控制UI更新
     */
    private fun handleRemoteControlUIUpdate(update: SenderViewModel.RemoteControlUIUpdate) {
        when (update) {
            is SenderViewModel.RemoteControlUIUpdate.BitrateUpdate -> {
                updateBitrateFromRemote(update.bitrateMbps)
            }
            is SenderViewModel.RemoteControlUIUpdate.ResolutionUpdate -> {
                updateResolutionFromRemote(update.scalePercent)
            }
            is SenderViewModel.RemoteControlUIUpdate.VolumeUpdate -> {
                updateVolumeFromRemote(update.volumeType, update.volume)
            }
            is SenderViewModel.RemoteControlUIUpdate.ConnectionToggle -> {
                updateConnectionToggleFromRemote(update.functionType, update.enabled)
            }
        }
    }

    /**
     * 通知远程控制端UI更新
     */
    private fun notifyRemoteControlClients(key: String, value: Any) {
        try {
            if (remoteSenderServer.isServerRunning()) {
                val settings = mapOf(key to value)
                remoteSenderServer.broadcastSettingsUpdate(settings)
                AppLog.d("通知远程控制端UI更新: $key = $value")
            }
        } catch (e: Exception) {
            AppLog.e("通知远程控制端失败", e)
        }
    }

    /**
     * 🚀 新增：设置精准状态更新监听器
     */
    private fun setupPreciseStateUpdateListeners() {
        connections.forEach { connection ->
            registerPreciseStateUpdateListener(connection.connectionId)
        }
    }

    /**
     * 🚀 新增：为新连接批量注册精准状态监听器
     */
    private fun setupPreciseStateUpdateListenersForNewConnections(newConnections: List<Connection>) {
        newConnections.forEach { connection ->
            registerPreciseStateUpdateListener(connection.connectionId)
        }
    }

    /**
     * 🚀 新增：注册单个连接的精准状态更新监听器
     */
    private fun registerPreciseStateUpdateListener(connectionId: String) {
        stateManager.registerPreciseStateChangeListener(connectionId) { changedConnectionId, updatedConnection ->
            // 在主线程中进行精准更新
            activity?.runOnUiThread {
                connectionAdapter.updateConnectionPrecisely(changedConnectionId, updatedConnection)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 🚀 新增：清理精准状态更新监听器
        connections.forEach { connection ->
            stateManager.unregisterPreciseStateChangeListener(connection.connectionId)
        }
        // 远程控制回调现在由SenderViewModel管理，UI层不再清理
        // 这确保了即使发送端设置窗口关闭，远程控制仍能正常工作
        AppLog.d("SenderDialogFragment销毁，远程控制回调由SenderViewModel管理")
    }
}
