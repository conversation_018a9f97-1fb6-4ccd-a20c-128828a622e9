package com.example.castapp.ui.dialog

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.OpenableColumns
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.fragment.app.DialogFragment
import com.example.castapp.R
import com.example.castapp.manager.WindowSettingsManager
import com.example.castapp.manager.PermissionManager
import com.example.castapp.ui.MainActivity
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.ToastUtils
import java.util.UUID

/**
 * 添加媒体对话框
 * 用于选择添加前置摄像头或后置摄像头窗口
 */
class AddMediaDialogFragment : DialogFragment() {

    // UI组件
    private lateinit var btnAddFrontCamera: LinearLayout
    private lateinit var btnAddRearCamera: LinearLayout
    private lateinit var btnAddVideo: LinearLayout
    private lateinit var btnAddPicture: LinearLayout
    private lateinit var btnAddText: LinearLayout

    // 权限请求相关
    private var pendingMediaType: String? = null

    // 文件选择器启动器
    private val videoPickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        handleVideoPickerResult(result)
    }

    private val imagePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        handleImagePickerResult(result)
    }

    // 存储权限请求启动器
    private val storagePermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        handleStoragePermissionResult(permissions)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_add_media, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        setupClickListeners()
        
        AppLog.d("添加媒体对话框已创建")
    }

    /**
     * 初始化视图组件
     */
    private fun initViews(view: View) {
        btnAddFrontCamera = view.findViewById(R.id.btn_add_front_camera)
        btnAddRearCamera = view.findViewById(R.id.btn_add_rear_camera)
        btnAddVideo = view.findViewById(R.id.btn_add_video)
        btnAddPicture = view.findViewById(R.id.btn_add_picture)
        btnAddText = view.findViewById(R.id.btn_add_text)
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        btnAddFrontCamera.setOnClickListener {
            handleAddFrontCamera()
        }

        btnAddRearCamera.setOnClickListener {
            handleAddRearCamera()
        }

        btnAddVideo.setOnClickListener {
            handleAddVideo()
        }

        btnAddPicture.setOnClickListener {
            handleAddPicture()
        }

        btnAddText.setOnClickListener {
            handleAddText()
        }
    }

    /**
     * 处理添加前置摄像头
     */
    private fun handleAddFrontCamera() {
        AppLog.d("用户点击添加前置摄像头")

        if (checkCameraPermission()) {
            createCameraWindow("front_camera", "前置摄像头")
        } else {
            requestBasicPermissions("front_camera", "前置摄像头")
        }
    }

    /**
     * 处理添加后置摄像头
     */
    private fun handleAddRearCamera() {
        AppLog.d("用户点击添加后置摄像头")

        if (checkCameraPermission()) {
            createCameraWindow("rear_camera", "后置摄像头")
        } else {
            requestBasicPermissions("rear_camera", "后置摄像头")
        }
    }

    /**
     * 处理添加视频
     */
    private fun handleAddVideo() {
        AppLog.d("用户点击添加视频")

        if (checkStoragePermission()) {
            openVideoFilePicker()
        } else {
            requestStoragePermissions("video")
        }
    }

    /**
     * 处理添加图片
     */
    private fun handleAddPicture() {
        AppLog.d("用户点击添加图片")

        if (checkStoragePermission()) {
            openImageFilePicker()
        } else {
            requestStoragePermissions("image")
        }
    }

    /**
     * 处理添加文本
     */
    private fun handleAddText() {
        AppLog.d("用户点击添加文本")

        try {
            // 生成唯一的文本窗口ID
            val textId = "text_${UUID.randomUUID()}"
            val defaultText = "默认文字"

            AppLog.d("开始创建文本窗口: ID=$textId, 文本=$defaultText")

            // 获取WindowSettingsManager实例
            val windowSettingsManager = WindowSettingsManager.getInstance()

            // 创建文本窗口
            windowSettingsManager.createTextWindow(textId, defaultText)

            ToastUtils.showToast(requireContext(), "文本窗口创建成功")

            // 关闭对话框
            dismiss()

        } catch (e: Exception) {
            AppLog.e("创建文本窗口失败", e)
            ToastUtils.showToast(requireContext(), "创建文本窗口失败")
        }
    }

    /**
     * 检查摄像头权限
     */
    private fun checkCameraPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            requireContext(),
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 请求基础权限（包含摄像头权限）
     */
    private fun requestBasicPermissions(cameraId: String, cameraName: String) {
        AppLog.d("请求基础权限以创建摄像头窗口: $cameraName")

        // 获取MainActivity的权限管理器实例
        val activity = requireActivity() as MainActivity
        val permissionHelper = activity.getActivityPermissionHelper()

        permissionHelper.requestBasicPermissions(object : PermissionManager.PermissionCallback {
            override fun onPermissionGranted() {
                AppLog.d("基础权限已授予，创建摄像头窗口: $cameraName")
                createCameraWindow(cameraId, cameraName)
            }

            override fun onPermissionDenied(deniedPermissions: List<String>) {
                AppLog.w("基础权限被拒绝: ${deniedPermissions.joinToString()}")

                if (deniedPermissions.contains(Manifest.permission.CAMERA)) {
                    ToastUtils.showToast(requireContext(), "摄像头权限被拒绝，无法创建摄像头窗口")
                } else {
                    ToastUtils.showToast(requireContext(), "权限被拒绝，无法创建摄像头窗口")
                }
            }
        })
    }

    /**
     * 创建摄像头窗口
     */
    private fun createCameraWindow(cameraId: String, cameraName: String) {
        try {
            AppLog.d("开始创建摄像头窗口: $cameraName (ID: $cameraId)")
            
            // 获取CastWindowManager实例
            val windowSettingsManager = WindowSettingsManager.getInstance()
            
            // 创建摄像头窗口
            windowSettingsManager.createCameraWindow(cameraId, cameraName)
            
            ToastUtils.showToast(requireContext(), "${cameraName}窗口创建成功")
            
            // 关闭对话框
            dismiss()
            
        } catch (e: Exception) {
            AppLog.e("创建摄像头窗口失败: $cameraName", e)
            ToastUtils.showToast(requireContext(), "创建${cameraName}窗口失败")
        }
    }

    // ==================== 媒体文件处理 ====================

    /**
     * 检查存储权限
     */
    private fun checkStoragePermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 使用细分权限
            requireContext().checkSelfPermission(Manifest.permission.READ_MEDIA_VIDEO) ==
                PackageManager.PERMISSION_GRANTED &&
            requireContext().checkSelfPermission(Manifest.permission.READ_MEDIA_IMAGES) ==
                PackageManager.PERMISSION_GRANTED
        } else {
            // Android 12及以下使用READ_EXTERNAL_STORAGE
            requireContext().checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) ==
                PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 请求存储权限
     */
    private fun requestStoragePermissions(mediaType: String) {
        try {
            // 获取需要的权限
            val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ 使用细分权限
                arrayOf(
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.READ_MEDIA_IMAGES
                )
            } else {
                // Android 12及以下使用READ_EXTERNAL_STORAGE
                arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
            }

            // 保存媒体类型以便在权限回调中使用
            pendingMediaType = mediaType

            // 使用现代的权限请求方式
            storagePermissionLauncher.launch(permissions)

        } catch (e: Exception) {
            AppLog.e("请求存储权限失败", e)
            ToastUtils.showToast(requireContext(), "请求存储权限失败")
        }
    }

    /**
     * 打开视频文件选择器（支持持久化权限）
     */
    private fun openVideoFilePicker() {
        try {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                type = "video/*"
                addCategory(Intent.CATEGORY_OPENABLE)
                // 📁 添加持久化权限标志
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION)
            }

            videoPickerLauncher.launch(intent)

        } catch (e: Exception) {
            AppLog.e("打开视频文件选择器失败", e)
            ToastUtils.showToast(requireContext(), "打开视频文件选择器失败")
        }
    }

    /**
     * 打开图片文件选择器（支持持久化权限）
     */
    private fun openImageFilePicker() {
        try {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                type = "image/*"
                addCategory(Intent.CATEGORY_OPENABLE)
                // 📁 添加持久化权限标志
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION)
            }

            imagePickerLauncher.launch(intent)

        } catch (e: Exception) {
            AppLog.e("打开图片文件选择器失败", e)
            ToastUtils.showToast(requireContext(), "打开图片文件选择器失败")
        }
    }

    /**
     * 处理视频文件选择结果
     */
    private fun handleVideoPickerResult(result: ActivityResult) {
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                try {
                    val fileName = getFileName(uri) ?: "未知视频"
                    val videoId = generateMediaId(fileName, "video")

                    AppLog.d("选择视频文件: $fileName, URI: $uri")

                    // 创建视频窗口
                    createMediaWindow(videoId, "视频", fileName, uri, "video")

                } catch (e: Exception) {
                    AppLog.e("处理视频文件失败", e)
                    ToastUtils.showToast(requireContext(), "处理视频文件失败")
                }
            }
        }
    }

    /**
     * 处理图片文件选择结果
     */
    private fun handleImagePickerResult(result: ActivityResult) {
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                try {
                    val fileName = getFileName(uri) ?: "未知图片"
                    val imageId = generateMediaId(fileName, "image")

                    AppLog.d("选择图片文件: $fileName, URI: $uri")

                    // 创建图片窗口
                    createMediaWindow(imageId, "图片", fileName, uri, "image")

                } catch (e: Exception) {
                    AppLog.e("处理图片文件失败", e)
                    ToastUtils.showToast(requireContext(), "处理图片文件失败")
                }
            }
        }
    }

    /**
     * 获取文件名
     */
    private fun getFileName(uri: Uri): String? {
        return try {
            val cursor = requireContext().contentResolver.query(uri, null, null, null, null)
            cursor?.use {
                if (it.moveToFirst()) {
                    val nameIndex = it.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                    if (nameIndex >= 0) {
                        it.getString(nameIndex)
                    } else null
                } else null
            }
        } catch (e: Exception) {
            AppLog.e("获取文件名失败", e)
            null
        }
    }

    /**
     * 生成媒体窗口ID
     */
    private fun generateMediaId(fileName: String, mediaType: String): String {
        // 移除文件扩展名，保留完整文件名用于显示
        val nameWithoutExtension = fileName.substringBeforeLast(".")
        return "${mediaType}_${nameWithoutExtension}"
    }

    /**
     * 创建媒体窗口
     */
    private fun createMediaWindow(mediaId: String, mediaType: String, fileName: String, uri: Uri, contentType: String) {
        try {
            AppLog.d("开始创建媒体窗口: $mediaType (ID: $mediaId, 文件: $fileName)")

            // 获取WindowSettingsManager实例
            val windowSettingsManager = WindowSettingsManager.getInstance()

            // 创建媒体窗口
            windowSettingsManager.createMediaWindow(mediaId, mediaType, fileName, uri, contentType)

            ToastUtils.showToast(requireContext(), "${mediaType}窗口创建成功")

            // 关闭对话框
            dismiss()

        } catch (e: Exception) {
            AppLog.e("创建媒体窗口失败: $mediaType", e)
            ToastUtils.showToast(requireContext(), "创建${mediaType}窗口失败")
        }
    }

    /**
     * 处理存储权限请求结果
     */
    private fun handleStoragePermissionResult(permissions: Map<String, Boolean>) {
        try {
            val allGranted = permissions.values.all { it }

            if (allGranted) {
                // 权限已授予
                AppLog.d("存储权限已授予，打开${pendingMediaType}文件选择器")
                when (pendingMediaType) {
                    "video" -> openVideoFilePicker()
                    "image" -> openImageFilePicker()
                }
            } else {
                // 权限被拒绝
                AppLog.w("存储权限被拒绝: ${permissions.filter { !it.value }.keys}")
                ToastUtils.showToast(requireContext(), "存储权限被拒绝，无法选择${pendingMediaType}文件")
            }

            pendingMediaType = null

        } catch (e: Exception) {
            AppLog.e("处理存储权限结果失败", e)
            ToastUtils.showToast(requireContext(), "处理权限结果失败")
            pendingMediaType = null
        }
    }
}
