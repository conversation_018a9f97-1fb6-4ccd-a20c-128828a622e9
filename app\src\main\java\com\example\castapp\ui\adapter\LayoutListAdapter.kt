package com.example.castapp.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.database.entity.WindowLayoutEntity
import com.example.castapp.ui.helper.LayoutItemTouchHelperCallback
import com.example.castapp.utils.AppLog
import java.util.Collections

/**
 * DiffUtil回调类，用于高效比较布局列表变化
 */
private class LayoutDiffCallback(
    private val oldList: List<WindowLayoutEntity>,
    private val newList: List<WindowLayoutEntity>
) : DiffUtil.Callback() {

    override fun getOldListSize(): Int = oldList.size

    override fun getNewListSize(): Int = newList.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList[oldItemPosition].id == newList[newItemPosition].id
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val oldItem = oldList[oldItemPosition]
        val newItem = newList[newItemPosition]
        return oldItem.layoutName == newItem.layoutName &&
                oldItem.windowCount == newItem.windowCount &&
                oldItem.createdAt == newItem.createdAt &&
                oldItem.sortOrder == newItem.sortOrder
    }
}

/**
 * 布局列表适配器 - 重构版
 * 用于显示已保存的窗口布局列表，支持拖拽排序
 * 采用单一数据源架构，避免数据不一致问题
 */
class LayoutListAdapter : RecyclerView.Adapter<LayoutListAdapter.LayoutViewHolder>(),
    LayoutItemTouchHelperCallback.ItemTouchHelperAdapter {
    
    // 布局选择监听器
    interface OnLayoutSelectedListener {
        fun onLayoutSelected(layout: WindowLayoutEntity)
    }

    // 拖拽完成监听器
    interface OnItemMoveListener {
        fun onItemMoved(fromPosition: Int, toPosition: Int, layouts: List<WindowLayoutEntity>)
    }

    // 批量选择状态变化监听器
    interface OnSelectionChangedListener {
        fun onSelectionChanged(selectedCount: Int, totalCount: Int)
    }

    private var onLayoutSelectedListener: OnLayoutSelectedListener? = null
    private var onItemMoveListener: OnItemMoveListener? = null
    private var onSelectionChangedListener: OnSelectionChangedListener? = null
    private var selectedLayoutId: Long = -1

    // 🐾 当前应用的布局ID（新增应用状态管理）
    private var appliedLayoutId: Long = -1

    // 🐾 批量选择模式相关状态
    private var isSelectionMode: Boolean = false
    private val selectedItems = mutableSetOf<Long>()

    // 单一数据源
    private val dataList = mutableListOf<WindowLayoutEntity>()
    
    /**
     * 设置布局选择监听器
     */
    fun setOnLayoutSelectedListener(listener: OnLayoutSelectedListener) {
        this.onLayoutSelectedListener = listener
    }

    /**
     * 设置拖拽完成监听器
     */
    fun setOnItemMoveListener(listener: OnItemMoveListener) {
        this.onItemMoveListener = listener
    }

    /**
     * 设置选择状态变化监听器
     */
    fun setOnSelectionChangedListener(listener: OnSelectionChangedListener) {
        this.onSelectionChangedListener = listener
    }
    
    /**
     * 设置当前选中的布局ID
     */
    fun setSelectedLayoutId(layoutId: Long) {
        val oldSelectedPosition = dataList.indexOfFirst { it.id == selectedLayoutId }
        val newSelectedPosition = dataList.indexOfFirst { it.id == layoutId }

        selectedLayoutId = layoutId

        // 刷新旧的和新的选中项
        if (oldSelectedPosition != -1) {
            notifyItemChanged(oldSelectedPosition)
        }
        if (newSelectedPosition != -1) {
            notifyItemChanged(newSelectedPosition)
        }

        AppLog.d("选中状态更新: layoutId=$layoutId, 新位置=$newSelectedPosition")
    }

    /**
     * 🐾 设置当前应用的布局ID（新增）
     */
    fun setAppliedLayoutId(layoutId: Long) {
        val oldAppliedPosition = dataList.indexOfFirst { it.id == appliedLayoutId }
        val newAppliedPosition = dataList.indexOfFirst { it.id == layoutId }

        appliedLayoutId = layoutId

        // 刷新旧的和新的应用项
        if (oldAppliedPosition != -1) {
            notifyItemChanged(oldAppliedPosition)
        }
        if (newAppliedPosition != -1) {
            notifyItemChanged(newAppliedPosition)
        }

        AppLog.d("应用状态更新: layoutId=$layoutId, 新位置=$newAppliedPosition")
    }

    // ==================== 批量选择模式相关方法 ====================

    /**
     * 进入选择模式
     */
    fun enterSelectionMode() {
        if (!isSelectionMode) {
            isSelectionMode = true
            selectedItems.clear()
            notifyDataSetChanged() // 刷新所有项以显示复选框
            AppLog.d("进入批量选择模式")
        }
    }

    /**
     * 退出选择模式
     */
    fun exitSelectionMode() {
        if (isSelectionMode) {
            isSelectionMode = false
            selectedItems.clear()
            notifyDataSetChanged() // 刷新所有项以隐藏复选框
            onSelectionChangedListener?.onSelectionChanged(0, dataList.size)
            AppLog.d("退出批量选择模式")
        }
    }

    /**
     * 切换指定布局的选择状态
     */
    fun toggleSelection(layoutId: Long) {
        if (!isSelectionMode) return

        if (selectedItems.contains(layoutId)) {
            selectedItems.remove(layoutId)
        } else {
            selectedItems.add(layoutId)
        }

        // 刷新对应的项
        val position = dataList.indexOfFirst { it.id == layoutId }
        if (position != -1) {
            notifyItemChanged(position)
        }

        // 通知选择状态变化
        onSelectionChangedListener?.onSelectionChanged(selectedItems.size, dataList.size)
        AppLog.d("切换选择状态: layoutId=$layoutId, 当前选中${selectedItems.size}个")
    }

    /**
     * 获取选中的布局列表
     */
    fun getSelectedLayouts(): List<WindowLayoutEntity> {
        return dataList.filter { selectedItems.contains(it.id) }
    }

    /**
     * 获取选中的布局ID列表
     */
    fun getSelectedLayoutIds(): List<Long> {
        return selectedItems.toList()
    }

    /**
     * 检查是否处于选择模式
     */
    fun isInSelectionMode(): Boolean {
        return isSelectionMode
    }

    /**
     * 获取选中项数量
     */
    fun getSelectedCount(): Int {
        return selectedItems.size
    }
    
    /**
     * 提交新的数据列表 - 使用DiffUtil优化性能
     */
    fun submitList(list: List<WindowLayoutEntity>?) {
        AppLog.d("提交新数据列表，共${list?.size ?: 0}个布局")

        val newList = list ?: emptyList()
        val oldList = dataList.toList() // 创建当前数据的副本

        // 使用DiffUtil计算差异
        val diffCallback = LayoutDiffCallback(oldList, newList)
        val diffResult = DiffUtil.calculateDiff(diffCallback)

        // 更新数据源
        dataList.clear()
        dataList.addAll(newList)

        // 应用差异更新，这比notifyDataSetChanged()更高效
        diffResult.dispatchUpdatesTo(this)

        AppLog.d("适配器数据更新完成，使用DiffUtil优化: ${newList.map { layout -> "${layout.layoutName}(id=${layout.id})" }}")
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LayoutViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_director_layout, parent, false)
        return LayoutViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: LayoutViewHolder, position: Int) {
        if (position < 0 || position >= dataList.size) {
            AppLog.w("绑定位置无效: position=$position, size=${dataList.size}")
            return
        }

        val layout = dataList[position]
        val isSelected = layout.id == selectedLayoutId
        val isApplied = layout.isApplied
        val isChecked = selectedItems.contains(layout.id)

        AppLog.d("绑定ViewHolder: position=$position, name='${layout.layoutName}', id=${layout.id}, selected=$isSelected, applied=$isApplied, checked=$isChecked")
        holder.bind(layout, isSelected, isApplied, isSelectionMode, isChecked)
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    // ==================== ItemTouchHelperAdapter接口实现 ====================

    /**
     * 当item位置移动时调用
     */
    override fun onItemMove(fromPosition: Int, toPosition: Int): Boolean {
        if (fromPosition < 0 || toPosition < 0 ||
            fromPosition >= dataList.size || toPosition >= dataList.size) {
            AppLog.w("拖拽位置无效: $fromPosition -> $toPosition, size=${dataList.size}")
            return false
        }

        // 交换数据位置
        Collections.swap(dataList, fromPosition, toPosition)

        // 通知RecyclerView更新视图
        notifyItemMoved(fromPosition, toPosition)

        AppLog.d("布局项移动: $fromPosition -> $toPosition, 当前列表: ${dataList.map { it.layoutName }}")
        return true
    }

    /**
     * 当拖拽完成时调用，保存最终排序结果
     */
    override fun onItemMoveCompleted(fromPosition: Int, toPosition: Int) {
        AppLog.d("拖拽排序完成: $fromPosition -> $toPosition")

        // 创建当前数据的副本
        val currentList = dataList.toList()
        AppLog.d("拖拽完成，最终列表: ${currentList.map { "${it.layoutName}(id=${it.id})" }}")

        // 通知外部监听器保存排序结果
        onItemMoveListener?.onItemMoved(fromPosition, toPosition, currentList)
    }

    /**
     * 布局项ViewHolder
     */
    inner class LayoutViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val layoutNameText: TextView = itemView.findViewById(R.id.layout_name_text)
        private val layoutDateText: TextView = itemView.findViewById(R.id.layout_date_text)
        private val layoutWindowCountText: TextView = itemView.findViewById(R.id.layout_window_count_text)
        private val selectionCheckBox: CheckBox = itemView.findViewById(R.id.selection_checkbox)
        
        init {
            itemView.setOnClickListener {
                val position = bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION && position < dataList.size) {
                    val layout = dataList[position]
                    AppLog.d("点击布局项: position=$position, layoutName=${layout.layoutName}, id=${layout.id}")

                    if (isSelectionMode) {
                        // 选择模式下，点击切换选择状态
                        toggleSelection(layout.id)
                    } else {
                        // 普通模式下，选中布局
                        setSelectedLayoutId(layout.id)
                        onLayoutSelectedListener?.onLayoutSelected(layout)
                    }
                }
            }
        }
        
        fun bind(layout: WindowLayoutEntity, isSelected: Boolean, isApplied: Boolean, isSelectionMode: Boolean, isChecked: Boolean) {
            // 数据验证，确保布局对象有效
            if (layout.layoutName.isEmpty()) {
                AppLog.w("绑定数据异常: 布局名称为空, id=${layout.id}")
            }

            // 安全地设置文本内容
            layoutNameText.text = layout.layoutName.takeIf { it.isNotEmpty() } ?: "未知布局"
            layoutDateText.text = layout.getFormattedCreatedTime()
            layoutWindowCountText.text = itemView.context.getString(R.string.window_count_format, layout.windowCount)

            // 🐾 设置复选框的显示状态
            selectionCheckBox.visibility = if (isSelectionMode) View.VISIBLE else View.GONE
            selectionCheckBox.isChecked = isChecked

            AppLog.d("绑定布局项详情: position=${bindingAdapterPosition}, name='${layout.layoutName}', id=${layout.id}, selected=$isSelected, applied=$isApplied, checked=$isChecked")

            // 🐾 设置多重状态的视觉效果（优化版：半透明覆盖层）
            when {
                isSelected && isApplied -> {
                    // 选中 + 应用状态：浅灰色背景 + 橙色渐变覆盖层 🐾
                    itemView.setBackgroundResource(R.drawable.item_selected_applied_background)
                    layoutNameText.setTextColor(itemView.context.getColor(R.color.text_primary))
                    layoutDateText.setTextColor(itemView.context.getColor(R.color.text_secondary))
                    layoutWindowCountText.setTextColor(itemView.context.getColor(R.color.text_secondary))
                }
                isSelected -> {
                    // 仅选中状态：浅灰色背景 🐾
                    itemView.setBackgroundResource(R.drawable.item_selected_background)
                    layoutNameText.setTextColor(itemView.context.getColor(R.color.text_primary))
                    layoutDateText.setTextColor(itemView.context.getColor(R.color.text_secondary))
                    layoutWindowCountText.setTextColor(itemView.context.getColor(R.color.text_secondary))
                }
                isApplied -> {
                    // 仅应用状态：白色背景 + 左侧绿色指示条
                    itemView.setBackgroundResource(R.drawable.item_applied_background)
                    // 🐾 应用状态下文字保持深色，因为底层是白色背景
                    layoutNameText.setTextColor(itemView.context.getColor(R.color.primary_blue))
                    layoutDateText.setTextColor(itemView.context.getColor(android.R.color.darker_gray))
                    layoutWindowCountText.setTextColor(itemView.context.getColor(android.R.color.darker_gray))
                }
                else -> {
                    // 普通状态：白色背景
                    itemView.setBackgroundResource(R.drawable.item_normal_background)
                    layoutNameText.setTextColor(itemView.context.getColor(R.color.primary_blue))
                    layoutDateText.setTextColor(itemView.context.getColor(android.R.color.darker_gray))
                    layoutWindowCountText.setTextColor(itemView.context.getColor(android.R.color.darker_gray))
                }
            }
        }
    }
}
