package com.example.castapp.ui.windowsettings

import android.content.Context
import android.text.SpannableString
import android.widget.FrameLayout
import android.widget.RelativeLayout
import com.example.castapp.R
import com.example.castapp.manager.WindowSettingsManager
import com.example.castapp.ui.view.TextEditPanel
import com.example.castapp.ui.view.TextWindowView
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.TextFormatManager
import com.example.castapp.utils.TextSizeManager

/**
 * 文本窗口管理器
 * 负责管理文本窗口的显示、编辑和格式化功能
 */
class TextWindowManager(
    private val context: Context,
    private val transformHandler: TransformHandler
) {
    
    // UI组件
    private var textWindowView: TextWindowView? = null
    private var textEditPanel: TextEditPanel? = null
    
    // 文本数据
    private var textId: String = ""
    private var currentTextContent: String = "默认文字"
    private var isBoldEnabled: Boolean = false
    private var isItalicEnabled: Boolean = false
    private var currentFontSize: Int = 13 // 默认字号13sp
    
    // 文本格式管理器
    private val textFormatManager = TextFormatManager(context)

    // 文本尺寸管理器
    private val textSizeManager = TextSizeManager(context)
    
    /**
     * 设置文本视图
     */
    fun setupTextView(textId: String, textContent: String) {
        this.textId = textId
        this.currentTextContent = textContent
        
        try {
            // 从存储中恢复格式状态
            restoreFormatState()
            
            // 创建文本视图
            textWindowView = TextWindowView(context).apply {
                // 🎯 关键修复：设置tag为textId，用于SharedPreferences查询
                tag = textId
                setTextContent(currentTextContent)
                setBoldEnabled(isBoldEnabled)
                setItalicEnabled(isItalicEnabled)
                setFontSize(currentFontSize)

                // 设置编辑状态监听器
                setOnEditStateChangeListener { isEditing ->
                    if (isEditing) {
                        AppLog.d("【文本窗口管理器】文本窗口进入编辑模式")
                        // 启用边框拖动调整大小功能
                        enableBorderResizing()
                    } else {
                        AppLog.d("【文本窗口管理器】文本窗口退出编辑模式")
                        // 禁用边框拖动调整大小功能
                        disableBorderResizing()
                        // 📝 关键修复：退出编辑模式时保存格式状态
                        saveFormatState()
                    }
                }

                // 设置文本变化监听器
                setOnTextChangeListener { newText ->
                    currentTextContent = newText
                    AppLog.d("【文本窗口管理器】文本内容已更新: $newText")

                    // 🔄 关键修复：保存文本内容到TextFormatManager，确保窗口管理界面能获取最新内容
                    saveFormatState()

                    // 🔄 更新设备信息显示并刷新窗口管理界面
                    transformHandler.setDeviceInfo(
                        deviceName = currentTextContent,
                        ipAddress = "本地设备",
                        port = 0
                    )

                    // 🔄 立即刷新窗口管理和层级管理对话框
                    try {
                        val windowSettingsManager = WindowSettingsManager.getInstance()
                        windowSettingsManager.refreshWindowManagerDialog()
                        windowSettingsManager.refreshLayerManagerDialog()
                    } catch (e: Exception) {
                        AppLog.e("【文本窗口管理器】刷新对话框失败", e)
                    }
                }

                // 设置尺寸变化监听器
                setOnSizeChangeListener { newWidth, newHeight ->
                    AppLog.d("【文本窗口管理器】文本窗口尺寸已变化: ${newWidth}x${newHeight}")
                    // 保存新尺寸到偏好设置
                    textSizeManager.saveTextWindowSize(textId, newWidth, newHeight)
                    // 🎯 关键修复：同时更新TransformHandler的基础窗口尺寸
                    updateTransformHandlerSize(newWidth, newHeight)
                    // 🎯 关键修复：更新TransformHandler的基础尺寸，确保布局保存时获取正确尺寸
                    transformHandler.updateBaseWindowSize(newWidth, newHeight)
                }

                // 设置选择变化监听器（包含所有格式信息）
                setOnSelectionChangeWithAllFormatsListener { bold, italic, textColor, strokeState ->
                    // 获取选中文字的完整格式状态（包含字号）
                    val formatState = getSelectionFormatStateWithFontSize()
                    val fontSize = formatState.third

                    // 获取选中文字的字体
                    val fontFamily = getSelectionFont()

                    // 获取选中文字的字间距
                    val letterSpacing = getSelectionLetterSpacing()

                    // 更新编辑面板按钮状态（包含颜色、描边、字体和字间距）
                    textEditPanel?.updateButtonStatesFromSelection(bold, italic, fontSize, textColor, strokeState, fontFamily, letterSpacing)
                    AppLog.v("【文本窗口管理器】选择变化，按钮状态已更新: 加粗=$bold, 倾斜=$italic, 字号=${fontSize}sp, 字体=${fontFamily?.name}, 字间距=${letterSpacing}em, 颜色=${textColor?.let { String.format("#%08X", it) } ?: "默认"}, 描边=${strokeState?.let { "(${it.first}, ${it.second}px, ${String.format("#%08X", it.third)})" } ?: "无"}")
                }

                // 🎯 移除：双击检测现在在父容器（TransformHandler）中处理
                // setOnDoubleClickListener { ... }
            }
            
            // 使用TransformHandler的尺寸，确保一致性
            val layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            transformHandler.addView(textWindowView, layoutParams)
            
            AppLog.d("【文本窗口管理器】文本视图设置完成: ID=$textId, 文本=$textContent")
            
        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】设置文本视图失败", e)
        }
    }
    
    /**
     * 显示编辑面板（公共方法）
     */
    fun showEditPanel() {
        try {
            // 让文本窗口进入编辑模式
            textWindowView?.enterEditMode()

            // 🎯 关键修复：进入编辑模式后，同步格式状态
            syncFormatStateFromTextView()

            // 获取主容器（MainActivity的主容器）
            val mainContainer = getMainContainer()
            if (mainContainer == null) {
                AppLog.w("【文本窗口管理器】无法获取主容器，无法显示编辑面板")
                return
            }

            // 如果编辑面板已存在且正在显示，则不重复创建
            if (textEditPanel?.isShowing() == true) {
                AppLog.d("【文本窗口管理器】编辑面板已在显示中")
                return
            }

            // 创建编辑面板
            textEditPanel = TextEditPanel(context, mainContainer)

            // 设置监听器
            textEditPanel?.setOnFormatChangeListener { bold, italic ->
                applyFormatChangesRealTime(bold, italic)
            }

            textEditPanel?.setOnFontSizeChangeListener { fontSize ->
                applyFontSizeChangeRealTime(fontSize)
            }

            textEditPanel?.setOnFontFamilyChangeListener { fontItem ->
                applyFontFamilyChangeRealTime(fontItem)
            }

            textEditPanel?.setOnLetterSpacingChangeListener { letterSpacing ->
                applyLetterSpacingChangeRealTime(letterSpacing)
            }

            textEditPanel?.setOnLineSpacingChangeListener { lineSpacing ->
                applyLineSpacingChangeRealTime(lineSpacing)
            }

            textEditPanel?.setOnTextAlignmentChangeListener { alignment ->
                applyTextAlignmentChangeRealTime(alignment)
            }

            textEditPanel?.setOnColorChangeListener { color ->
                applyColorChangeRealTime(color)
            }

            textEditPanel?.setOnStrokeChangeListener { enabled, width, color ->
                applyStrokeChangeRealTime(enabled, width, color)
            }

            textEditPanel?.setOnWindowColorChangeListener { enabled, color ->
                applyWindowColorChangeRealTime(enabled, color)
            }

            textEditPanel?.setOnClearFormatListener {
                clearSelectionFormat()
            }

            textEditPanel?.setOnCloseListener {
                AppLog.d("【文本窗口管理器】用户关闭编辑面板")
                textWindowView?.exitEditMode()

                // 🔧 修复：用户通过关闭图标退出编辑模式时，同步更新编辑开关状态
                try {
                    val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
                    val connectionId = transformHandler.getConnectionId()
                    windowSettingsManager.setEditState(connectionId, false)
                    AppLog.d("【文本窗口管理器】已同步更新编辑开关状态为关闭: $connectionId")
                } catch (e: Exception) {
                    AppLog.e("【文本窗口管理器】更新编辑开关状态失败", e)
                }
            }

            // 设置文本窗口拖动监听器 - 让编辑面板的拖动手柄控制文本窗口位置
            textEditPanel?.setOnTextWindowDragListener { deltaX, deltaY ->
                transformHandler.performDragByHandle(deltaX, deltaY)
            }

            textEditPanel?.setOnTextWindowDragEndListener {
                transformHandler.notifyDragEnd()
            }

            // 🎯 关键修复：从TextWindowView获取当前窗口颜色状态并初始化到编辑面板
            val windowColorState = textWindowView?.getWindowBackgroundColorState()
            if (windowColorState != null) {
                textEditPanel?.initializeWindowColorState(windowColorState.first, windowColorState.second)
                AppLog.d("【文本窗口管理器】已初始化编辑面板窗口颜色状态: 启用=${windowColorState.first}, 颜色=${String.format("#%08X", windowColorState.second)}")
            }

            // 🎯 关键修复：获取实际的文字格式状态来显示编辑面板
            val actualFormatState = textWindowView?.getSelectionFormatStateWithFontSize() ?: Triple(isBoldEnabled, isItalicEnabled, currentFontSize)
            val actualBold = actualFormatState.first
            val actualItalic = actualFormatState.second
            val actualFontSize = actualFormatState.third

            // 获取其他格式信息
            val currentAlignment = textWindowView?.getTextGravity() ?: (android.view.Gravity.CENTER_HORIZONTAL or android.view.Gravity.CENTER_VERTICAL)
            val currentFontFamily = textWindowView?.getSelectionFont()
            val currentLetterSpacing = textWindowView?.getSelectionLetterSpacing() ?: 0.0f

            // 🎯 关键修复：行间距从SharedPreferences获取，因为它是TextView级别的属性
            val currentLineSpacing = getCurrentLineSpacing()

            AppLog.d("【文本窗口管理器】编辑面板格式状态: 行间距=${currentLineSpacing}dp, 对齐=$currentAlignment")

            // 显示编辑面板，使用实际的格式状态
            textEditPanel?.show(
                currentText = currentTextContent,
                bold = actualBold,
                italic = actualItalic,
                fontSize = actualFontSize,
                fontFamily = currentFontFamily,
                letterSpacing = currentLetterSpacing,
                lineSpacing = currentLineSpacing,
                textAlignment = currentAlignment
            )

            AppLog.d("【文本窗口管理器】编辑面板已显示，使用实际格式状态: 加粗=$actualBold, 倾斜=$actualItalic, 字号=${actualFontSize}sp")

            // 🔧 修复：编辑面板显示完成后，延迟刷新对话框以更新编辑开关状态
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                try {
                    val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
                    windowSettingsManager.refreshWindowManagerDialog()
                    AppLog.d("【文本窗口管理器】编辑面板显示完成，已触发对话框刷新")
                } catch (e: Exception) {
                    AppLog.e("【文本窗口管理器】触发对话框刷新失败", e)
                }
            }, 200) // 延迟200ms确保编辑面板完全显示

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】显示编辑面板失败", e)
        }
    }

    /**
     * 隐藏编辑面板（公共方法）
     */
    fun hideEditPanel() {
        try {
            // 隐藏编辑面板
            textEditPanel?.hide()

            // 让文本窗口退出编辑模式
            textWindowView?.exitEditMode()

            AppLog.d("【文本窗口管理器】编辑面板已隐藏，退出编辑模式")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】隐藏编辑面板失败", e)
        }
    }

    /**
     * 实时应用格式更改到选中文字（不退出编辑模式）
     */
    private fun applyFormatChangesRealTime(bold: Boolean, italic: Boolean) {
        try {
            // 获取当前选中文字的格式状态
            val currentFormatState = textWindowView?.getSelectionFormatState() ?: Pair(false, false)
            val currentBold = currentFormatState.first
            val currentItalic = currentFormatState.second

            // 只有当格式状态发生变化时才应用
            if (bold != currentBold) {
                textWindowView?.applyBoldToSelection(bold)
                AppLog.d("【文本窗口管理器】对选中文字应用加粗: $bold")
            }

            if (italic != currentItalic) {
                textWindowView?.applyItalicToSelection(italic)
                AppLog.d("【文本窗口管理器】对选中文字应用倾斜: $italic")
            }

            // 更新全局格式状态（用于新输入的文字）
            isBoldEnabled = bold
            isItalicEnabled = italic

            // 保存格式状态
            saveFormatState()

            // 更新设备信息显示
            transformHandler.setDeviceInfo(
                deviceName = currentTextContent,
                ipAddress = "本地设备",
                port = 0
            )

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】实时应用格式更改失败", e)
        }
    }

    /**
     * 实时应用字号更改到选中文字（不退出编辑模式）
     */
    private fun applyFontSizeChangeRealTime(fontSize: Int) {
        try {
            // 应用字号到选中文字或全局
            textWindowView?.applyFontSizeToSelection(fontSize)

            // 更新全局字号状态
            currentFontSize = fontSize

            // 保存格式状态
            saveFormatState()

            AppLog.d("【文本窗口管理器】对选中文字应用字号: ${fontSize}sp")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】实时应用字号更改失败", e)
        }
    }

    /**
     * 实时应用字体更改到选中文字（不退出编辑模式）
     */
    private fun applyFontFamilyChangeRealTime(fontItem: com.example.castapp.utils.FontPresetManager.FontItem) {
        try {
            // 应用字体到选中文字或全局
            textWindowView?.applyFontFamilyToSelection(fontItem)

            // 保存格式状态
            saveFormatState()

            AppLog.d("【文本窗口管理器】对选中文字应用字体: ${fontItem.name}")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】实时应用字体更改失败", e)
        }
    }

    /**
     * 实时应用字间距更改到选中文字（不退出编辑模式）
     */
    private fun applyLetterSpacingChangeRealTime(letterSpacing: Float) {
        try {
            // 应用字间距到选中文字或全局
            textWindowView?.applyLetterSpacingToSelection(letterSpacing)

            // 保存格式状态
            saveFormatState()

            AppLog.d("【文本窗口管理器】对选中文字应用字间距: ${letterSpacing}em")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】实时应用字间距更改失败", e)
        }
    }

    /**
     * 实时应用行间距更改到选中文字（不退出编辑模式）
     */
    private fun applyLineSpacingChangeRealTime(lineSpacing: Float) {
        try {
            // 应用行间距到选中文字或全局
            textWindowView?.applyLineSpacingToSelection(lineSpacing)

            // 保存格式状态
            saveFormatState()

            // 📝 关键修复：单独保存行间距到SharedPreferences
            saveLineSpacingToPreferences(lineSpacing)

            AppLog.d("【文本窗口管理器】对选中文字应用行间距: ${lineSpacing}dp")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】实时应用行间距更改失败", e)
        }
    }

    /**
     * 实时应用对齐更改到选中文字（不退出编辑模式）
     */
    private fun applyTextAlignmentChangeRealTime(alignment: Int) {
        try {
            // 应用对齐方式到选中文字或全局
            textWindowView?.applyTextAlignmentToSelection(alignment)

            // 保存格式状态
            saveFormatState()

            AppLog.d("【文本窗口管理器】对选中文字应用对齐方式: $alignment")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】实时应用对齐更改失败", e)
        }
    }

    /**
     * 实时应用颜色更改到选中文字（不退出编辑模式）
     */
    private fun applyColorChangeRealTime(color: Int) {
        try {
            // 应用颜色到选中文字
            textWindowView?.applyColorToSelection(color)

            AppLog.d("【文本窗口管理器】对选中文字应用颜色: ${String.format("#%08X", color)}")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】实时应用颜色更改失败", e)
        }
    }

    /**
     * 实时应用描边更改到选中文字（不退出编辑模式）
     */
    private fun applyStrokeChangeRealTime(enabled: Boolean, strokeWidth: Float, strokeColor: Int) {
        try {
            // 应用描边到选中文字
            textWindowView?.applyStrokeToSelection(enabled, strokeWidth, strokeColor)

            AppLog.d("【文本窗口管理器】对选中文字应用描边: 启用=$enabled, 宽度=${strokeWidth}px, 颜色=${String.format("#%08X", strokeColor)}")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】实时应用描边更改失败", e)
        }
    }

    /**
     * 实时应用窗口颜色更改（不退出编辑模式）
     */
    private fun applyWindowColorChangeRealTime(enabled: Boolean, color: Int) {
        try {
            // 应用窗口背景颜色
            textWindowView?.setWindowBackgroundColor(enabled, color)

            AppLog.d("【文本窗口管理器】窗口背景颜色已应用: 启用=$enabled, 颜色=${String.format("#%08X", color)}")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】实时应用窗口颜色更改失败", e)
        }
    }

    /**
     * 清除选中文字的格式
     */
    private fun clearSelectionFormat() {
        try {
            textWindowView?.clearSelectionFormat()

            // 重置全局格式状态
            isBoldEnabled = false
            isItalicEnabled = false
            currentFontSize = 13 // 重置为默认字号

            // 🎯 关键修复：重置对齐方式为默认居中对齐
            val defaultAlignment = android.view.Gravity.CENTER_HORIZONTAL or android.view.Gravity.CENTER_VERTICAL
            textWindowView?.setTextGravity(defaultAlignment)

            // 保存格式状态
            saveFormatState()

            AppLog.d("【文本窗口管理器】选中文字格式已清除，对齐方式已重置为居中")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】清除选中文字格式失败", e)
        }
    }
    
    /**
     * 获取主容器
     */
    private fun getMainContainer(): RelativeLayout? {
        return try {
            // 通过Context获取Activity，然后获取主容器
            val activity = context as? android.app.Activity
            activity?.findViewById(R.id.main_container)
        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】获取主容器失败", e)
            null
        }
    }
    
    /**
     * 保存格式状态
     */
    private fun saveFormatState() {
        // 📝 优先保存富文本格式
        textWindowView?.let { textView ->
            val currentText = textView.text
            if (currentText != null && currentText.isNotEmpty()) {
                // 创建SpannableString来保存当前的格式信息
                val spannableString = if (currentText is android.text.SpannableString) {
                    currentText
                } else {
                    // 将Editable转换为SpannableString，保持所有格式
                    android.text.SpannableString(currentText)
                }

                // 保存富文本格式（包含所有格式信息，无需重复保存基本格式）
                textFormatManager.saveRichTextFormat(textId, spannableString)
                AppLog.d("【文本窗口管理器】富文本格式状态已保存: ID=$textId, 文本长度=${spannableString.length}, 类型=${currentText::class.simpleName}")
            } else {
                // 后备方案：仅在文本为空时保存基本格式
                textFormatManager.saveTextFormat(textId, currentTextContent, isBoldEnabled, isItalicEnabled, currentFontSize)
                AppLog.d("【文本窗口管理器】基本格式状态已保存（文本为空）: ID=$textId, 字号=${currentFontSize}sp")
            }
        } ?: run {
            // 后备方案：仅在无法获取TextWindowView时保存基本格式
            textFormatManager.saveTextFormat(textId, currentTextContent, isBoldEnabled, isItalicEnabled, currentFontSize)
            AppLog.d("【文本窗口管理器】基本格式状态已保存（无法获取视图）: ID=$textId, 字号=${currentFontSize}sp")
        }
    }
    
    /**
     * 恢复格式状态
     */
    private fun restoreFormatState() {
        val formatInfo = textFormatManager.getTextFormat(textId)
        if (formatInfo != null) {
            currentTextContent = formatInfo.textContent
            isBoldEnabled = formatInfo.isBold
            isItalicEnabled = formatInfo.isItalic
            currentFontSize = formatInfo.fontSize
            AppLog.d("【文本窗口管理器】格式状态已恢复: ID=$textId, 文本=$currentTextContent, 字号=${currentFontSize}sp")
        } else {
            AppLog.d("【文本窗口管理器】未找到保存的格式状态，使用默认值: ID=$textId")
        }
    }

    /**
     * 🎯 从TextWindowView同步格式状态到管理器
     */
    private fun syncFormatStateFromTextView() {
        try {
            textWindowView?.let { textView ->
                if (textView.isInEditMode) {
                    // 获取选中文字的完整格式状态
                    val formatState = textView.getSelectionFormatStateWithFontSize()
                    val newBold = formatState.first
                    val newItalic = formatState.second
                    val newFontSize = formatState.third

                    // 更新管理器的格式状态
                    if (isBoldEnabled != newBold || isItalicEnabled != newItalic || currentFontSize != newFontSize) {
                        isBoldEnabled = newBold
                        isItalicEnabled = newItalic
                        currentFontSize = newFontSize
                        AppLog.d("【文本窗口管理器】基本格式状态已从TextWindowView同步: 加粗=$isBoldEnabled, 倾斜=$isItalicEnabled, 字号=${currentFontSize}sp")
                    }

                    // 🎯 关键修复：同步TextView级别的属性（行间距和对齐方式）
                    // 这些属性不能从选中文字检测，需要从TextView本身或SharedPreferences获取
                    val currentLineSpacing = getCurrentLineSpacing()
                    val currentAlignment = textView.getTextGravity()

                    AppLog.d("【文本窗口管理器】TextView级别属性已同步: 行间距=${currentLineSpacing}dp, 对齐=$currentAlignment")
                }
            }
        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】同步格式状态失败", e)
        }
    }

    /**
     * 🎨 获取文本窗口视图
     */
    fun getTextWindowView(): TextWindowView? = textWindowView

    /**
     * 📝 刷新格式显示（用于布局恢复后刷新文本格式）
     */
    fun refreshFormatDisplay() {
        try {
            // 📝 优先尝试恢复富文本格式
            val richTextSpannable = textFormatManager.getRichTextFormat(textId)

            if (richTextSpannable != null) {
                // 恢复富文本格式
                textWindowView?.let { textView ->
                    textView.setText(richTextSpannable)
                    currentTextContent = richTextSpannable.toString()
                    AppLog.d("【文本窗口管理器】富文本格式显示已刷新: ID=$textId, 内容=$currentTextContent, 文本长度=${richTextSpannable.length}")
                }
            } else {
                // 后备方案：使用基本格式恢复
                restoreFormatState()

                textWindowView?.let { textView ->
                    // 设置文本内容
                    textView.setText(currentTextContent)

                    // 应用格式到整个文本
                    textView.applyFormatToAllText(isBoldEnabled, isItalicEnabled, currentFontSize)

                    AppLog.d("【文本窗口管理器】基本格式显示已刷新: ID=$textId, 内容=$currentTextContent, 加粗=$isBoldEnabled, 倾斜=$isItalicEnabled, 字号=${currentFontSize}sp")
                }
            }

            // 更新设备信息显示
            transformHandler.setDeviceInfo(
                deviceName = currentTextContent,
                ipAddress = "本地设备",
                port = 0
            )

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】刷新格式显示失败: ID=$textId", e)
        }
    }

    /**
     * 更新TransformHandler的尺寸
     */
    private fun updateTransformHandlerSize(newWidth: Int, newHeight: Int) {
        try {
            // 更新TransformHandler的布局参数
            val layoutParams = transformHandler.layoutParams
            layoutParams.width = newWidth
            layoutParams.height = newHeight
            transformHandler.layoutParams = layoutParams

            AppLog.d("【文本窗口管理器】TransformHandler尺寸已更新: ${newWidth}x${newHeight}")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】更新TransformHandler尺寸失败", e)
        }
    }

    /**
     * 设置文本窗口尺寸（公共方法，用于布局恢复）
     */
    fun setTextWindowSize(newWidth: Int, newHeight: Int) {
        try {
            // 更新 TextWindowView 的尺寸
            textWindowView?.setWindowSize(newWidth, newHeight)

            // 更新 TransformHandler 的尺寸
            updateTransformHandlerSize(newWidth, newHeight)

            // 更新 TransformHandler 的基础尺寸
            transformHandler.updateBaseWindowSize(newWidth, newHeight)

            // 保存尺寸到 TextSizeManager
            textSizeManager.saveTextWindowSize(textId, newWidth, newHeight)

            AppLog.d("【文本窗口管理器】文本窗口尺寸设置完成: ${newWidth}x${newHeight}")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】设置文本窗口尺寸失败", e)
        }
    }

    /**
     * 启用边框拖动调整大小功能
     */
    private fun enableBorderResizing() {
        try {
            // 🔧 修复：进入编辑模式时，临时启用拖动功能（用于编辑面板拖动手柄）
            // 注意：这里不影响用户设置的拖动开关状态，只是临时启用TransformHandler的拖动功能
            transformHandler.setDragEnabled(true)

            AppLog.d("【文本窗口管理器】边框拖动调整大小功能已启用（临时启用拖动功能）")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】启用边框拖动功能失败", e)
        }
    }

    /**
     * 禁用边框拖动调整大小功能
     */
    private fun disableBorderResizing() {
        try {
            // 🔧 修复：退出编辑模式时，恢复到用户设置的拖动开关状态
            val connectionId = transformHandler.getConnectionId()
            val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
            val userDragState = windowSettingsManager.getDragSwitchState(connectionId)

            // 恢复到用户设置的拖动状态
            transformHandler.setDragEnabled(userDragState)

            AppLog.d("【文本窗口管理器】边框拖动调整大小功能已禁用，拖动功能恢复到用户设置状态: $userDragState")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】禁用边框拖动功能失败", e)
        }
    }

    /**
     * 📝 保存扩展格式状态（包含字体名称、行间距等完整信息）
     */
    fun saveExtendedFormatState() {
        try {
            val currentTextContent = textWindowView?.text?.toString() ?: ""
            val currentText = textWindowView?.text

            // 获取当前字体信息
            val currentFontName = getCurrentFontName()
            val currentFontFamily = getCurrentFontFamily()
            val currentLineSpacing = getCurrentLineSpacing()
            val currentAlignment = getCurrentTextAlignment()

            // 检查是否为SpannableString并且不为空
            if (currentText is SpannableString && currentText.isNotEmpty()) {
                // 保存富文本格式（包含所有格式信息）
                textFormatManager.saveRichTextFormat(textId, currentText)
                AppLog.d("【文本窗口管理器】富文本扩展格式状态已保存: ID=$textId")
            } else {
                // 保存扩展格式信息
                textFormatManager.saveExtendedTextFormat(
                    textId = textId,
                    textContent = currentTextContent,
                    isBold = isBoldEnabled,
                    isItalic = isItalicEnabled,
                    fontSize = currentFontSize,
                    fontName = currentFontName,
                    fontFamily = currentFontFamily,
                    lineSpacing = currentLineSpacing,
                    textAlignment = currentAlignment
                )
                AppLog.d("【文本窗口管理器】扩展格式状态已保存: ID=$textId, 字体=$currentFontName, 行间距=${currentLineSpacing}dp")
            }
        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】保存扩展格式状态失败", e)
        }
    }

    /**
     * 📝 恢复扩展格式状态（从数据库布局恢复时使用）
     */
    fun restoreExtendedFormatState(
        textContent: String?,
        isBold: Boolean,
        isItalic: Boolean,
        fontSize: Int,
        fontName: String?,
        lineSpacing: Float,
        textAlignment: Int,
        richTextData: String?
    ) {
        try {
            // 📝 调试：记录接收到的参数
            AppLog.d("【文本窗口管理器】开始恢复扩展格式状态: ID=$textId")
            AppLog.d("【文本窗口管理器】参数 - 行间距: ${lineSpacing}dp, 对齐: $textAlignment, 字体: $fontName")
            AppLog.d("【文本窗口管理器】参数 - 富文本数据长度: ${richTextData?.length ?: 0}")

            if (lineSpacing > 0.0f) {
                AppLog.d("【文本窗口管理器】✅ 接收到有效行间距参数: ${lineSpacing}dp")
            } else {
                AppLog.w("【文本窗口管理器】⚠️ 接收到的行间距参数为0或无效: ${lineSpacing}dp")
            }
            // 优先恢复富文本格式
            if (!richTextData.isNullOrBlank()) {
                // 先保存富文本数据到SharedPreferences
                val sharedPrefs = context.getSharedPreferences("text_format_preferences", Context.MODE_PRIVATE)
                sharedPrefs.edit().putString("rich_text_data_$textId", richTextData).apply()

                // 恢复富文本格式
                val richTextSpannable = textFormatManager.getRichTextFormat(textId)
                if (richTextSpannable != null) {
                    textWindowView?.setText(richTextSpannable)

                    // 📝 关键修复：即使恢复了富文本格式，也要恢复行间距（TextView级别属性）
                    textWindowView?.let { view ->
                        if (lineSpacing > 0.0f) {
                            val lineSpacingExtra = lineSpacing * view.resources.displayMetrics.density
                            view.setLineSpacing(lineSpacingExtra, 1.0f)
                            AppLog.d("【文本窗口管理器】富文本模式下行间距已恢复: ${lineSpacing}dp")
                        }

                        // 设置文本对齐
                        view.gravity = textAlignment
                        AppLog.d("【文本窗口管理器】富文本模式下文本对齐已恢复: $textAlignment")
                    }

                    // 保存扩展格式状态到SharedPreferences
                    saveExtendedFormatToPreferences(fontName, lineSpacing, textAlignment)

                    AppLog.d("【文本窗口管理器】富文本格式已恢复（包含行间距和对齐）: ID=$textId")
                    return
                }
            }

            // 后备方案：恢复扩展格式
            textWindowView?.let { view ->
                // 设置文本内容
                if (!textContent.isNullOrBlank()) {
                    view.setTextContent(textContent)
                }

                // 设置基本格式
                view.setBoldEnabled(isBold)
                view.setItalicEnabled(isItalic)
                view.setFontSizeSp(fontSize)

                // 设置字体（直接使用setFontFamily，不需要编辑模式）
                if (!fontName.isNullOrBlank()) {
                    try {
                        // 确保FontPresetManager已初始化
                        com.example.castapp.utils.FontPresetManager.initialize(context)

                        val fontItem = com.example.castapp.utils.FontPresetManager.getFontByName(fontName)
                        if (fontItem != null) {
                            view.setFontFamily(fontItem)
                            AppLog.d("【文本窗口管理器】字体已恢复: $fontName")
                        } else {
                            AppLog.w("【文本窗口管理器】未找到字体: $fontName")
                        }
                    } catch (e: Exception) {
                        AppLog.e("【文本窗口管理器】恢复字体失败: $fontName", e)
                    }
                }

                // 设置行间距（直接使用TextView的setLineSpacing方法）
                if (lineSpacing > 0.0f) {
                    val lineSpacingExtra = lineSpacing * view.resources.displayMetrics.density
                    view.setLineSpacing(lineSpacingExtra, 1.0f)
                    AppLog.d("【文本窗口管理器】行间距已恢复: ${lineSpacing}dp")
                }

                // 设置文本对齐
                view.gravity = textAlignment
                AppLog.d("【文本窗口管理器】文本对齐已恢复: $textAlignment")

                // 保存扩展格式状态到SharedPreferences
                saveExtendedFormatToPreferences(fontName, lineSpacing, textAlignment)
            }

            AppLog.d("【文本窗口管理器】扩展格式状态已恢复: ID=$textId, 字体=$fontName, 行间距=${lineSpacing}dp")

        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】恢复扩展格式状态失败", e)
        }
    }

    /**
     * 📝 获取当前字体名称
     */
    private fun getCurrentFontName(): String? {
        return try {
            // 从SharedPreferences获取当前字体名称
            val sharedPrefs = context.getSharedPreferences("text_format_preferences", Context.MODE_PRIVATE)
            sharedPrefs.getString("font_name_$textId", null)
        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】获取当前字体名称失败", e)
            null
        }
    }

    /**
     * 📝 获取当前字体族
     */
    private fun getCurrentFontFamily(): String? {
        return try {
            val sharedPrefs = context.getSharedPreferences("text_format_preferences", Context.MODE_PRIVATE)
            sharedPrefs.getString("font_family_$textId", null)
        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】获取当前字体族失败", e)
            null
        }
    }

    /**
     * 📝 获取当前行间距
     */
    private fun getCurrentLineSpacing(): Float {
        return try {
            val sharedPrefs = context.getSharedPreferences("text_format_preferences", Context.MODE_PRIVATE)
            sharedPrefs.getFloat("line_spacing_$textId", 0.0f)
        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】获取当前行间距失败", e)
            0.0f
        }
    }

    /**
     * 📝 获取当前文本对齐方式
     */
    private fun getCurrentTextAlignment(): Int {
        return try {
            val sharedPrefs = context.getSharedPreferences("text_format_preferences", Context.MODE_PRIVATE)
            sharedPrefs.getInt("text_alignment_$textId", android.view.Gravity.CENTER)
        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】获取当前文本对齐失败", e)
            android.view.Gravity.CENTER
        }
    }

    /**
     * 📝 保存行间距到SharedPreferences
     */
    private fun saveLineSpacingToPreferences(lineSpacing: Float) {
        try {
            val sharedPrefs = context.getSharedPreferences("text_format_preferences", Context.MODE_PRIVATE)
            sharedPrefs.edit().apply {
                putFloat("line_spacing_$textId", lineSpacing)
                apply()
            }
            AppLog.d("【文本窗口管理器】行间距已保存到SharedPreferences: ${lineSpacing}dp")
        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】保存行间距失败", e)
        }
    }

    /**
     * 📝 保存扩展格式信息到SharedPreferences（用于布局恢复后的状态同步）
     */
    private fun saveExtendedFormatToPreferences(fontName: String?, lineSpacing: Float, textAlignment: Int) {
        try {
            val sharedPrefs = context.getSharedPreferences("text_format_preferences", Context.MODE_PRIVATE)
            sharedPrefs.edit().apply {
                if (!fontName.isNullOrBlank()) {
                    putString("font_name_$textId", fontName)
                }
                putFloat("line_spacing_$textId", lineSpacing)
                putInt("text_alignment_$textId", textAlignment)
                apply()
            }
            AppLog.d("【文本窗口管理器】扩展格式信息已保存到SharedPreferences: 字体=$fontName, 行间距=${lineSpacing}dp")
        } catch (e: Exception) {
            AppLog.e("【文本窗口管理器】保存扩展格式信息失败", e)
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        textEditPanel?.hide()
        textEditPanel?.destroy() // 清理字号预设管理器监听器
        textEditPanel = null
        textWindowView = null
        AppLog.d("【文本窗口管理器】资源已清理: ID=$textId")
    }
}
