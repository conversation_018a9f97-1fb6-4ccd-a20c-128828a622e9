<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bottom_sheet_background"
    android:paddingTop="16dp"
    android:paddingBottom="16dp">

    <!-- 拖拽指示器 -->
    <View
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="4dp"
        android:background="#CCCCCC"
        android:alpha="0.6" />

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingBottom="4dp">

        <!-- 标题图标 -->
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginEnd="8dp"
            app:tint="#333333"
            android:src="@drawable/ic_layer" />

        <!-- 标题文字 -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="层级管理"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333" />

        <!-- 关闭按钮 -->
        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_close"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="关闭" />

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="12dp" />

    <!-- 设备列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_layer_devices"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:clipToPadding="false"
        android:overScrollMode="never" />

    <!-- 空状态布局 -->
    <LinearLayout
        android:id="@+id/layout_empty_state"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_layer"
            android:alpha="0.3"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="暂无投屏窗口"
            android:textSize="14sp"
            android:textColor="#999999" />

    </LinearLayout>

</LinearLayout>
