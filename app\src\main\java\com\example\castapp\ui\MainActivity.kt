package com.example.castapp.ui

import android.os.Bundle
import android.widget.Button
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import com.example.castapp.R
import com.example.castapp.manager.PermissionManager
import com.example.castapp.manager.WindowSettingsManager
import com.example.castapp.utils.ToastUtils
import com.example.castapp.manager.FloatingWindowManager
import com.example.castapp.manager.PrecisionControlPanelManager
import com.example.castapp.manager.LayoutManager
import com.example.castapp.manager.HideShowManager
import com.example.castapp.viewmodel.MainViewModel
import com.example.castapp.utils.AppLog
import com.example.castapp.viewmodel.SenderViewModel
import com.example.castapp.model.Connection
import android.content.Intent
import android.os.Handler
import android.os.Looper
import com.example.castapp.ui.dialog.RemoteControlManagerDialog
import com.example.castapp.ui.dialog.AddMediaDialogFragment

/**
 * 主界面Activity - 重构为协调者模式
 * 只负责UI初始化和各管理器的协调，具体功能委托给专门的管理器
 */
class MainActivity : AppCompatActivity() {
    private val viewModel: MainViewModel by viewModels()
    private val senderViewModel: SenderViewModel by viewModels()
    private lateinit var activityPermissionHelper: PermissionManager.ActivityPermissionHelper

    // UI组件
    private lateinit var surfaceContainer: FrameLayout
    private lateinit var btnSend: Button
    private lateinit var btnReceive: Button
    private lateinit var btnRemoteControl: Button
    private lateinit var btnStopwatch: Button
    private lateinit var btnLayerManager: Button
    private lateinit var btnWindowManager: Button
    private lateinit var btnSave: Button
    private lateinit var btnDirector: Button
    private lateinit var btnClear: Button
    private lateinit var btnAddMedia: Button
    private lateinit var buttonGroup: LinearLayout

    // 管理器
    private lateinit var mainContainer: RelativeLayout
    private lateinit var precisionControlPanelManager: PrecisionControlPanelManager

    // 管理器实例
    private lateinit var windowSettingsManager: WindowSettingsManager
    private lateinit var floatingWindowManager: FloatingWindowManager
    private lateinit var layoutManager: LayoutManager
    private lateinit var hideShowManager: HideShowManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // MainViewModel会自动设置静态实例引用

        // 初始化权限管理器
        activityPermissionHelper = PermissionManager.ActivityPermissionHelper(this)
        activityPermissionHelper.initialize()

        // 初始化各个管理器
        initializeManagers()

        // 初始化UI
        initViews()
        checkAndRequestPermissions()

        // 🔥 新增：APP启动时预请求MediaProjection权限
        preRequestMediaProjectionPermission()

        // 🔥 新增：APP启动时初始化麦克风管理器
        initializeMicrophoneManager()

        setupClickListeners()
        observeViewModel()

        // 🔥 关键修复：在应用启动时恢复远程被控服务器状态
        restoreRemoteControlServerState()

        // 🔥 新增：在应用启动时恢复固定端口7777 WebSocket服务器状态
        restoreFixedWebSocketServerState()

        // 🎯 新增：在应用启动时自动恢复之前应用的布局
        restoreAppliedLayoutOnStartup()

        AppLog.d("MainActivity创建完成 - 协调者模式")
    }

    /**
     * 初始化各个管理器
     */
    private fun initializeManagers() {
        // 🎯 新增：设置WebSocketManager的ApplicationContext
        com.example.castapp.manager.WebSocketManager.setApplicationContext(this)

        // 获取管理器实例
        windowSettingsManager = WindowSettingsManager.getInstance()
        floatingWindowManager = FloatingWindowManager.getInstance()
        layoutManager = LayoutManager.getInstance()
        hideShowManager = HideShowManager.getInstance()

        // 初始化管理器
        floatingWindowManager.initialize(this)
        layoutManager.initialize(this)

        AppLog.d("所有管理器初始化完成")
    }

    /**
     * 初始化视图
     */
    private fun initViews() {
        surfaceContainer = findViewById(R.id.surface_view_container)
        btnSend = findViewById(R.id.btn_send)
        btnReceive = findViewById(R.id.btn_receive)
        btnRemoteControl = findViewById(R.id.btn_remote_control)
        btnStopwatch = findViewById(R.id.btn_stopwatch)
        btnLayerManager = findViewById(R.id.btn_layer_manager)
        btnWindowManager = findViewById(R.id.btn_window_manager)
        btnSave = findViewById(R.id.btn_save)
        btnDirector = findViewById(R.id.btn_director)
        btnClear = findViewById(R.id.btn_clear)
        btnAddMedia = findViewById(R.id.btn_add_media)
        buttonGroup = findViewById(R.id.button_group)
        mainContainer = findViewById(R.id.main_container)

        // 初始化CastWindowManager（需要容器引用）
        windowSettingsManager.initialize(this, surfaceContainer)

        // 初始化精准控制面板管理器
        initializePrecisionControlPanelManager()

        // 初始化清屏功能管理器
        initializeHideShowManager()
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        btnSend.setOnClickListener {
            viewModel.showSenderDialog()
        }

        btnReceive.setOnClickListener {
            viewModel.showReceiverDialog()
        }

        btnRemoteControl.setOnClickListener {
            viewModel.showRemoteControlManagerDialog()
        }

        btnStopwatch.setOnClickListener {
            // 委托给FloatingWindowManager处理
            floatingWindowManager.requestOverlayPermissionAndStartStopwatch(activityPermissionHelper)
        }

        btnLayerManager.setOnClickListener {
            // 委托给CastWindowManager处理层级管理
            windowSettingsManager.showLayerManagerDialog()
        }

        btnWindowManager.setOnClickListener {
            // 委托给CastWindowManager处理
            windowSettingsManager.showWindowManagerDialog()
        }

        btnSave.setOnClickListener {
            // 保存当前窗口布局
            handleSaveLayout()
        }

        btnDirector.setOnClickListener {
            // 显示导播台
            handleShowDirector()
        }

        btnClear.setOnClickListener {
            // 委托给HideShowManager处理清屏功能
            hideShowManager.handleClearScreen(buttonGroup)
        }

        btnAddMedia.setOnClickListener {
            // 显示添加媒体对话框
            showAddMediaDialog()
        }
    }

    /**
     * 观察ViewModel状态变化 - 重构为事件分发
     */
    private fun observeViewModel() {
        // 对话框显示事件
        viewModel.showSenderDialog.observe(this) { shouldShow ->
            if (shouldShow) {
                showSenderDialog()
                viewModel.hideSenderDialog()
            }
        }

        viewModel.showReceiverDialog.observe(this) { shouldShow ->
            if (shouldShow) {
                showReceiverDialog()
                viewModel.hideReceiverDialog()
            }
        }

        viewModel.showRemoteControlManagerDialog.observe(this) { shouldShow ->
            if (shouldShow) {
                showRemoteControlManagerDialog()
                viewModel.hideRemoteControlManagerDialog()
            }
        }

        // 🔥 关键修复：监听SenderViewModel的权限请求，确保即使设置窗口关闭也能处理权限请求
        observeSenderViewModelPermissionRequests()

        // Toast消息事件
        viewModel.toastMessage.observe(this) { message ->
            if (!message.isNullOrEmpty()) {
                ToastUtils.showToast(this, message)
            }
        }

        // 投屏连接状态事件
        viewModel.castingConnections.observe(this) { connections ->
            AppLog.d("当前投屏连接数: ${connections.size}")
        }

        // 新连接事件 - 委托给CastWindowManager
        viewModel.newConnectionEvent.observe(this) { connectionId ->
            if (!connectionId.isNullOrEmpty()) {
                windowSettingsManager.handleNewConnection(connectionId)
            }
        }

        // 移除连接事件 - 委托给CastWindowManager
        viewModel.removeConnectionEvent.observe(this) { connectionId ->
            if (!connectionId.isNullOrEmpty()) {
                windowSettingsManager.removeTextureViewForConnection(connectionId)
            }
        }

        // 移除所有连接事件 - 委托给CastWindowManager
        viewModel.removeAllConnectionsEvent.observe(this) { shouldRemoveAll ->
            if (shouldRemoveAll == true) {
                windowSettingsManager.removeAllTextureViews()
                AppLog.d("收到移除所有连接事件，已委托给CastWindowManager处理")
            }
        }

        // 屏幕分辨率事件 - 委托给CastWindowManager
        viewModel.screenResolutionEvent.observe(this) { (connectionId, width, height) ->
            windowSettingsManager.handleScreenResolution(connectionId, width, height)
        }

        // 设备信息更新事件 - 通知CastWindowManager刷新窗口管理对话框
        viewModel.deviceInfoUpdatedEvent.observe(this) { connectionId ->
            AppLog.d("收到设备信息更新事件: $connectionId")
            // 主动刷新窗口管理对话框以显示最新的设备名称
            windowSettingsManager.refreshWindowManagerDialog()
        }

        // 🎯 横竖屏适配：视频方向变化事件 - 委托给CastWindowManager
        viewModel.videoOrientationChangedEvent.observe(this) { triple ->
            val connectionId = triple.first
            val orientation = triple.second
            val (videoWidth, videoHeight) = triple.third
            AppLog.d("🎯 收到视频方向变化事件: $connectionId, 方向: $orientation, 分辨率: ${videoWidth}×${videoHeight}")
            windowSettingsManager.handleVideoOrientationChanged(connectionId, orientation, videoWidth, videoHeight)
        }
    }

    /**
     * 检查并请求权限 - 使用新的Activity Result API
     */
    private fun checkAndRequestPermissions() {
        activityPermissionHelper.requestBasicPermissions(object : PermissionManager.PermissionCallback {
            override fun onPermissionGranted() {
                AppLog.d("所有基础权限已授予")
            }

            override fun onPermissionDenied(deniedPermissions: List<String>) {
                AppLog.w("以下权限被拒绝: ${deniedPermissions.joinToString()}")
                ToastUtils.showToast(this@MainActivity, "应用需要这些权限才能正常工作")
            }
        })
    }

    /**
     * 🔥 新增：APP启动时预请求MediaProjection权限
     * 实现方案一：启动时静默预请求权限，缓存后供后续功能使用
     */
    private fun preRequestMediaProjectionPermission() {
        AppLog.d("【MainActivity】开始预请求MediaProjection权限")

        // 检查是否已有缓存权限
        val mediaProjectionManager = com.example.castapp.manager.MediaProjectionManager.getInstance(this)
        if (mediaProjectionManager.hasValidPermission()) {
            AppLog.d("【MainActivity】MediaProjection权限已存在，跳过预请求")
            return
        }

        // 请求MediaProjection权限并缓存
        activityPermissionHelper.requestMediaProjection(object : PermissionManager.UnifiedMediaProjectionCallback {
            override fun onMediaProjectionGranted(resultCode: Int, data: Intent) {
                AppLog.d("【MainActivity】MediaProjection权限预请求成功，已缓存供后续使用")
                // 缓存权限数据，但不执行具体功能
                mediaProjectionManager.startMediaProjectionWithPermission(resultCode, data, com.example.castapp.manager.MediaProjectionManager.FEATURE_APP_STARTUP_CACHE)
                ToastUtils.showToast(this@MainActivity, "投屏权限已授予")
            }

            override fun onMediaProjectionDenied() {
                AppLog.w("【MainActivity】MediaProjection权限预请求被拒绝")
                ToastUtils.showToast(this@MainActivity, "投屏权限被拒绝，投屏和媒体音频功能将无法使用")
            }

            override fun onPermissionAlreadyGranted(resultCode: Int, data: Intent) {
                AppLog.d("【MainActivity】MediaProjection权限已存在，无需重复请求")
            }
        })
    }

    /**
     * 🔥 新增：APP启动时初始化麦克风管理器
     * 预创建麦克风AudioRecord实例，避免后台录音权限问题
     */
    private fun initializeMicrophoneManager() {
        AppLog.d("【MainActivity】开始初始化麦克风管理器")

        val microphoneManager = com.example.castapp.manager.MicrophoneManager.getInstance(this)
        val success = microphoneManager.initialize()

        if (success) {
            AppLog.d("【MainActivity】麦克风管理器初始化成功")
            ToastUtils.showToast(this, "麦克风已准备就绪")
        } else {
            AppLog.w("【MainActivity】麦克风管理器初始化失败")
            ToastUtils.showToast(this, "麦克风初始化失败，麦克风功能可能无法正常使用")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理面板管理器资源
        if (::precisionControlPanelManager.isInitialized) {
            precisionControlPanelManager.clearAllPanels()
        }
        // 清理所有管理器
        windowSettingsManager.cleanup()
        floatingWindowManager.cleanup()
        layoutManager.cleanup()
        hideShowManager.cleanup(mainContainer)

        // 🔥 新增：清理麦克风管理器
        try {
            val microphoneManager = com.example.castapp.manager.MicrophoneManager.getInstance(this)
            microphoneManager.destroy()
            AppLog.d("【MainActivity】麦克风管理器已清理")
        } catch (e: Exception) {
            AppLog.e("【MainActivity】清理麦克风管理器失败", e)
        }

        AppLog.d("MainActivity销毁 - 协调者模式")
    }

    /**
     * 显示发送端设置对话框
     */
    private fun showSenderDialog() {
        try {
            val dialog = SenderDialogFragment()
            dialog.show(supportFragmentManager, "SenderDialog")
            AppLog.d("显示发送端对话框")
        } catch (e: Exception) {
            AppLog.e("显示发送端对话框失败", e)
        }
    }

    /**
     * 显示接收端设置对话框
     */
    private fun showReceiverDialog() {
        try {
            val dialog = ReceiverDialogFragment()
            dialog.show(supportFragmentManager, "ReceiverDialog")
            AppLog.d("显示接收端对话框")
        } catch (e: Exception) {
            AppLog.e("显示接收端对话框失败", e)
        }
    }

    /**
     * 显示遥控管理对话框
     */
    private fun showRemoteControlManagerDialog() {
        try {
            val dialog = RemoteControlManagerDialog()
            dialog.show(supportFragmentManager, "RemoteControlManagerDialog")
            AppLog.d("显示遥控管理对话框")
        } catch (e: Exception) {
            AppLog.e("显示遥控管理对话框失败", e)
        }
    }

    /**
     * 处理保存布局操作
     */
    private fun handleSaveLayout() {
        try {
            // 获取当前窗口信息列表
            val windowInfoList = windowSettingsManager.getCurrentWindowInfoList()

            if (windowInfoList.isEmpty()) {
                ToastUtils.showToast(this, "当前没有投屏窗口可以保存")
                return
            }

            AppLog.d("准备保存布局，当前窗口数: ${windowInfoList.size}")

            // 显示保存布局对话框
            layoutManager.showSaveLayoutDialog(this, windowInfoList)

        } catch (e: Exception) {
            AppLog.e("处理保存布局失败", e)
            ToastUtils.showToast(this, "保存布局失败")
        }
    }

    /**
     * 处理显示导播台操作
     */
    private fun handleShowDirector() {
        try {
            AppLog.d("显示导播台")

            // 显示导播台对话框
            layoutManager.showDirectorDialog(this, windowSettingsManager) { layoutItems ->
                // 恢复布局回调
                handleRestoreLayout(layoutItems)
            }

        } catch (e: Exception) {
            AppLog.e("显示导播台失败", e)
            ToastUtils.showToast(this, "显示导播台失败")
        }
    }

    /**
     * 处理恢复布局操作
     */
    private fun handleRestoreLayout(layoutItems: List<com.example.castapp.database.entity.WindowLayoutItemEntity>) {
        try {
            AppLog.d("开始恢复布局，包含${layoutItems.size}个窗口参数")

            if (layoutItems.isEmpty()) {
                ToastUtils.showToast(this, "布局中没有窗口信息")
                return
            }

            // 📝 检查是否有可创建的窗口（文本窗口或媒体窗口）
            val currentWindowCount = windowSettingsManager.getCurrentWindowInfoList().size
            val hasTextWindows = layoutItems.any { it.deviceId.startsWith("text_") }
            val hasMediaWindows = layoutItems.any {
                it.deviceId.startsWith("video_") || it.deviceId.startsWith("image_")
            }

            if (currentWindowCount == 0 && !hasTextWindows && !hasMediaWindows) {
                ToastUtils.showToast(this, "当前没有投屏窗口，且布局中没有可创建的窗口")
                return
            }

            if (currentWindowCount == 0 && (hasTextWindows || hasMediaWindows)) {
                val windowTypes = mutableListOf<String>()
                if (hasTextWindows) windowTypes.add("文本窗口")
                if (hasMediaWindows) windowTypes.add("媒体窗口")
                AppLog.d("当前没有现有窗口，但布局包含${windowTypes.joinToString("和")}，将自动创建")
            }

            // 委托给CastWindowManager处理布局恢复（匹配现有窗口并应用参数）
            windowSettingsManager.applyLayoutItemsToExistingWindows(layoutItems)

            // 通知精准控制面板更新
            if (::precisionControlPanelManager.isInitialized) {
                // 延迟更新，确保参数应用完成
                surfaceContainer.post {
                    val windowInfoList = windowSettingsManager.getCurrentWindowInfoList()
                    precisionControlPanelManager.updatePanels(windowInfoList)
                }
            }

            ToastUtils.showToast(this, "布局参数恢复完成")
            AppLog.d("布局恢复完成 - 通过设备ID匹配现有窗口并应用保存的参数")

        } catch (e: Exception) {
            AppLog.e("恢复布局失败", e)
            ToastUtils.showToast(this, "恢复布局失败: ${e.message}")
        }
    }

    /**
     * 显示添加媒体对话框
     */
    private fun showAddMediaDialog() {
        try {
            AppLog.d("显示添加媒体对话框")

            val dialogFragment = AddMediaDialogFragment()
            dialogFragment.show(supportFragmentManager, "AddMediaDialog")

        } catch (e: Exception) {
            AppLog.e("显示添加媒体对话框失败", e)
            ToastUtils.showToast(this, "显示添加媒体对话框失败")
        }
    }

    /**
     * 获取权限管理器实例
     * 供其他组件使用
     */
    fun getActivityPermissionHelper(): PermissionManager.ActivityPermissionHelper {
        return activityPermissionHelper
    }

    /**
     * 初始化精准控制面板管理器
     */
    private fun initializePrecisionControlPanelManager() {
        precisionControlPanelManager = PrecisionControlPanelManager(this, mainContainer)

        // 设置面板事件监听器
        precisionControlPanelManager.setPanelEventListener(object : PrecisionControlPanelManager.PanelEventListener {
            override fun onTransformChanged(connectionId: String, x: Float, y: Float, scale: Float, rotation: Float) {
                windowSettingsManager.applyPrecisionTransform(connectionId, x, y, scale, rotation)
                AppLog.d("【MainActivity】委托变换操作: $connectionId")
            }

            override fun onResetTransform(connectionId: String) {
                windowSettingsManager.resetWindowTransform(connectionId)
                AppLog.d("【MainActivity】委托重置操作: $connectionId")
            }

            override fun onPanelClosed(connectionId: String) {
                windowSettingsManager.toggleControlMode(connectionId, false)
                AppLog.d("【MainActivity】委托关闭调控: $connectionId")
            }
        })

        // 设置CastWindowManager回调
        windowSettingsManager.setPrecisionControlUpdateCallback {
            updatePrecisionControlPanels()
        }

        windowSettingsManager.setTransformValueChangeCallback { connectionId, x, y, scale, rotation ->
            precisionControlPanelManager.updateTransformValues(connectionId, x, y, scale, rotation)
        }

        AppLog.d("【MainActivity】精准控制面板管理器初始化完成")
    }

    /**
     * 更新精准控制面板（委托给管理器）
     */
    private fun updatePrecisionControlPanels() {
        val windowInfoList = windowSettingsManager.getCurrentWindowInfoList()
        precisionControlPanelManager.updatePanels(windowInfoList)
        AppLog.d("【MainActivity】委托更新面板，当前面板数: ${precisionControlPanelManager.getActivePanelCount()}")
    }

    /**
     * 初始化清屏功能管理器
     */
    private fun initializeHideShowManager() {
        hideShowManager.initialize(
            context = this,
            mainContainer = mainContainer
        ) {
            // 恢复按钮组显示的回调
            buttonGroup.isVisible = true
            AppLog.d("【MainActivity】按钮组已通过手势恢复显示")
        }
        AppLog.d("【MainActivity】清屏功能管理器初始化完成")
    }

    /**
     * 🔥 关键修复：监听SenderViewModel的权限请求
     * 确保即使发送端设置窗口关闭，也能处理远程控制触发的权限请求
     */
    private fun observeSenderViewModelPermissionRequests() {
        senderViewModel.requestMediaProjection.observe(this) { connectionAndType ->
            connectionAndType?.let { (connection, featureType) ->
                AppLog.d("【MainActivity】收到权限请求: ${connection.getDisplayText()}, 功能: $featureType")
                requestMediaProjectionPermissionForSender(connection, featureType)
            }
        }

        // 监听Toast消息
        senderViewModel.toastMessage.observe(this) { message ->
            if (!message.isNullOrEmpty()) {
                ToastUtils.showToast(this, message)
            }
        }

        AppLog.d("【MainActivity】已设置SenderViewModel权限请求监听")
    }

    /**
     * 🔥 关键修复：为SenderViewModel处理MediaProjection权限请求
     * 这是全局权限处理入口，不依赖于任何Fragment的生命周期
     */
    private fun requestMediaProjectionPermissionForSender(connection: Connection, featureType: String) {
        AppLog.d("【MainActivity】开始处理权限请求: ${connection.getDisplayText()}, 功能: $featureType")

        activityPermissionHelper.requestMediaProjection(object : PermissionManager.UnifiedMediaProjectionCallback {
            override fun onMediaProjectionGranted(resultCode: Int, data: Intent) {
                AppLog.d("【MainActivity】MediaProjection权限已授予，功能: $featureType")
                senderViewModel.handleUnifiedMediaProjectionResult(connection, featureType, true, resultCode, data)
            }

            override fun onMediaProjectionDenied() {
                AppLog.w("【MainActivity】MediaProjection权限被拒绝，功能: $featureType")
                senderViewModel.handleUnifiedMediaProjectionResult(connection, featureType, false)
            }

            override fun onPermissionAlreadyGranted(resultCode: Int, data: Intent) {
                AppLog.d("【MainActivity】MediaProjection权限已存在，直接使用，功能: $featureType")
                senderViewModel.handleUnifiedMediaProjectionResult(connection, featureType, true, resultCode, data)
            }
        })
    }

    /**
     * 🔥 关键修复：恢复远程被控服务器状态
     * 在应用启动时检查并恢复远程被控服务器的状态，确保不依赖于UI窗口
     */
    private fun restoreRemoteControlServerState() {
        try {
            val sharedPrefs = getSharedPreferences("cast_settings", MODE_PRIVATE)
            val isRemoteControlEnabled = sharedPrefs.getBoolean("remote_control_enabled", false)

            AppLog.d("【MainActivity】检查远程被控服务器状态: enabled=$isRemoteControlEnabled")

            if (isRemoteControlEnabled) {
                val remoteSenderServer = com.example.castapp.remote.RemoteSenderServer.getInstance()

                // 检查服务器是否已经在运行
                if (!remoteSenderServer.isServerRunning()) {
                    AppLog.d("【MainActivity】远程被控开关已开启，自动启动服务器")

                    if (remoteSenderServer.startServer()) {
                        AppLog.d("【MainActivity】远程被控服务器自动启动成功")
                        ToastUtils.showToast(this, "远程被控服务器已自动启动")
                    } else {
                        AppLog.w("【MainActivity】远程被控服务器自动启动失败")
                        // 启动失败时，重置开关状态
                        sharedPrefs.edit().putBoolean("remote_control_enabled", false).apply()
                        ToastUtils.showToast(this, "远程被控服务器启动失败，已重置状态")
                    }
                } else {
                    AppLog.d("【MainActivity】远程被控服务器已在运行")
                }
            } else {
                AppLog.d("【MainActivity】远程被控功能未开启，跳过服务器启动")
            }
        } catch (e: Exception) {
            AppLog.e("【MainActivity】恢复远程被控服务器状态失败", e)
        }
    }

    /**
     * 恢复固定端口7777 WebSocket服务器状态
     * 在应用启动时检查用户之前是否开启了远程被控开关，如果是则自动启动7777端口服务器
     */
    private fun restoreFixedWebSocketServerState() {
        try {
            val sharedPrefs = getSharedPreferences("receiver_settings", MODE_PRIVATE)
            val isFixedWebSocketEnabled = sharedPrefs.getBoolean("fixed_websocket_enabled", false)

            AppLog.d("【MainActivity】检查固定端口WebSocket服务器状态: $isFixedWebSocketEnabled")

            if (isFixedWebSocketEnabled) {
                // 检查服务器是否已经在运行
                val isRunning = com.example.castapp.service.RemoteReceiverService.isServiceRunning()

                if (!isRunning) {
                    AppLog.d("【MainActivity】远程控制开关已开启，自动启动远程控制服务器")

                    // 启动远程控制服务器
                    val intent = Intent(this, com.example.castapp.service.RemoteReceiverService::class.java).apply {
                        action = com.example.castapp.service.RemoteReceiverService.ACTION_START_REMOTE_CONTROL
                    }

                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                        startForegroundService(intent)
                    } else {
                        startService(intent)
                    }

                    AppLog.d("【MainActivity】远程控制服务器自动启动成功")
                } else {
                    AppLog.d("【MainActivity】远程控制服务器已在运行")
                }
            } else {
                AppLog.d("【MainActivity】远程控制功能未开启，跳过服务器启动")
            }
        } catch (e: Exception) {
            AppLog.e("【MainActivity】恢复固定端口WebSocket服务器状态失败", e)
        }
    }

    /**
     * 🎯 在应用启动时自动恢复之前应用的布局
     * 解决重启APP后导播台应用布局时文本窗口参数没有自动恢复的问题
     */
    private fun restoreAppliedLayoutOnStartup() {
        try {
            AppLog.d("【MainActivity】检查是否有需要恢复的应用布局")

            // 延迟执行，确保所有管理器都已初始化完成
            Handler(Looper.getMainLooper()).postDelayed({
                layoutManager.getCurrentAppliedLayout { appliedLayout ->
                    if (appliedLayout != null) {
                        AppLog.d("【MainActivity】发现已应用的布局，开始自动恢复: ${appliedLayout.layoutName}")

                        // 获取布局详情并自动应用
                        layoutManager.getLayoutDetails(appliedLayout.id) { success, items, message ->
                            if (success && items != null && items.isNotEmpty()) {
                                AppLog.d("【MainActivity】自动恢复布局: ${appliedLayout.layoutName}, 包含${items.size}个窗口")

                                // 自动应用布局（不显示Toast，静默恢复）
                                handleRestoreLayoutSilently(items)

                            } else {
                                AppLog.w("【MainActivity】获取应用布局详情失败: $message")
                            }
                        }
                    } else {
                        AppLog.d("【MainActivity】没有发现已应用的布局，跳过自动恢复")
                    }
                }
            }, 1000) // 延迟1秒，确保所有初始化完成

        } catch (e: Exception) {
            AppLog.e("【MainActivity】自动恢复应用布局失败", e)
        }
    }

    /**
     * 🎯 静默恢复布局操作（不显示Toast提示）
     * 用于APP启动时的自动布局恢复
     */
    private fun handleRestoreLayoutSilently(layoutItems: List<com.example.castapp.database.entity.WindowLayoutItemEntity>) {
        try {
            AppLog.d("【MainActivity】开始静默恢复布局，包含${layoutItems.size}个窗口参数")

            if (layoutItems.isEmpty()) {
                AppLog.w("【MainActivity】布局中没有窗口信息，跳过恢复")
                return
            }

            // 📝 检查是否有可创建的窗口（文本窗口或媒体窗口）
            val currentWindowCount = windowSettingsManager.getCurrentWindowInfoList().size
            val hasTextWindows = layoutItems.any { it.deviceId.startsWith("text_") }
            val hasMediaWindows = layoutItems.any {
                it.deviceId.startsWith("video_") || it.deviceId.startsWith("image_")
            }

            if (currentWindowCount == 0 && !hasTextWindows && !hasMediaWindows) {
                AppLog.d("【MainActivity】当前没有投屏窗口，且布局中没有可创建的窗口，跳过恢复")
                return
            }

            if (currentWindowCount == 0 && (hasTextWindows || hasMediaWindows)) {
                val windowTypes = mutableListOf<String>()
                if (hasTextWindows) windowTypes.add("文本窗口")
                if (hasMediaWindows) windowTypes.add("媒体窗口")
                AppLog.d("【MainActivity】当前没有现有窗口，但布局包含${windowTypes.joinToString("和")}，将自动创建")
            }

            // 委托给CastWindowManager处理布局恢复（匹配现有窗口并应用参数）
            windowSettingsManager.applyLayoutItemsToExistingWindows(layoutItems)

            // 通知精准控制面板更新
            if (::precisionControlPanelManager.isInitialized) {
                // 延迟更新，确保参数应用完成
                surfaceContainer.post {
                    val windowInfoList = windowSettingsManager.getCurrentWindowInfoList()
                    precisionControlPanelManager.updatePanels(windowInfoList)
                }
            }

            AppLog.d("【MainActivity】静默布局恢复完成 - 通过设备ID匹配现有窗口并应用保存的参数")

        } catch (e: Exception) {
            AppLog.e("【MainActivity】静默恢复布局失败", e)
        }
    }

}