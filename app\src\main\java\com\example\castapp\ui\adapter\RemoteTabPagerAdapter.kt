package com.example.castapp.ui.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.example.castapp.ui.fragment.RemoteReceiverTabFragment
import com.example.castapp.ui.fragment.RemoteSenderTabFragment

/**
 * 遥控管理标签页适配器
 * 管理发送端和接收端两个标签页
 */
class RemoteTabPagerAdapter(
    fragmentManager: FragmentManager,
    lifecycle: Lifecycle
) : FragmentStateAdapter(fragmentManager, lifecycle) {

    // 使用懒加载方式创建Fragment实例，避免ViewPager2的状态混乱问题
    private var senderFragment: RemoteSenderTabFragment? = null
    private var receiverFragment: RemoteReceiverTabFragment? = null

    override fun getItemCount(): Int = 2

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> {
                if (senderFragment == null) {
                    senderFragment = RemoteSenderTabFragment.newInstance()
                }
                senderFragment!!
            }
            1 -> {
                if (receiverFragment == null) {
                    receiverFragment = RemoteReceiverTabFragment.newInstance()
                }
                receiverFragment!!
            }
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }

    /**
     * 获取发送端Fragment
     * 确保Fragment已经创建
     */
    fun getSenderFragment(): RemoteSenderTabFragment {
        if (senderFragment == null) {
            senderFragment = RemoteSenderTabFragment.newInstance()
        }
        return senderFragment!!
    }

    /**
     * 获取接收端Fragment
     * 确保Fragment已经创建
     */
    fun getReceiverFragment(): RemoteReceiverTabFragment {
        if (receiverFragment == null) {
            receiverFragment = RemoteReceiverTabFragment.newInstance()
        }
        return receiverFragment!!
    }

    /**
     * 获取标签页标题
     */
    fun getTabTitle(position: Int): String {
        return when (position) {
            0 -> "发送端"
            1 -> "接收端"
            else -> ""
        }
    }
}
