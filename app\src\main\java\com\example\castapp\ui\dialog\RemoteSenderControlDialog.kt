package com.example.castapp.ui.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.SeekBar
import androidx.appcompat.widget.SwitchCompat
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.model.Connection
import com.example.castapp.model.RemoteSenderConnection
import com.example.castapp.remote.RemoteSenderWebSocketClient
import com.example.castapp.ui.adapter.RemoteSenderAdapter
import com.example.castapp.utils.AppLog
import com.example.castapp.websocket.ControlMessage
import kotlin.collections.get

/**
 * 远程发送端控制对话框
 * 复制SenderDialogFragment的功能，用于远程控制
 */
class RemoteSenderControlDialog(
    private val remoteSenderConnection: RemoteSenderConnection,
    private val remoteClient: RemoteSenderWebSocketClient
) : DialogFragment() {

    private lateinit var dialogTitle: TextView
    private lateinit var closeButton: ImageButton
    private lateinit var bitrateSeekBar: SeekBar
    private lateinit var bitrateValueText: TextView
    private lateinit var resolutionSeekBar: SeekBar
    private lateinit var resolutionValueText: TextView
    private lateinit var resolutionInfoText: TextView
    private lateinit var mediaAudioVolumeLayout: LinearLayout
    private lateinit var mediaAudioVolumeSeekBar: SeekBar
    private lateinit var mediaAudioVolumeText: TextView
    private lateinit var micAudioVolumeLayout: LinearLayout
    private lateinit var micAudioVolumeSeekBar: SeekBar
    private lateinit var micAudioVolumeText: TextView
    private lateinit var remoteControlLayout: LinearLayout
    private lateinit var remoteControlSwitch: SwitchCompat
    private lateinit var remoteControlStatusText: TextView
    private lateinit var connectionsRecyclerView: RecyclerView
    private lateinit var connectionCountText: TextView
    private lateinit var addConnectionButton: ImageButton

    // 连接列表适配器
    private lateinit var connectionListAdapter: RemoteSenderAdapter

    // 对话框关闭监听器
    private var onDismissListener: (() -> Unit)? = null

    /**
     * 设置对话框关闭监听器
     */
    fun setOnDismissListener(listener: () -> Unit) {
        onDismissListener = listener
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.DialogTheme)
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDismissListener?.invoke()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_remote_sender_control, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setupRecyclerView()
        setupControls()
        setupClickListeners()
        requestSettingsSync()
    }

    private fun initViews(view: View) {
        dialogTitle = view.findViewById(R.id.dialog_title)
        closeButton = view.findViewById(R.id.close_dialog_button)
        bitrateSeekBar = view.findViewById(R.id.bitrate_seekbar)
        bitrateValueText = view.findViewById(R.id.bitrate_value_text)
        resolutionSeekBar = view.findViewById(R.id.resolution_seekbar)
        resolutionValueText = view.findViewById(R.id.resolution_value_text)
        resolutionInfoText = view.findViewById(R.id.resolution_info_text)
        mediaAudioVolumeLayout = view.findViewById(R.id.media_audio_volume_layout)
        mediaAudioVolumeSeekBar = view.findViewById(R.id.media_audio_volume_seekbar)
        mediaAudioVolumeText = view.findViewById(R.id.media_audio_volume_text)
        micAudioVolumeLayout = view.findViewById(R.id.mic_audio_volume_layout)
        micAudioVolumeSeekBar = view.findViewById(R.id.mic_audio_volume_seekbar)
        micAudioVolumeText = view.findViewById(R.id.mic_audio_volume_text)
        remoteControlLayout = view.findViewById(R.id.remote_control_layout)
        remoteControlSwitch = view.findViewById(R.id.remote_control_switch)
        remoteControlStatusText = view.findViewById(R.id.remote_control_status_text)
        connectionsRecyclerView = view.findViewById(R.id.connections_container)
        connectionCountText = view.findViewById(R.id.connection_count_text)
        addConnectionButton = view.findViewById(R.id.add_connection_button)
        
        // 设置标题
        dialogTitle.text = "远程发送端设置 - ${remoteSenderConnection.deviceName}"
    }

    private fun setupRecyclerView() {
        // 显示连接列表，实现远程控制功能
        connectionsRecyclerView.visibility = View.VISIBLE

        connectionListAdapter = RemoteSenderAdapter(
            connections = mutableListOf(),
            onCastToggle = { connection, enabled ->
                handleRemoteCastToggle(connection, enabled)
            },
            onMediaAudioToggle = { connection, enabled ->
                handleRemoteMediaAudioToggle(connection, enabled)
            },
            onMicAudioToggle = { connection, enabled ->
                handleRemoteMicAudioToggle(connection, enabled)
            },
            onEditConnection = { connection ->
                showEditConnectionDialog(connection)
            },
            onDeleteConnection = { connection ->
                showDeleteConnectionConfirmDialog(connection)
            }
        )

        connectionsRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = connectionListAdapter
        }

        connectionCountText.text = "0个连接"

        // 设置添加连接按钮点击监听器
        addConnectionButton.setOnClickListener {
            showAddConnectionDialog()
        }
    }

    private fun setupControls() {
        // 设置码率控制
        bitrateSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val bitrateMbps = progress + 5
                    updateBitrateDisplay(bitrateMbps)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val bitrateMbps = (seekBar?.progress ?: 15) + 5
                sendBitrateChange(bitrateMbps)
            }
        })

        // 设置分辨率控制
        resolutionSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val scalePercent = progress + 1
                    updateResolutionDisplay(scalePercent)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val scalePercent = (seekBar?.progress ?: 99) + 1
                sendResolutionChange(scalePercent)
            }
        })

        // 设置音量控制
        setupVolumeControl(mediaAudioVolumeSeekBar, mediaAudioVolumeText, "media")
        setupVolumeControl(micAudioVolumeSeekBar, micAudioVolumeText, "mic")
    }

    private fun setupVolumeControl(seekBar: SeekBar, textView: TextView, volumeType: String) {
        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    textView.text = "$progress%"
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val volume = seekBar?.progress ?: 80
                sendVolumeChange(volumeType, volume)
            }
        })
    }

    private fun setupClickListeners() {
        closeButton.setOnClickListener {
            dismiss()
        }
    }

    private fun updateBitrateDisplay(bitrateMbps: Int) {
        bitrateValueText.text = "$bitrateMbps Mbps"
    }

    private fun updateResolutionDisplay(scalePercent: Int) {
        resolutionValueText.text = "$scalePercent%"
    }

    private fun sendBitrateChange(bitrateMbps: Int) {
        if (remoteClient.sendBitrateChange(bitrateMbps)) {
            AppLog.d("发送码率变更: ${bitrateMbps}Mbps")
        } else {
            Toast.makeText(requireContext(), "发送码率变更失败", Toast.LENGTH_SHORT).show()
        }
    }

    private fun sendResolutionChange(scalePercent: Int) {
        if (remoteClient.sendResolutionChange(scalePercent)) {
            AppLog.d("发送分辨率变更: ${scalePercent}%")
        } else {
            Toast.makeText(requireContext(), "发送分辨率变更失败", Toast.LENGTH_SHORT).show()
        }
    }

    private fun sendVolumeChange(volumeType: String, volume: Int) {
        if (remoteClient.sendVolumeChange(volumeType, volume)) {
            AppLog.d("发送音量变更: $volumeType = $volume%")
        } else {
            Toast.makeText(requireContext(), "发送音量变更失败", Toast.LENGTH_SHORT).show()
        }
    }

    // 连接控制方法暂时简化，专注于基础控制功能

    private fun requestSettingsSync() {
        if (remoteClient.requestSettingsSync()) {
            AppLog.d("请求设置同步")
        } else {
            Toast.makeText(requireContext(), "请求设置同步失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 处理远程设置同步消息
     */
    fun handleSettingsSync(message: ControlMessage) {
        activity?.runOnUiThread {
            // 更新UI控件的值
            message.getIntData("bitrate_mbps")?.let { bitrate ->
                bitrateSeekBar.progress = bitrate - 5
                updateBitrateDisplay(bitrate)
            }
            
            message.getIntData("resolution_scale")?.let { scale ->
                resolutionSeekBar.progress = scale - 1
                updateResolutionDisplay(scale)
            }
            
            message.getIntData("media_volume")?.let { volume ->
                mediaAudioVolumeSeekBar.progress = volume
                mediaAudioVolumeText.text = "$volume%"
            }
            
            message.getIntData("mic_volume")?.let { volume ->
                micAudioVolumeSeekBar.progress = volume
                micAudioVolumeText.text = "$volume%"
            }
            
            // 更新连接列表
            handleConnectionListSync(message)

            AppLog.d("设置同步完成")
        }
    }

    /**
     * 处理连接列表同步消息
     */
    private fun handleConnectionListSync(message: ControlMessage) {
        try {
            val connectionsData = message.data["connections"] as? List<*>
            if (connectionsData != null) {
                val connections = connectionsData.mapNotNull { connectionData ->
                    if (connectionData is Map<*, *>) {
                        try {
                            Connection(
                                connectionId = connectionData["connectionId"] as String,
                                ipAddress = connectionData["ipAddress"] as String,
                                port = when (val portValue = connectionData["port"]) {
                                    is Int -> portValue
                                    is Double -> portValue.toInt()
                                    is String -> portValue.toInt()
                                    else -> 0
                                },
                                isCasting = connectionData["isCasting"] as? Boolean ?: false,
                                isMediaAudioEnabled = connectionData["isMediaAudioEnabled"] as? Boolean ?: false,
                                isMicAudioEnabled = connectionData["isMicAudioEnabled"] as? Boolean ?: false,
                                isConnected = connectionData["isConnected"] as? Boolean ?: false,
                                webSocketConnected = connectionData["webSocketConnected"] as? Boolean ?: false
                            )
                        } catch (e: Exception) {
                            AppLog.e("解析连接数据失败", e)
                            AppLog.e("连接数据内容: $connectionData")
                            null
                        }
                    } else null
                }

                // 更新适配器
                connectionListAdapter.updateConnections(connections)
                connectionCountText.text = "${connections.size}个连接"

                AppLog.d("连接列表同步完成: ${connections.size} 个连接")
            }
        } catch (e: Exception) {
            AppLog.e("处理连接列表同步失败", e)
        }
    }

    /**
     * 处理远程投屏切换
     */
    private fun handleRemoteCastToggle(connection: Connection, enabled: Boolean) {
        if (remoteClient.sendSpecificConnectionToggle(connection.connectionId, "video", enabled)) {
            AppLog.d("发送远程投屏切换: ${connection.getDisplayText()} -> $enabled")
        } else {
            Toast.makeText(requireContext(), "发送投屏切换失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 处理远程媒体音频切换
     */
    private fun handleRemoteMediaAudioToggle(connection: Connection, enabled: Boolean) {
        if (remoteClient.sendSpecificConnectionToggle(connection.connectionId, "media_audio", enabled)) {
            AppLog.d("发送远程媒体音频切换: ${connection.getDisplayText()} -> $enabled")
        } else {
            Toast.makeText(requireContext(), "发送媒体音频切换失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 处理远程麦克风音频切换
     */
    private fun handleRemoteMicAudioToggle(connection: Connection, enabled: Boolean) {
        if (remoteClient.sendSpecificConnectionToggle(connection.connectionId, "mic_audio", enabled)) {
            AppLog.d("发送远程麦克风音频切换: ${connection.getDisplayText()} -> $enabled")
        } else {
            Toast.makeText(requireContext(), "发送麦克风音频切换失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 处理连接添加事件
     */
    fun handleConnectionAdded(message: ControlMessage) {
        activity?.runOnUiThread {
            try {
                val connectionData = message.data["connection"] as? Map<*, *>
                if (connectionData != null) {
                    val newConnection = parseConnectionData(connectionData)
                    if (newConnection != null) {
                        connectionListAdapter.addConnection(newConnection)
                        updateConnectionCount()
                        AppLog.d("远程控制端：连接已添加 ${newConnection.getDisplayText()}")
                    }
                }
            } catch (e: Exception) {
                AppLog.e("处理连接添加事件失败", e)
            }
        }
    }

    /**
     * 处理连接更新事件
     */
    fun handleConnectionUpdated(message: ControlMessage) {
        activity?.runOnUiThread {
            try {
                val connectionData = message.data["connection"] as? Map<*, *>
                if (connectionData != null) {
                    val updatedConnection = parseConnectionData(connectionData)
                    if (updatedConnection != null) {
                        connectionListAdapter.updateConnection(updatedConnection)
                        AppLog.d("远程控制端：连接已更新 ${updatedConnection.getDisplayText()}")
                    }
                }
            } catch (e: Exception) {
                AppLog.e("处理连接更新事件失败", e)
            }
        }
    }

    /**
     * 处理连接删除事件
     */
    fun handleConnectionRemoved(message: ControlMessage) {
        activity?.runOnUiThread {
            try {
                val removedConnectionId = message.getStringData("removed_connection_id")
                if (removedConnectionId != null) {
                    connectionListAdapter.removeConnection(removedConnectionId)
                    updateConnectionCount()
                    AppLog.d("远程控制端：连接已删除 $removedConnectionId")
                }
            } catch (e: Exception) {
                AppLog.e("处理连接删除事件失败", e)
            }
        }
    }

    /**
     * 解析连接数据
     */
    private fun parseConnectionData(connectionData: Map<*, *>): Connection? {
        return try {
            Connection(
                connectionId = connectionData["connectionId"] as String,
                ipAddress = connectionData["ipAddress"] as String,
                port = when (val portValue = connectionData["port"]) {
                    is Int -> portValue
                    is Double -> portValue.toInt()
                    is String -> portValue.toInt()
                    else -> 0
                },
                isCasting = connectionData["isCasting"] as? Boolean ?: false,
                isMediaAudioEnabled = connectionData["isMediaAudioEnabled"] as? Boolean ?: false,
                isMicAudioEnabled = connectionData["isMicAudioEnabled"] as? Boolean ?: false,
                isConnected = connectionData["isConnected"] as? Boolean ?: false,
                webSocketConnected = connectionData["webSocketConnected"] as? Boolean ?: false
            )
        } catch (e: Exception) {
            AppLog.e("解析连接数据失败", e)
            null
        }
    }

    /**
     * 更新连接数量显示
     */
    private fun updateConnectionCount() {
        connectionCountText.text = "${connectionListAdapter.itemCount}个连接"
    }

    /**
     * 显示添加连接对话框
     */
    private fun showAddConnectionDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_add_receiver, null)
        val titleText = dialogView.findViewById<TextView>(R.id.dialog_title)
        val ipEditText = dialogView.findViewById<EditText>(R.id.ip_address_input)
        val portEditText = dialogView.findViewById<EditText>(R.id.port_input)

        // 设置标题和默认端口
        titleText.text = "添加接收端"
        portEditText.setText("8888")

        AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setPositiveButton("添加") { dialog, which ->
                val ipAddress = ipEditText.text.toString().trim()
                val portText = portEditText.text.toString().trim()

                if (ipAddress.isEmpty()) {
                    Toast.makeText(requireContext(), "请输入IP地址", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                val port = try {
                    portText.toInt()
                } catch (_: NumberFormatException) {
                    Toast.makeText(requireContext(), "端口格式错误", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                if (port in 1..65535) {
                    sendAddConnectionRequest(ipAddress, port)
                } else {
                    Toast.makeText(requireContext(), "端口范围应在1-65535之间", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示编辑连接对话框
     */
    private fun showEditConnectionDialog(connection: Connection) {
        val dialogView = layoutInflater.inflate(R.layout.dialog_add_receiver, null)
        val titleText = dialogView.findViewById<TextView>(R.id.dialog_title)
        val ipEditText = dialogView.findViewById<EditText>(R.id.ip_address_input)
        val portEditText = dialogView.findViewById<EditText>(R.id.port_input)

        // 设置标题和填入当前连接信息
        titleText.text = "编辑连接"
        ipEditText.setText(connection.ipAddress)
        portEditText.setText(connection.port.toString())

        AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setPositiveButton("保存") { dialog, which ->
                val newIpAddress = ipEditText.text.toString().trim()
                val newPortText = portEditText.text.toString().trim()

                if (newIpAddress.isEmpty()) {
                    Toast.makeText(requireContext(), "请输入IP地址", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                val newPort = try {
                    newPortText.toInt()
                } catch (_: NumberFormatException) {
                    Toast.makeText(requireContext(), "端口格式错误", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                if (newPort in 1..65535) {
                    sendEditConnectionRequest(connection.connectionId, newIpAddress, newPort)
                } else {
                    Toast.makeText(requireContext(), "端口范围应在1-65535之间", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示删除连接确认对话框
     */
    private fun showDeleteConnectionConfirmDialog(connection: Connection) {
        AlertDialog.Builder(requireContext())
            .setTitle("删除连接")
            .setMessage("确定要删除连接 ${connection.getDisplayText()} 吗？")
            .setPositiveButton("删除") { _, _ ->
                sendDeleteConnectionRequest(connection.connectionId)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 发送添加连接请求
     */
    private fun sendAddConnectionRequest(ipAddress: String, port: Int) {
        if (remoteClient.sendAddConnectionRequest(ipAddress, port)) {
            AppLog.d("发送远程添加连接请求: $ipAddress:$port")
            Toast.makeText(requireContext(), "添加连接请求已发送", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(requireContext(), "发送添加连接请求失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 发送编辑连接请求
     */
    private fun sendEditConnectionRequest(connectionId: String, newIpAddress: String, newPort: Int) {
        if (remoteClient.sendEditConnectionRequest(connectionId, newIpAddress, newPort)) {
            AppLog.d("发送远程编辑连接请求: $connectionId -> $newIpAddress:$newPort")
            Toast.makeText(requireContext(), "编辑连接请求已发送", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(requireContext(), "发送编辑连接请求失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 发送删除连接请求
     */
    private fun sendDeleteConnectionRequest(connectionId: String) {
        if (remoteClient.sendDeleteConnectionRequest(connectionId)) {
            AppLog.d("发送远程删除连接请求: $connectionId")
            Toast.makeText(requireContext(), "删除连接请求已发送", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(requireContext(), "发送删除连接请求失败", Toast.LENGTH_SHORT).show()
        }
    }
}
