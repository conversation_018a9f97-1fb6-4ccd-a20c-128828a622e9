package com.example.castapp.manager.windowsettings

import androidx.fragment.app.FragmentActivity
import com.example.castapp.ui.dialog.WindowManagerDialog
import com.example.castapp.ui.dialog.LayerManagerDialog
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.utils.AppLog

/**
 * 窗口对话框模块
 * 负责管理窗口管理对话框的显示和刷新
 */
class WindowDialogModule(
    private val dataModule: WindowDataModule,
    private val infoModule: WindowInfoModule
) {

    // 窗口层级调整回调
    private var windowLayerAdjustCallback: ((List<CastWindowInfo>) -> Unit)? = null

    // 🔄 层级管理对话框引用（用于刷新）
    private var currentLayerManagerDialog: LayerManagerDialog? = null
    
    // 窗口操作回调
    private var cropSwitchCallback: ((String, Boolean) -> Unit)? = null
    private var dragSwitchCallback: ((String, Boolean) -> Unit)? = null
    private var scaleSwitchCallback: ((String, Boolean) -> Unit)? = null
    private var rotationSwitchCallback: ((String, Boolean) -> Unit)? = null
    private var visibilitySwitchCallback: ((String, Boolean) -> Unit)? = null
    private var mirrorSwitchCallback: ((String, Boolean) -> Unit)? = null
    private var cornerRadiusCallback: ((String, Float) -> Unit)? = null
    private var alphaCallback: ((String, Float) -> Unit)? = null
    private var controlSwitchCallback: ((String, Boolean) -> Unit)? = null
    private var editSwitchCallback: ((String, Boolean) -> Unit)? = null // 📝 编辑开关回调

    private var borderSwitchCallback: ((String, Boolean) -> Unit)? = null
    private var borderColorCallback: ((String, Int) -> Unit)? = null
    private var borderWidthCallback: ((String, Float) -> Unit)? = null
    private var noteCallback: ((String, String) -> Unit)? = null // 🏷️ 备注变更回调
    private var windowDeleteCallback: ((String) -> Unit)? = null // 🗑️ 窗口删除回调

    // 🎬 视频控制回调
    private var videoPlaySwitchCallback: ((String, Boolean) -> Unit)? = null
    private var videoLoopCountCallback: ((String, Int) -> Unit)? = null
    private var videoVolumeCallback: ((String, Int) -> Unit)? = null

    // 🎯 横屏模式控制回调
    private var landscapeSwitchCallback: ((String, Boolean) -> Unit)? = null
    
    /**
     * 设置窗口层级调整回调
     */
    fun setWindowLayerAdjustCallback(callback: (List<CastWindowInfo>) -> Unit) {
        this.windowLayerAdjustCallback = callback
    }
    
    /**
     * 设置窗口操作回调
     */
    fun setWindowOperationCallbacks(
        onCropSwitchChanged: (String, Boolean) -> Unit,
        onDragSwitchChanged: (String, Boolean) -> Unit,
        onScaleSwitchChanged: (String, Boolean) -> Unit,
        onRotationSwitchChanged: (String, Boolean) -> Unit,
        onVisibilitySwitchChanged: (String, Boolean) -> Unit,
        onMirrorSwitchChanged: (String, Boolean) -> Unit,
        onCornerRadiusChanged: (String, Float) -> Unit,
        onAlphaChanged: (String, Float) -> Unit,
        onControlSwitchChanged: (String, Boolean) -> Unit,
        onEditSwitchChanged: (String, Boolean) -> Unit, // 📝 编辑开关回调

        onBorderSwitchChanged: (String, Boolean) -> Unit,
        onBorderColorChanged: (String, Int) -> Unit,
        onBorderWidthChanged: (String, Float) -> Unit,
        onNoteChanged: (String, String) -> Unit, // 🏷️ 备注变更回调
        onLandscapeSwitchChanged: (String, Boolean) -> Unit // 🎯 横屏模式控制回调
    ) {
        this.cropSwitchCallback = onCropSwitchChanged
        this.dragSwitchCallback = onDragSwitchChanged
        this.scaleSwitchCallback = onScaleSwitchChanged
        this.rotationSwitchCallback = onRotationSwitchChanged
        this.visibilitySwitchCallback = onVisibilitySwitchChanged
        this.mirrorSwitchCallback = onMirrorSwitchChanged
        this.cornerRadiusCallback = onCornerRadiusChanged
        this.alphaCallback = onAlphaChanged
        this.controlSwitchCallback = onControlSwitchChanged
        this.editSwitchCallback = onEditSwitchChanged // 📝 设置编辑开关回调

        this.borderSwitchCallback = onBorderSwitchChanged
        this.borderColorCallback = onBorderColorChanged
        this.borderWidthCallback = onBorderWidthChanged
        this.noteCallback = onNoteChanged // 🏷️ 设备备注变更回调
        this.landscapeSwitchCallback = onLandscapeSwitchChanged // 🎯 横屏模式控制回调
    }

    /**
     * 🗑️ 设置窗口删除回调
     */
    fun setWindowDeleteCallback(callback: (String) -> Unit) {
        this.windowDeleteCallback = callback
    }

    /**
     * 🎬 设置视频控制回调
     */
    fun setVideoControlCallbacks(
        onVideoPlaySwitchChanged: (String, Boolean) -> Unit,
        onVideoLoopCountChanged: (String, Int) -> Unit,
        onVideoVolumeChanged: (String, Int) -> Unit
    ) {
        this.videoPlaySwitchCallback = onVideoPlaySwitchChanged
        this.videoLoopCountCallback = onVideoLoopCountChanged
        this.videoVolumeCallback = onVideoVolumeChanged
    }
    
    /**
     * 显示窗口管理BottomSheet
     */
    fun showWindowManagerDialog() {
        val activity = dataModule.getCurrentActivity() ?: return

        try {
            AppLog.d("【窗口管理】显示窗口管理BottomSheet")

            val dialog = WindowManagerDialog(
                windowInfoProvider = { infoModule.getActiveWindowInfoList() },
                onCropSwitchChanged = { connectionId, isEnabled ->
                    cropSwitchCallback?.invoke(connectionId, isEnabled)
                },
                onDragSwitchChanged = { connectionId, isEnabled ->
                    dragSwitchCallback?.invoke(connectionId, isEnabled)
                },
                onScaleSwitchChanged = { connectionId, isEnabled ->
                    scaleSwitchCallback?.invoke(connectionId, isEnabled)
                },
                onRotationSwitchChanged = { connectionId, isEnabled ->
                    rotationSwitchCallback?.invoke(connectionId, isEnabled)
                },
                onVisibilitySwitchChanged = { connectionId, isVisible ->
                    visibilitySwitchCallback?.invoke(connectionId, isVisible)
                },
                onMirrorSwitchChanged = { connectionId, isEnabled ->
                    mirrorSwitchCallback?.invoke(connectionId, isEnabled)
                },
                onCornerRadiusChanged = { connectionId, cornerRadius ->
                    cornerRadiusCallback?.invoke(connectionId, cornerRadius)
                },
                onAlphaChanged = { connectionId, alpha ->
                    alphaCallback?.invoke(connectionId, alpha)
                },
                onControlSwitchChanged = { connectionId, isEnabled ->
                    controlSwitchCallback?.invoke(connectionId, isEnabled)
                },
                onEditSwitchChanged = { connectionId, isEnabled ->
                    // 📝 编辑开关变更处理
                    editSwitchCallback?.invoke(connectionId, isEnabled)
                },

                onBorderSwitchChanged = { connectionId, isEnabled ->
                    borderSwitchCallback?.invoke(connectionId, isEnabled)
                },
                onBorderColorChanged = { connectionId, color ->
                    borderColorCallback?.invoke(connectionId, color)
                },
                onBorderWidthChanged = { connectionId, width ->
                    borderWidthCallback?.invoke(connectionId, width)
                },
                onNoteChanged = { connectionId, note ->
                    noteCallback?.invoke(connectionId, note)
                },
                onWindowDeleted = { windowInfo ->
                    handleWindowDelete(windowInfo)
                },
                onVideoPlaySwitchChanged = { connectionId, isEnabled ->
                    videoPlaySwitchCallback?.invoke(connectionId, isEnabled)
                },
                onVideoLoopCountChanged = { connectionId, loopCount ->
                    videoLoopCountCallback?.invoke(connectionId, loopCount)
                },
                onVideoVolumeChanged = { connectionId, volume ->
                    videoVolumeCallback?.invoke(connectionId, volume)
                },
                onLandscapeSwitchChanged = { connectionId, isEnabled ->
                    landscapeSwitchCallback?.invoke(connectionId, isEnabled)
                }
            )

            // 设置关闭回调
            dialog.onDialogDismissed = {
                dataModule.setCurrentDialog(null)
                AppLog.d("【窗口管理】对话框已关闭，清理引用")
            }

            dataModule.setCurrentDialog(dialog)
            if (activity is FragmentActivity) {
                dialog.show(activity.supportFragmentManager, "WindowManagerBottomSheet")
            }

        } catch (e: Exception) {
            AppLog.e("【窗口管理】显示窗口管理BottomSheet失败", e)
        }
    }
    
    /**
     * 刷新窗口管理对话框（如果正在显示）
     */
    fun refreshWindowManagerDialogIfVisible() {
        dataModule.getCurrentDialog()?.let { dialog ->
            if (dialog.isVisible) {
                AppLog.d("【窗口管理】刷新窗口管理对话框")
                dialog.refreshWindowList()
            }
        }
    }

    /**
     * 公开的刷新窗口管理对话框方法
     * 用于外部触发刷新（如设备信息更新时）
     */
    fun refreshWindowManagerDialog() {
        dataModule.getCurrentDialog()?.let { dialog ->
            if (dialog.isVisible) {
                AppLog.d("【窗口管理】外部触发刷新窗口管理对话框")
                dialog.refreshWindowList()
            }
        }
    }

    /**
     * 🔄 刷新层级管理对话框（如果正在显示）
     */
    fun refreshLayerManagerDialog() {
        currentLayerManagerDialog?.let { dialog ->
            if (dialog.isVisible) {
                AppLog.d("【层级管理】刷新层级管理对话框")
                dialog.refreshDeviceList()
            }
        }
    }

    /**
     * 显示层级管理BottomSheet对话框
     */
    fun showLayerManagerDialog() {
        try {
            val activity = dataModule.getCurrentActivity() ?: return

            AppLog.d("【层级管理】显示层级管理BottomSheet")

            val dialog = LayerManagerDialog(
                windowInfoProvider = { infoModule.getActiveWindowInfoList() },
                onWindowOrderChanged = { newOrderList ->
                    windowLayerAdjustCallback?.invoke(newOrderList)
                },
                onNoteChanged = { connectionId, note ->
                    noteCallback?.invoke(connectionId, note)
                }
            )

            // 设置关闭回调
            dialog.onDialogDismissed = {
                currentLayerManagerDialog = null // 🔄 清理引用
                AppLog.d("【层级管理】对话框已关闭")
            }

            // 🔄 保存对话框引用
            currentLayerManagerDialog = dialog

            if (activity is FragmentActivity) {
                dialog.show(activity.supportFragmentManager, "LayerManagerBottomSheet")
            }

        } catch (e: Exception) {
            AppLog.e("【层级管理】显示层级管理BottomSheet失败", e)
        }
    }

    /**
     * 🗑️ 处理窗口删除 - 优化版本
     * 作为中间层，负责调用实际的删除逻辑，避免重复日志
     */
    private fun handleWindowDelete(windowInfo: CastWindowInfo) {
        try {
            AppLog.d("【窗口删除中间层】接收删除请求: ${windowInfo.connectionId}")

            // 🚀 优化：直接调用删除回调，避免重复的日志和逻辑
            windowDeleteCallback?.invoke(windowInfo.connectionId)

            // 刷新窗口管理对话框
            refreshWindowManagerDialogIfVisible()

        } catch (e: Exception) {
            AppLog.e("【窗口删除中间层】删除窗口失败: ${windowInfo.connectionId}", e)
        }
    }
}
