package com.example.castapp.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit

/**
 * 🏷️ 备注管理器
 * 负责备注信息的保存、读取和管理
 */
class NoteManager(context: Context) {

    companion object {
        private const val PREFS_NAME = "notes_prefs"
        private const val KEY_PREFIX = "note_"
        private const val DEFAULT_NOTE = "无"
    }

    private val sharedPrefs: SharedPreferences =
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    /**
     * 🏷️ 获取备注
     * @param connectionId 连接ID
     * @return 备注内容，如果没有设置则返回"无"
     */
    fun getNote(connectionId: String): String {
        return try {
            sharedPrefs.getString("$KEY_PREFIX$connectionId", DEFAULT_NOTE) ?: DEFAULT_NOTE
        } catch (e: Exception) {
            AppLog.e("🏷️ 获取备注失败: $connectionId", e)
            DEFAULT_NOTE
        }
    }

    /**
     * 🏷️ 保存备注
     * @param connectionId 连接ID
     * @param note 备注内容
     * @return 是否保存成功
     */
    fun saveNote(connectionId: String, note: String): Boolean {
        return try {
            val noteToSave = note.trim().ifEmpty { DEFAULT_NOTE }
            sharedPrefs.edit {
                putString("$KEY_PREFIX$connectionId", noteToSave)
            }

            AppLog.d("🏷️ 备注保存成功: $connectionId -> $noteToSave")
            true
        } catch (e: Exception) {
            AppLog.e("🏷️ 保存备注失败: $connectionId", e)
            false
        }
    }

    /**
     * 🏷️ 删除备注
     * @param connectionId 连接ID
     * @return 是否删除成功
     */
    fun deleteNote(connectionId: String): Boolean {
        return try {
            sharedPrefs.edit {
                remove("$KEY_PREFIX$connectionId")
            }

            AppLog.d("🏷️ 备注删除成功: $connectionId")
            true
        } catch (e: Exception) {
            AppLog.e("🏷️ 删除备注失败: $connectionId", e)
            false
        }
    }
}
