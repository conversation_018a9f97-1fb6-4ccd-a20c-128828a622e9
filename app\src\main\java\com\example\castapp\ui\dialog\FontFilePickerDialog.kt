package com.example.castapp.ui.dialog

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.ToastUtils
import java.io.File

/**
 * 字体文件选择对话框
 * 提供简单的文件浏览功能，专门用于选择.ttf字体文件
 */
class FontFilePickerDialog(
    private val context: Context,
    private val onFileSelected: (File) -> Unit
) {
    
    private var dialog: AlertDialog? = null
    private lateinit var tvCurrentPath: TextView
    private lateinit var tvFileCount: TextView
    private lateinit var rvFiles: RecyclerView
    private lateinit var btnClose: ImageView
    
    private var currentDirectory: File = getInitialDirectory()
    private val fileList = mutableListOf<FileItem>()
    private lateinit var fileAdapter: FileAdapter
    
    /**
     * 文件项数据类
     */
    data class FileItem(
        val file: File,
        val name: String,
        val isDirectory: Boolean,
        val isTtfFile: Boolean = false
    )
    
    /**
     * 显示对话框
     */
    fun show() {
        try {
            // 检查存储权限
            if (!hasStoragePermission()) {
                showPermissionDialog()
                return
            }

            val view = LayoutInflater.from(context).inflate(R.layout.dialog_font_file_picker, null)

            initViews(view)
            setupRecyclerView()
            loadCurrentDirectory()
            setupListeners()

            dialog = AlertDialog.Builder(context)
                .setView(view)
                .setCancelable(true)
                .create()

            dialog?.show()

            AppLog.d("【字体文件选择器】对话框已显示")

        } catch (e: Exception) {
            AppLog.e("【字体文件选择器】显示对话框失败", e)
            ToastUtils.showToast(context, "显示文件选择器失败")
        }
    }

    /**
     * 检查存储权限
     */
    private fun hasStoragePermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            Environment.isExternalStorageManager()
        } else {
            true // 低版本Android不需要特殊权限
        }
    }

    /**
     * 显示权限请求对话框
     */
    private fun showPermissionDialog() {
        AlertDialog.Builder(context)
            .setTitle("需要存储权限")
            .setMessage("为了浏览和选择字体文件，需要授予存储访问权限。\n\n请在设置中允许「所有文件访问权限」。")
            .setPositiveButton("去设置") { _, _ ->
                try {
                    val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                        data = Uri.parse("package:${context.packageName}")
                    }
                    context.startActivity(intent)
                } catch (e: Exception) {
                    AppLog.e("【字体文件选择器】打开设置失败", e)
                    ToastUtils.showToast(context, "请手动在设置中授予存储权限")
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 初始化视图
     */
    private fun initViews(view: View) {
        tvCurrentPath = view.findViewById(R.id.tv_current_path)
        tvFileCount = view.findViewById(R.id.tv_file_count)
        rvFiles = view.findViewById(R.id.rv_files)
        btnClose = view.findViewById(R.id.btn_close)
    }
    
    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        rvFiles.layoutManager = LinearLayoutManager(context)
        fileAdapter = FileAdapter()
        rvFiles.adapter = fileAdapter
    }
    
    /**
     * 设置监听器
     */
    private fun setupListeners() {
        btnClose.setOnClickListener {
            dialog?.dismiss()
        }
    }
    
    /**
     * 加载当前目录
     */
    private fun loadCurrentDirectory() {
        try {
            AppLog.d("【字体文件选择器】开始加载目录: ${currentDirectory.absolutePath}")
            tvCurrentPath.text = currentDirectory.absolutePath

            fileList.clear()

            // 检查目录权限
            if (!currentDirectory.exists()) {
                AppLog.e("【字体文件选择器】目录不存在: ${currentDirectory.absolutePath}")
                ToastUtils.showToast(context, "目录不存在")
                return
            }

            if (!currentDirectory.canRead()) {
                AppLog.e("【字体文件选择器】无法读取目录: ${currentDirectory.absolutePath}")
                ToastUtils.showToast(context, "无法读取目录，权限不足")
                return
            }

            // 添加返回上级目录项（如果不是根目录）
            if (currentDirectory.parent != null) {
                fileList.add(FileItem(
                    file = currentDirectory.parentFile!!,
                    name = ".. (返回上级)",
                    isDirectory = true
                ))
            }

            // 获取当前目录下的文件和文件夹
            val files = currentDirectory.listFiles()
            AppLog.d("【字体文件选择器】目录下文件总数: ${files?.size ?: "null(权限不足或目录为空)"}")

            if (files == null) {
                AppLog.w("【字体文件选择器】无法读取目录内容，可能是权限问题: ${currentDirectory.absolutePath}")

                // 尝试使用不同的方法列出文件
                try {
                    val fileNames = currentDirectory.list()
                    if (fileNames != null) {
                        AppLog.d("【字体文件选择器】使用list()方法找到文件名: ${fileNames.toList()}")
                        // 手动创建File对象
                        fileNames.forEach { fileName ->
                            val file = File(currentDirectory, fileName)
                            AppLog.v("【字体文件选择器】检查文件: $fileName, 存在: ${file.exists()}, 是文件: ${file.isFile}, 是目录: ${file.isDirectory}")

                            if (file.isDirectory) {
                                fileList.add(FileItem(
                                    file = file,
                                    name = fileName,
                                    isDirectory = true
                                ))
                            } else if (file.isFile) {
                                val lowerName = fileName.lowercase()
                                if (lowerName.endsWith(".ttf") || lowerName.endsWith(".otf")) {
                                    fileList.add(FileItem(
                                        file = file,
                                        name = fileName,
                                        isDirectory = false,
                                        isTtfFile = true
                                    ))
                                    AppLog.d("【字体文件选择器】通过list()方法找到字体文件: $fileName")
                                }
                            }
                        }
                    } else {
                        AppLog.e("【字体文件选择器】list()方法也返回null，完全无法访问目录")
                    }
                } catch (e: Exception) {
                    AppLog.e("【字体文件选择器】使用list()方法失败", e)
                }

            } else if (files.isNotEmpty()) {
                // 先添加文件夹
                val directories = files.filter { it.isDirectory }
                AppLog.d("【字体文件选择器】找到文件夹数量: ${directories.size}")

                directories.sortedBy { it.name.lowercase() }
                    .forEach { dir ->
                        fileList.add(FileItem(
                            file = dir,
                            name = dir.name,
                            isDirectory = true
                        ))
                        AppLog.v("【字体文件选择器】添加文件夹: ${dir.name}")
                    }

                // 再添加字体文件 - 使用更宽松的过滤条件
                val allFiles = files.filter { it.isFile }
                AppLog.d("【字体文件选择器】找到文件数量: ${allFiles.size}")

                val fontFiles = allFiles.filter { file ->
                    val fileName = file.name.lowercase()
                    val isTtf = fileName.endsWith(".ttf") || fileName.endsWith(".otf")
                    AppLog.v("【字体文件选择器】检查文件: ${file.name}, 是字体文件: $isTtf")
                    isTtf
                }

                AppLog.d("【字体文件选择器】找到字体文件数量: ${fontFiles.size}")

                fontFiles.sortedBy { it.name.lowercase() }
                    .forEach { file ->
                        fileList.add(FileItem(
                            file = file,
                            name = file.name,
                            isDirectory = false,
                            isTtfFile = true
                        ))
                        AppLog.d("【字体文件选择器】添加字体文件: ${file.name}")
                    }

            } else {
                AppLog.d("【字体文件选择器】目录为空或listFiles()返回空数组")
            }

            fileAdapter.notifyDataSetChanged()

            // 更新文件统计显示
            val folderCount = fileList.count { it.isDirectory && !it.name.startsWith("..") }
            val fontFileCount = fileList.count { it.isTtfFile }
            tvFileCount.text = "文件夹: $folderCount, 字体文件: $fontFileCount"

            AppLog.d("【字体文件选择器】已加载目录: ${currentDirectory.absolutePath}, 总项目数: ${fileList.size}, 文件夹: $folderCount, 字体文件: $fontFileCount")

        } catch (e: Exception) {
            AppLog.e("【字体文件选择器】加载目录失败", e)
            ToastUtils.showToast(context, "加载目录失败: ${e.message}")
        }
    }
    
    /**
     * 获取初始目录
     */
    private fun getInitialDirectory(): File {
        val possibleDirs = listOf(
            // 下载目录
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
            // 外部存储根目录
            Environment.getExternalStorageDirectory(),
            // 文档目录
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS),
            // SD卡根目录的常见路径
            File("/storage/emulated/0"),
            File("/sdcard"),
            // 应用内部存储
            context.filesDir
        )

        for (dir in possibleDirs) {
            try {
                if (dir.exists() && dir.canRead()) {
                    AppLog.d("【字体文件选择器】选择初始目录: ${dir.absolutePath}")
                    return dir
                }
            } catch (e: Exception) {
                AppLog.w("【字体文件选择器】检查目录失败: ${dir.absolutePath}", e)
            }
        }

        // 最后备选
        AppLog.w("【字体文件选择器】使用应用内部目录作为初始目录")
        return context.filesDir
    }
    
    /**
     * 文件适配器
     */
    private inner class FileAdapter : RecyclerView.Adapter<FileAdapter.FileViewHolder>() {
        
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FileViewHolder {
            val view = LayoutInflater.from(context).inflate(R.layout.item_font_file, parent, false)
            return FileViewHolder(view)
        }
        
        override fun onBindViewHolder(holder: FileViewHolder, position: Int) {
            holder.bind(fileList[position])
        }
        
        override fun getItemCount(): Int = fileList.size
        
        inner class FileViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            private val ivIcon: ImageView = itemView.findViewById(R.id.iv_file_icon)
            private val tvFileName: TextView = itemView.findViewById(R.id.tv_file_name)
            
            fun bind(item: FileItem) {
                tvFileName.text = item.name
                
                // 设置图标
                when {
                    item.name.startsWith("..") -> {
                        ivIcon.setImageResource(R.drawable.ic_arrow_back)
                    }
                    item.isDirectory -> {
                        ivIcon.setImageResource(R.drawable.ic_folder)
                    }
                    item.isTtfFile -> {
                        ivIcon.setImageResource(R.drawable.ic_font)
                    }
                    else -> {
                        ivIcon.setImageResource(R.drawable.ic_file)
                    }
                }
                
                // 设置点击事件
                itemView.setOnClickListener {
                    AppLog.d("【字体文件选择器】点击项目: ${item.name}, 是目录: ${item.isDirectory}, 是字体文件: ${item.isTtfFile}")

                    if (item.isDirectory) {
                        // 进入目录
                        AppLog.d("【字体文件选择器】进入目录: ${item.file.absolutePath}")
                        currentDirectory = item.file
                        loadCurrentDirectory()
                    } else if (item.isTtfFile) {
                        // 选择字体文件
                        AppLog.d("【字体文件选择器】选择字体文件: ${item.file.absolutePath}")
                        onFileSelected(item.file)
                        dialog?.dismiss()
                    } else {
                        AppLog.w("【字体文件选择器】点击了非字体文件: ${item.name}")
                    }
                }
            }
        }
    }
}
