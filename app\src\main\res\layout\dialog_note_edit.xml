<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="#F5F5F5"
        android:paddingStart="16dp"
        android:paddingEnd="10dp"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="编辑备注"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_centerVertical="true" />

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_close"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:contentDescription="关闭" />

    </RelativeLayout>

    <!-- 内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

    <!-- 设备信息显示 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="设备信息："
        android:textSize="14sp"
        android:textColor="#666666"
        android:layout_marginBottom="4dp" />

    <TextView
        android:id="@+id/tv_device_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="vivo V2244A（ID:12345678）"
        android:textSize="14sp"
        android:textColor="#333333"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <!-- 备注输入 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="备注："
        android:textSize="14sp"
        android:textColor="#666666"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/et_device_note"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:hint="请输入设备备注（如：客厅65寸智能电视专用投屏设备）"
        android:inputType="textMultiLine|textCapSentences"
        android:gravity="top|start"
        android:textSize="14sp"
        android:padding="12dp"
        android:background="@drawable/edittext_background"
        android:layout_marginBottom="8dp"
        android:scrollbars="vertical"
        android:overScrollMode="always"
        android:scrollbarStyle="insideInset"
        android:fadeScrollbars="false" />

    <!-- 提示信息 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="💡 支持多行输入，留空则显示为&quot;无&quot;"
        android:textSize="12sp"
        android:textColor="#999999" />

    </LinearLayout>

</LinearLayout>
