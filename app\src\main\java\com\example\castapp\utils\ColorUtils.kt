package com.example.castapp.utils

import android.graphics.Color
import androidx.core.graphics.toColorInt
import kotlin.math.roundToInt

/**
 * 🎨 颜色工具类
 * 提供颜色格式转换、验证和处理功能
 */
object ColorUtils {

    /**
     * 🎨 将颜色转换为ARGB HEX字符串
     * @param color ARGB颜色值
     * @return HEX字符串，格式：#AARRGGBB
     */
    fun colorToArgbHex(color: Int): String {
        return String.format("#%08X", color)
    }

    /**
     * 🎨 解析HEX字符串为颜色值
     * @param hexString HEX字符串，支持 #RGB, #RRGGBB, #AARRGGBB 格式
     * @return 颜色值，解析失败返回null
     */
    fun parseHexColor(hexString: String): Int? {
        return try {
            var hex = hexString.trim()
            
            // 移除#前缀
            if (hex.startsWith("#")) {
                hex = hex.substring(1)
            }
            
            when (hex.length) {
                3 -> {
                    // #RGB -> #RRGGBB
                    val r = hex[0].toString().repeat(2)
                    val g = hex[1].toString().repeat(2)
                    val b = hex[2].toString().repeat(2)
                    "#FF$r$g$b".toColorInt()
                }
                6 -> {
                    // #RRGGBB -> #FFRRGGBB
                    "#FF$hex".toColorInt()
                }
                8 -> {
                    // #AARRGGBB
                    "#$hex".toColorInt()
                }
                else -> null
            }
        } catch (e: Exception) {
            AppLog.w("🎨 解析HEX颜色失败: $hexString", e)
            null
        }
    }



    /**
     * 🎨 从ARGB值创建颜色
     * @param a 透明度分量 (0-255)
     * @param r 红色分量 (0-255)
     * @param g 绿色分量 (0-255)
     * @param b 蓝色分量 (0-255)
     * @return 颜色值，输入无效返回null
     */
    fun createArgbColor(a: Int, r: Int, g: Int, b: Int): Int? {
        return if (isValidRgbComponent(a) && isValidRgbComponent(r) && 
                  isValidRgbComponent(g) && isValidRgbComponent(b)) {
            Color.argb(a, r, g, b)
        } else {
            null
        }
    }

    /**
     * 🎨 设置颜色的透明度
     * @param color 原始颜色
     * @param alpha 透明度 (0.0f-1.0f)
     * @return 新的颜色值
     */
    fun setColorAlpha(color: Int, alpha: Float): Int {
        val alphaInt = (alpha * 255).roundToInt().coerceIn(0, 255)
        return Color.argb(alphaInt, Color.red(color), Color.green(color), Color.blue(color))
    }

    /**
     * 🎨 获取颜色的透明度百分比
     * @param color 颜色值
     * @return 透明度百分比 (0-100)
     */
    fun getAlphaPercentage(color: Int): Int {
        val alpha = Color.alpha(color)
        return ((alpha / 255.0f) * 100).roundToInt()
    }

    /**
     * 🎨 验证RGB分量是否有效
     * @param component RGB分量值
     * @return 是否有效 (0-255)
     */
    fun isValidRgbComponent(component: Int): Boolean {
        return component in 0..255
    }


    /**
     * 🎨 获取颜色的RGB字符串表示
     * @param color 颜色值
     * @return RGB字符串，格式：RGB(R, G, B)
     */
    fun colorToRgbString(color: Int): String {
        val r = Color.red(color)
        val g = Color.green(color)
        val b = Color.blue(color)
        return "RGB($r, $g, $b)"
    }

    /**
     * 🎨 判断颜色是否为深色
     * @param color 颜色值
     * @return 是否为深色
     */
    fun isDarkColor(color: Int): Boolean {
        val r = Color.red(color)
        val g = Color.green(color)
        val b = Color.blue(color)
        // 使用亮度公式判断
        val brightness = (r * 0.299 + g * 0.587 + b * 0.114)
        return brightness < 128
    }

    /**
     * 🎨 获取对比色（黑色或白色）
     * @param color 背景颜色
     * @return 对比色（黑色或白色）
     */
    fun getContrastColor(color: Int): Int {
        return if (isDarkColor(color)) Color.WHITE else Color.BLACK
    }
}
