package com.example.castapp

import org.junit.Test
import org.junit.Assert.*
import kotlin.math.roundToInt

/**
 * ResolutionManager相关的单元测试
 */
class ResolutionManagerTest {

    @Test
    fun testWindowSizeCalculation() {
        // 测试不同分辨率的窗口尺寸计算
        
        // 测试1080×2400分辨率（原硬编码分辨率）
        val (width1, height1) = calculateWindowSize(1080, 2400)
        assertEquals(432, width1)  // 1080 * 0.4
        assertEquals(960, height1) // 2400 * 0.4
        
        // 测试1920×1080分辨率（常见横屏分辨率）
        val (width2, height2) = calculateWindowSize(1920, 1080)
        assertEquals(768, width2)  // 1920 * 0.4
        assertEquals(432, height2) // 1080 * 0.4
        
        // 测试2560×1440分辨率（2K分辨率）
        val (width3, height3) = calculateWindowSize(2560, 1440)
        assertEquals(1024, width3) // 2560 * 0.4
        assertEquals(576, height3)  // 1440 * 0.4
        
        // 测试3840×2160分辨率（4K分辨率）
        val (width4, height4) = calculateWindowSize(3840, 2160)
        assertEquals(1536, width4) // 3840 * 0.4
        assertEquals(864, height4)  // 2160 * 0.4
    }
    
    @Test
    fun testResolutionValidation() {
        // 测试分辨率验证逻辑
        
        // 有效分辨率
        assertTrue("1920×1080应该是有效分辨率", isValidResolution(1920, 1080))
        assertTrue("1080×2400应该是有效分辨率", isValidResolution(1080, 2400))
        assertTrue("2560×1440应该是有效分辨率", isValidResolution(2560, 1440))
        assertTrue("3840×2160应该是有效分辨率", isValidResolution(3840, 2160))
        
        // 无效分辨率
        assertFalse("0×0应该是无效分辨率", isValidResolution(0, 0))
        assertFalse("负数分辨率应该无效", isValidResolution(-1920, 1080))
        assertFalse("过大分辨率应该无效", isValidResolution(8192, 8192))
        assertFalse("奇数分辨率应该无效", isValidResolution(1921, 1080))
    }
    
    @Test
    fun testResolutionScaleCalculation() {
        // 测试分辨率缩放计算
        
        // 假设原始分辨率为1080×1920
        val originalWidth = 1080
        val originalHeight = 1920
        
        // 测试不同缩放比例
        val scale50 = calculateScaledResolution(originalWidth, originalHeight, 50)
        assertEquals(540, scale50.first)  // 1080 * 0.5
        assertEquals(960, scale50.second) // 1920 * 0.5
        
        val scale150 = calculateScaledResolution(originalWidth, originalHeight, 150)
        assertEquals(1620, scale150.first)  // 1080 * 1.5
        assertEquals(2880, scale150.second) // 1920 * 1.5
        
        // 测试边界值
        val scale1 = calculateScaledResolution(originalWidth, originalHeight, 1)
        assertEquals(10, scale1.first)   // 1080 * 0.01 = 10.8，向下取偶数为10
        assertEquals(18, scale1.second)  // 1920 * 0.01 = 19.2，向下取偶数为18
        
        // 测试100%缩放
        val scale100 = calculateScaledResolution(originalWidth, originalHeight, 100)
        assertEquals(originalWidth, scale100.first)
        assertEquals(originalHeight, scale100.second)
    }
    
    @Test
    fun testScaleRangeValidation() {
        // 测试缩放范围验证
        assertTrue("1%应该是有效缩放比例", isValidScale(1))
        assertTrue("100%应该是有效缩放比例", isValidScale(100))
        assertTrue("150%应该是有效缩放比例", isValidScale(150))
        
        assertFalse("0%应该是无效缩放比例", isValidScale(0))
        assertFalse("151%应该是无效缩放比例", isValidScale(151))
        assertFalse("负数应该是无效缩放比例", isValidScale(-10))
    }
    
    // 辅助方法
    private fun calculateWindowSize(senderWidth: Int, senderHeight: Int): Pair<Int, Int> {
        val windowWidth = (senderWidth * 0.4).roundToInt()
        val windowHeight = (senderHeight * 0.4).roundToInt()
        return Pair(windowWidth, windowHeight)
    }
    
    private fun isValidResolution(width: Int, height: Int): Boolean {
        return width > 0 && height > 0 && 
               width <= 4096 && height <= 4096 && 
               width % 2 == 0 && height % 2 == 0
    }
    
    private fun calculateScaledResolution(originalWidth: Int, originalHeight: Int, scalePercent: Int): Pair<Int, Int> {
        val actualWidth = (originalWidth * scalePercent / 100.0).roundToInt()
        val actualHeight = (originalHeight * scalePercent / 100.0).roundToInt()
        
        // 确保分辨率为偶数（H.264编码要求）
        val evenWidth = if (actualWidth % 2 == 0) actualWidth else actualWidth - 1
        val evenHeight = if (actualHeight % 2 == 0) actualHeight else actualHeight - 1
        
        return Pair(evenWidth, evenHeight)
    }
    
    private fun isValidScale(scalePercent: Int): Boolean {
        return scalePercent in 1..150
    }

    @Test
    fun testWindowContainerSizeStability() {
        // 测试窗口容器尺寸的稳定性
        // 验证窗口容器尺寸不应随编码分辨率调整而改变

        val originalSenderWidth = 1080
        val originalSenderHeight = 1920

        // 基于发送端原始分辨率计算窗口容器尺寸（使用本地方法模拟）
        val (containerWidth, containerHeight) = calculateWindowSize(originalSenderWidth, originalSenderHeight)
        assertEquals(432, containerWidth)  // 1080 * 0.4
        assertEquals(768, containerHeight) // 1920 * 0.4

        // 模拟不同的编码分辨率（通过分辨率滑动条调整）
        val encodingResolutions = listOf(
            calculateScaledResolution(originalSenderWidth, originalSenderHeight, 50),   // 50%
            calculateScaledResolution(originalSenderWidth, originalSenderHeight, 80),   // 80%
            calculateScaledResolution(originalSenderWidth, originalSenderHeight, 120),  // 120%
            calculateScaledResolution(originalSenderWidth, originalSenderHeight, 150)   // 150%
        )

        // 验证：无论编码分辨率如何变化，窗口容器尺寸都应该保持固定
        encodingResolutions.forEach { (encodingWidth, encodingHeight) ->
            // 窗口容器尺寸应该始终基于原始分辨率，而不是编码分辨率
            val (fixedContainerWidth, fixedContainerHeight) = calculateWindowSize(originalSenderWidth, originalSenderHeight)

            assertEquals("窗口容器宽度应该固定不变", containerWidth.toLong(), fixedContainerWidth.toLong())
            assertEquals("窗口容器高度应该固定不变", containerHeight.toLong(), fixedContainerHeight.toLong())

            // 编码分辨率可以变化，但不应影响窗口容器尺寸
            assertNotEquals("编码分辨率应该与原始分辨率不同", originalSenderWidth.toLong(), encodingWidth.toLong())
            assertNotEquals("编码分辨率应该与原始分辨率不同", originalSenderHeight.toLong(), encodingHeight.toLong())
        }

        println("✅ 窗口容器尺寸稳定性测试通过：容器尺寸固定为${containerWidth}x${containerHeight}")
    }

    @Test
    fun testResolutionSeparation() {
        // 测试两种分辨率概念的分离

        val originalWidth = 1080
        val originalHeight = 1920

        // 原始屏幕分辨率（用于窗口容器）
        val (containerWidth, containerHeight) = calculateWindowSize(originalWidth, originalHeight)

        // 不同的编码分辨率（用于视频编码/解码）
        val scale75 = calculateScaledResolution(originalWidth, originalHeight, 75)
        val scale125 = calculateScaledResolution(originalWidth, originalHeight, 125)

        // 验证：窗口容器尺寸与编码分辨率无关
        assertNotEquals("75%编码分辨率与窗口容器宽度应该不同", scale75.first.toLong(), containerWidth.toLong())
        assertNotEquals("75%编码分辨率与窗口容器高度应该不同", scale75.second.toLong(), containerHeight.toLong())
        assertNotEquals("125%编码分辨率与窗口容器宽度应该不同", scale125.first.toLong(), containerWidth.toLong())
        assertNotEquals("125%编码分辨率与窗口容器高度应该不同", scale125.second.toLong(), containerHeight.toLong())

        // 验证：编码分辨率确实在变化
        assertEquals(810, scale75.first)   // 1080 * 0.75
        assertEquals(1440, scale75.second) // 1920 * 0.75
        assertEquals(1350, scale125.first)  // 1080 * 1.25
        assertEquals(2400, scale125.second) // 1920 * 1.25

        println("✅ 分辨率分离测试通过：窗口容器=${containerWidth}x${containerHeight}, 编码分辨率可变")
    }
}
