package com.example.castapp.model

import java.io.Serializable
import java.util.UUID

/**
 * 远程接收端数据模型
 * 用于管理远程接收端设备信息
 */
data class RemoteReceiverConnection(
    val id: String,
    val ipAddress: String,
    val port: Int = 7777, // 固定使用7777端口
    val deviceName: String,
    val isConnected: Boolean = false,
    val lastConnectedTime: Long = 0L,
    val createdTime: Long = System.currentTimeMillis(),
    val screenWidth: Int = 0, // 接收端设备屏幕宽度
    val screenHeight: Int = 0 // 接收端设备屏幕高度
) : Serializable {
    
    /**
     * 获取显示文本
     */
    fun getDisplayText(): String = "$ipAddress:$port"
    
    /**
     * 获取连接状态文本
     */
    fun getStatusText(): String = if (isConnected) "已连接" else "未连接"
    
    /**
     * 更新连接状态
     */
    fun withConnectionState(connected: Boolean): RemoteReceiverConnection = copy(
        isConnected = connected,
        lastConnectedTime = if (connected) System.currentTimeMillis() else lastConnectedTime
    )

    /**
     * 更新屏幕分辨率
     */
    fun withScreenResolution(width: Int, height: Int): RemoteReceiverConnection = copy(
        screenWidth = width,
        screenHeight = height
    )

    /**
     * 获取屏幕分辨率文本
     */
    fun getScreenResolutionText(): String {
        return if (screenWidth > 0 && screenHeight > 0) {
            "${screenWidth}×${screenHeight}"
        } else {
            "未知分辨率"
        }
    }

    /**
     * 检查是否有有效的屏幕分辨率信息
     */
    fun hasValidScreenResolution(): Boolean = screenWidth > 0 && screenHeight > 0
    
    companion object {
        /**
         * 生成接收端ID
         */
        fun generateId(): String = UUID.randomUUID().toString()
        
        /**
         * 创建新的远程接收端
         */
        fun create(ipAddress: String, deviceName: String): RemoteReceiverConnection {
            return RemoteReceiverConnection(
                id = generateId(),
                ipAddress = ipAddress,
                port = 7777, // 固定端口
                deviceName = deviceName
            )
        }
        
        /**
         * 验证IP地址格式
         */
        fun isValidIpAddress(ip: String): Boolean {
            return try {
                val parts = ip.split(".")
                if (parts.size != 4) return false
                parts.all { part ->
                    val num = part.toIntOrNull()
                    num != null && num in 0..255
                }
            } catch (_: Exception) {
                false
            }
        }
        
        /**
         * 验证设备名称
         */
        fun isValidDeviceName(name: String): Boolean {
            return name.isNotBlank() && name.length <= 50
        }
    }
}
