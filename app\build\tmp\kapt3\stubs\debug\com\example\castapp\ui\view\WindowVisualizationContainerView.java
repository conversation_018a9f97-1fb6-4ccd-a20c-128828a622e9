package com.example.castapp.ui.view;

/**
 * 🪟 单个窗口容器可视化View
 * 使用View.clipBounds实现裁剪，与接收端CropManager保持一致
 * 🎯 修复：改为FrameLayout以支持裁剪覆盖层
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00b8\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0016\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u001c\n\u0002\u0010 \n\u0002\b\u0003\u0018\u00002\u00020\u0001B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0010\u0010&\u001a\u00020\u00112\u0006\u0010\'\u001a\u00020#H\u0002J\u0018\u0010(\u001a\u00020\u00112\u0006\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020,H\u0002J8\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070.2\u0006\u0010/\u001a\u00020\u00072\u0006\u00100\u001a\u00020\u00072\u0012\u00101\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020302H\u0002J$\u00104\u001a\u00020\u00072\u0006\u00105\u001a\u00020\u00072\u0012\u00106\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020302H\u0002J\u0010\u00107\u001a\u00020\u00112\u0006\u00108\u001a\u00020\u0010H\u0002J0\u00109\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u000203022\u0012\u00106\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u000203022\u0006\u0010+\u001a\u00020,H\u0002J\b\u0010:\u001a\u00020\u0011H\u0002J\u0010\u0010;\u001a\u00020\u00112\u0006\u0010<\u001a\u00020\u0007H\u0002J\u0010\u0010=\u001a\u00020\u00112\u0006\u0010>\u001a\u00020?H\u0002J\u0010\u0010@\u001a\u00020\u00112\u0006\u0010A\u001a\u00020\u0007H\u0002J\u0018\u0010B\u001a\u00020\u00112\u0006\u0010C\u001a\u00020\n2\u0006\u0010D\u001a\u00020\nH\u0002J\u0010\u0010E\u001a\u00020\u00112\u0006\u0010F\u001a\u00020*H\u0002J\u0010\u0010G\u001a\u00020\u00112\u0006\u0010H\u001a\u00020*H\u0002J \u0010I\u001a\u00020\u00112\u0006\u0010J\u001a\u00020\n2\u0006\u0010K\u001a\u00020*2\u0006\u0010<\u001a\u00020\u0007H\u0002J\u0010\u0010L\u001a\u00020\u00112\u0006\u0010M\u001a\u00020\u0007H\u0002J\u0018\u0010N\u001a\u00020\u00112\u0006\u0010J\u001a\u00020\n2\u0006\u0010<\u001a\u00020\u0007H\u0002J\b\u0010O\u001a\u00020\u0011H\u0002J\b\u0010P\u001a\u00020\u0011H\u0016J$\u0010Q\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007022\u0006\u0010R\u001a\u00020\u001d2\u0006\u0010S\u001a\u00020\u001dH\u0002J\u0018\u0010T\u001a\u00020\u00112\u0006\u0010\'\u001a\u00020#2\u0006\u0010U\u001a\u00020VH\u0002J\u0010\u0010W\u001a\u00020\u00112\u0006\u0010\'\u001a\u00020#H\u0002J\u0010\u0010X\u001a\u00020*2\u0006\u0010Y\u001a\u00020*H\u0002J(\u0010Z\u001a\u00020\u00112\u0006\u0010[\u001a\u00020\\2\u0006\u0010]\u001a\u00020^2\u0006\u0010_\u001a\u00020\u00102\u0006\u0010\'\u001a\u00020#H\u0002J\u0018\u0010`\u001a\u00020\u00112\u0006\u0010[\u001a\u00020\\2\u0006\u0010\'\u001a\u00020#H\u0002J\u000e\u0010a\u001a\u00020\u00112\u0006\u0010b\u001a\u00020\nJ\u0006\u0010c\u001a\u00020\u0011J\u0006\u0010d\u001a\u00020\u0011J\u0010\u0010e\u001a\u00020\u00112\u0006\u0010U\u001a\u00020VH\u0002J\u0014\u0010f\u001a\u0010\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u000203\u0018\u000102J\n\u0010g\u001a\u0004\u0018\u00010VH\u0002J\u001e\u0010h\u001a\u0004\u0018\u00010i2\u0012\u00106\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020302H\u0002J\u0010\u0010j\u001a\u00020\u00112\u0006\u0010\'\u001a\u00020#H\u0002J\b\u0010k\u001a\u00020\u0011H\u0002J\b\u0010l\u001a\u00020\nH\u0002J6\u0010m\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0018\u00010.2\u0006\u0010R\u001a\u00020\u001d2\u0006\u0010S\u001a\u00020\u001d2\u0006\u0010n\u001a\u00020\u00072\u0006\u0010o\u001a\u00020\u0007H\u0002J\b\u0010p\u001a\u00020\u0011H\u0014J\u0010\u0010q\u001a\u00020\u00112\u0006\u0010[\u001a\u00020\\H\u0014J\b\u0010r\u001a\u00020\u0011H\u0002J\b\u0010s\u001a\u00020\u0011H\u0002J\b\u0010t\u001a\u00020\u0011H\u0002J\u001a\u0010u\u001a\u00020\u00112\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00110\u001bJ\u000e\u0010w\u001a\u00020\u00112\u0006\u0010\'\u001a\u00020#J\u001a\u0010x\u001a\u00020\u00112\u0006\u0010\'\u001a\u00020#2\b\b\u0002\u0010y\u001a\u00020\nH\u0002J\b\u0010z\u001a\u00020\u0011H\u0002J,\u0010{\u001a\u00020\u00112\b\u0010|\u001a\u0004\u0018\u00010\u00102\u001a\u0010}\u001a\u0016\u0012\u0006\u0012\u0004\u0018\u00010\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00110\u000fJ\b\u0010~\u001a\u00020\u0011H\u0002J\u0010\u0010\u007f\u001a\u00020\u00112\u0006\u0010\'\u001a\u00020#H\u0002J\u0019\u0010\u0080\u0001\u001a\u00020\u00112\u0007\u0010\u0081\u0001\u001a\u00020*2\u0007\u0010\u0082\u0001\u001a\u00020*J\u0011\u0010\u0083\u0001\u001a\u00020\u00112\u0006\u0010\'\u001a\u00020#H\u0002J-\u0010\u0084\u0001\u001a\u00020\u00112\u000e\u0010\u0085\u0001\u001a\t\u0012\u0002\b\u0003\u0018\u00010\u0086\u00012\u0012\u00106\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020302H\u0002J!\u0010\u0087\u0001\u001a\u00020\u0011*\u00020!2\u0012\u00106\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020302H\u0002J!\u0010\u0088\u0001\u001a\u00020\u0011*\u00020!2\u0012\u00106\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020302H\u0002R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\r\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R$\u0010\u000e\u001a\u0018\u0012\u0006\u0012\u0004\u0018\u00010\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\"\u0010\u0014\u001a\u0004\u0018\u00010\u00132\b\u0010\u0012\u001a\u0004\u0018\u00010\u0013@BX\u0086\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u000e\u0010\u0017\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u001a\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u001c\u001a\u0010\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010 \u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\"\u001a\u0004\u0018\u00010#X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0089\u0001"}, d2 = {"Lcom/example/castapp/ui/view/WindowVisualizationContainerView;", "Landroid/widget/FrameLayout;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "borderStateBeforeCrop", "", "borderViewRef", "Ljava/lang/ref/WeakReference;", "Landroid/view/View;", "cropModeCallback", "Lkotlin/Function2;", "Landroid/graphics/RectF;", "", "<set-?>", "Lcom/example/castapp/ui/view/CropOverlayView;", "cropOverlay", "getCropOverlay", "()Lcom/example/castapp/ui/view/CropOverlayView;", "isCropping", "isDetached", "isRemoteEditMode", "onRemoteEditStateChangeListener", "Lkotlin/Function1;", "onRemoteTextChangeListener", "", "remoteTextEditPanel", "Lcom/example/castapp/ui/view/TextEditPanel;", "textWindowView", "Lcom/example/castapp/ui/view/TextWindowView;", "windowData", "Lcom/example/castapp/model/WindowVisualizationData;", "windowFillPaint", "Landroid/graphics/Paint;", "applyClipBounds", "data", "applyContainerScale", "scaleFactor", "", "remoteControlScale", "", "applyDpiAdjustmentToWindowSize", "Lkotlin/Pair;", "originalWidth", "originalHeight", "textFormatData", "", "", "applyDpiDensityAdjustment", "originalFontSize", "formatData", "applyFinalCropBounds", "cropRatio", "applyFontScaleToFormatData", "applyRemoteClearFormat", "applyRemoteColorChange", "color", "applyRemoteFontFamilyChange", "fontFamily", "Lcom/example/castapp/utils/FontPresetManager$FontItem;", "applyRemoteFontSizeChange", "fontSize", "applyRemoteFormatChanges", "bold", "italic", "applyRemoteLetterSpacingChange", "letterSpacing", "applyRemoteLineSpacingChange", "lineSpacing", "applyRemoteStrokeChange", "enabled", "width", "applyRemoteTextAlignmentChange", "alignment", "applyRemoteWindowColorChange", "bringBorderToFront", "bringToFront", "buildCharacterPositionMap", "originalText", "newText", "createBorderView", "parentView", "Landroid/view/ViewGroup;", "createTextWindowView", "dpToPx", "dp", "drawScreenshot", "canvas", "Landroid/graphics/Canvas;", "bitmap", "Landroid/graphics/Bitmap;", "bounds", "drawWindowContent", "endCropMode", "isCancel", "enterRemoteEditMode", "exitRemoteEditMode", "forceRemoveAllBorderViews", "getCurrentTextFormatData", "getMainContainer", "getReceiverDpiInfo", "Lcom/example/castapp/utils/DpiDensityManager$DpiInfo;", "handleTextWindowView", "hideRemoteEditPanel", "isTextWindow", "mapSpanPositionToNewText", "originalStart", "originalEnd", "onDetachedFromWindow", "onDraw", "removeBorderView", "removeTextWindowView", "safeRemoveBorderView", "setOnRemoteEditStateChangeListener", "listener", "setWindowData", "setWindowDataInternal", "forceRefresh", "showRemoteEditPanel", "startCropMode", "originalCropRatio", "callback", "updateBorderPosition", "updateBorderView", "updatePosition", "newX", "newY", "updateTextWindowView", "verifyNaturalLineBreak", "actualLineTexts", "", "applyOtherFormats", "applyTextFormat", "app_debug"})
public final class WindowVisualizationContainerView extends android.widget.FrameLayout {
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.model.WindowVisualizationData windowData;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.CropOverlayView cropOverlay;
    private boolean isCropping = false;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super android.graphics.RectF, ? super java.lang.Boolean, kotlin.Unit> cropModeCallback;
    @org.jetbrains.annotations.Nullable()
    private java.lang.ref.WeakReference<android.view.View> borderViewRef;
    private boolean isDetached = false;
    private boolean borderStateBeforeCrop = false;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.TextWindowView textWindowView;
    private boolean isRemoteEditMode = false;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.TextEditPanel remoteTextEditPanel;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onRemoteEditStateChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onRemoteTextChangeListener;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint windowFillPaint = null;
    
    @kotlin.jvm.JvmOverloads()
    public WindowVisualizationContainerView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.view.CropOverlayView getCropOverlay() {
        return null;
    }
    
    /**
     * 设置窗口数据并更新显示
     */
    public final void setWindowData(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 内部窗口数据设置方法，支持强制刷新
     */
    private final void setWindowDataInternal(com.example.castapp.model.WindowVisualizationData data, boolean forceRefresh) {
    }
    
    /**
     * 🎯 新增：更新窗口位置（拖动时调用，同步更新边框位置）
     */
    public final void updatePosition(float newX, float newY) {
    }
    
    /**
     * 🎯 应用View.clipBounds裁剪，与接收端CropManager保持一致
     */
    private final void applyClipBounds(com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 📝 处理文本窗口View的创建或更新
     */
    private final void handleTextWindowView(com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 📝 创建文本窗口View
     */
    private final void createTextWindowView(com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 📝 应用文本格式到TextWindowView
     */
    private final void applyTextFormat(com.example.castapp.ui.view.TextWindowView $this$applyTextFormat, java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
    }
    
    /**
     * 🎯 应用其他格式（背景颜色、行间距、对齐等）
     */
    private final void applyOtherFormats(com.example.castapp.ui.view.TextWindowView $this$applyOtherFormats, java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
    }
    
    /**
     * 📝 更新现有文本窗口View的内容
     * 🎯 修复：同时应用容器级缩放和字体级缩放，实现完整的同步效果
     */
    private final void updateTextWindowView(com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 📝 移除文本窗口View
     */
    private final void removeTextWindowView() {
    }
    
    /**
     * 🎯 最终方案：对格式数据进行最小化处理，主要处理DPI差异
     * 🔧 修复：增加DPI密度处理，从根本上解决不同设备间的换行差异
     * 🎯 关键修复：所有视觉缩放由容器级统一缩放处理，避免文字重新布局
     */
    private final java.util.Map<java.lang.String, java.lang.Object> applyFontScaleToFormatData(java.util.Map<java.lang.String, ? extends java.lang.Object> formatData, double remoteControlScale) {
        return null;
    }
    
    /**
     * 🎯 新增：应用DPI密度调整，解决不同设备间的换行差异
     */
    private final int applyDpiDensityAdjustment(int originalFontSize, java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
        return 0;
    }
    
    /**
     * 🎯 新增：从格式数据中获取接收端DPI信息
     */
    @kotlin.Suppress(names = {"UNCHECKED_CAST"})
    private final com.example.castapp.utils.DpiDensityManager.DpiInfo getReceiverDpiInfo(java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
        return null;
    }
    
    @java.lang.Override()
    protected void onDetachedFromWindow() {
    }
    
    @java.lang.Override()
    protected void onDraw(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas) {
    }
    
    /**
     * 绘制窗口内容
     */
    private final void drawWindowContent(android.graphics.Canvas canvas, com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 绘制截图
     */
    private final void drawScreenshot(android.graphics.Canvas canvas, android.graphics.Bitmap bitmap, android.graphics.RectF bounds, com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 🎯 新增：开始裁剪模式
     */
    public final void startCropMode(@org.jetbrains.annotations.Nullable()
    android.graphics.RectF originalCropRatio, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super android.graphics.RectF, ? super java.lang.Boolean, kotlin.Unit> callback) {
    }
    
    /**
     * 🎯 新增：结束裁剪模式
     */
    public final void endCropMode(boolean isCancel) {
    }
    
    /**
     * 🎯 新增：应用最终裁剪边界
     */
    private final void applyFinalCropBounds(android.graphics.RectF cropRatio) {
    }
    
    /**
     * 🎯 裁剪修复：更新独立边框View（强化边框清理，避免重复创建）
     */
    private final void updateBorderView(com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 🎯 新增：创建独立边框View
     */
    private final void createBorderView(com.example.castapp.model.WindowVisualizationData data, android.view.ViewGroup parentView) {
    }
    
    /**
     * 🎯 修复：安全移除边框View
     */
    private final void safeRemoveBorderView() {
    }
    
    /**
     * 🎯 兼容：保留原有接口
     */
    private final void removeBorderView() {
    }
    
    /**
     * 🎯 裁剪修复：强制移除所有边框View（解决裁剪模式下边框重复问题）
     */
    private final void forceRemoveAllBorderViews(android.view.ViewGroup parentView) {
    }
    
    /**
     * 🎯 修复：更新边框View位置（拖动时调用）
     */
    private final void updateBorderPosition() {
    }
    
    /**
     * 🎯 层级修复：重写bringToFront方法，确保边框View同步调整层级
     */
    @java.lang.Override()
    public void bringToFront() {
    }
    
    /**
     * 🎯 层级修复：将边框View移到前台（层级管理时调用）
     */
    private final void bringBorderToFront() {
    }
    
    /**
     * dp转px工具方法
     */
    private final float dpToPx(float dp) {
        return 0.0F;
    }
    
    /**
     * 🎯 精确映射格式span位置到新文本（用于富文本精准换行同步）
     * 将原始文本中的格式位置映射到按行重组后的新文本中
     * 🔧 修复：处理重复文本的精确位置映射
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> mapSpanPositionToNewText(java.lang.String originalText, java.lang.String newText, int originalStart, int originalEnd) {
        return null;
    }
    
    /**
     * 🎯 构建字符位置映射表（原始文本位置 -> 新文本位置）
     * 处理换行符重新排列后的精确字符位置对应关系
     */
    private final java.util.Map<java.lang.Integer, java.lang.Integer> buildCharacterPositionMap(java.lang.String originalText, java.lang.String newText) {
        return null;
    }
    
    /**
     * 🎯 进入遥控端编辑模式
     */
    public final void enterRemoteEditMode() {
    }
    
    /**
     * 🎯 退出遥控端编辑模式
     */
    public final void exitRemoteEditMode() {
    }
    
    /**
     * 🎯 显示遥控端编辑面板
     */
    private final void showRemoteEditPanel() {
    }
    
    /**
     * 🎯 隐藏遥控端编辑面板
     */
    private final void hideRemoteEditPanel() {
    }
    
    /**
     * 🎯 应用遥控端格式变化（对选中文字生效）
     */
    private final void applyRemoteFormatChanges(boolean bold, boolean italic) {
    }
    
    /**
     * 🎯 应用遥控端字号变化（对选中文字生效）
     */
    private final void applyRemoteFontSizeChange(int fontSize) {
    }
    
    /**
     * 🎯 应用遥控端字体变化（对选中文字生效）
     */
    private final void applyRemoteFontFamilyChange(com.example.castapp.utils.FontPresetManager.FontItem fontFamily) {
    }
    
    /**
     * 🎯 应用遥控端字间距变化
     */
    private final void applyRemoteLetterSpacingChange(float letterSpacing) {
    }
    
    /**
     * 🎯 应用遥控端行间距变化
     */
    private final void applyRemoteLineSpacingChange(float lineSpacing) {
    }
    
    /**
     * 🎯 应用遥控端文本对齐变化
     */
    private final void applyRemoteTextAlignmentChange(int alignment) {
    }
    
    /**
     * 🎯 应用遥控端颜色变化（对选中文字生效）
     */
    private final void applyRemoteColorChange(int color) {
    }
    
    /**
     * 🎯 应用遥控端描边变化
     */
    private final void applyRemoteStrokeChange(boolean enabled, float width, int color) {
    }
    
    /**
     * 🎯 应用遥控端窗口颜色变化
     */
    private final void applyRemoteWindowColorChange(boolean enabled, int color) {
    }
    
    /**
     * 🎯 应用遥控端清除格式
     */
    private final void applyRemoteClearFormat() {
    }
    
    /**
     * 🎯 获取主容器
     */
    private final android.view.ViewGroup getMainContainer() {
        return null;
    }
    
    /**
     * 🎯 检查是否为文本窗口
     */
    private final boolean isTextWindow() {
        return false;
    }
    
    /**
     * 🎯 设置遥控端编辑状态变化监听器
     */
    public final void setOnRemoteEditStateChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> listener) {
    }
    
    /**
     * 🎯 获取当前文本内容和格式数据（包含完整的富文本格式）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.util.Map<java.lang.String, java.lang.Object> getCurrentTextFormatData() {
        return null;
    }
    
    /**
     * 🎯 新增：验证自然换行结果是否与接收端一致
     */
    @kotlin.Suppress(names = {"UNUSED_PARAMETER"})
    private final void verifyNaturalLineBreak(java.util.List<?> actualLineTexts, java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
    }
    
    /**
     * 🎯 新增：应用DPI密度调整到窗口尺寸
     * 确保遥控端窗口尺寸能够容纳与接收端相同的文字内容，实现自然换行
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> applyDpiAdjustmentToWindowSize(int originalWidth, int originalHeight, java.util.Map<java.lang.String, ? extends java.lang.Object> textFormatData) {
        return null;
    }
    
    /**
     * 🎯 核心方法：应用容器级整体缩放（真正的放大镜效果）
     * 🎯 新方案：视图层统一处理所有缩放，避免文字重新布局
     * 这是实现遥控端与接收端缩放同步的核心方法
     */
    private final void applyContainerScale(float scaleFactor, double remoteControlScale) {
    }
    
    @kotlin.jvm.JvmOverloads()
    public WindowVisualizationContainerView(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public WindowVisualizationContainerView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs) {
        super(null);
    }
}