package com.example.castapp.utils

import com.example.castapp.model.Connection
import org.junit.Test
import org.junit.Assert.*

/**
 * 通知管理器单元测试
 * 验证统一通知管理器的功能
 */
class NotificationManagerTest {

    @Test
    fun testNotificationTypeIds() {
        // 验证通知ID的唯一性
        val notificationIds = NotificationManager.NotificationType.values().map { it.notificationId }
        val uniqueIds = notificationIds.toSet()

        assertEquals("通知ID应该是唯一的", notificationIds.size, uniqueIds.size)
    }

    @Test
    fun testChannelIds() {
        // 验证渠道ID的唯一性
        val channelIds = NotificationManager.NotificationType.values().map { it.channelId }
        val uniqueChannelIds = channelIds.toSet()

        assertEquals("渠道ID应该是唯一的", channelIds.size, uniqueChannelIds.size)
    }

    @Test
    fun testSimplifiedConnection() {
        // 测试简化的连接管理
        val connection = Connection.create("*************", 8080)

        // 验证connectionId不为空
        assertTrue("connectionId应该不为空", connection.connectionId.isNotBlank())

        // 验证基本属性
        assertEquals("IP地址应该正确", "*************", connection.ipAddress)
        assertEquals("端口应该正确", 8080, connection.port)

        // 验证显示文本
        assertEquals("显示文本应该正确", "*************:8080", connection.getDisplayText())
    }

    @Test
    fun testNotificationConfig() {
        // 测试通知配置类
        val config = NotificationManager.NotificationConfig(
            title = "测试标题",
            content = "测试内容"
        )
        
        assertEquals("测试标题", config.title)
        assertEquals("测试内容", config.content)
        assertTrue("默认应该是持续通知", config.isOngoing)
        assertTrue("默认动作列表应该为空", config.actions.isEmpty())
    }

    @Test
    fun testNotificationTypeValues() {
        // 验证所有通知类型的基本属性
        NotificationManager.NotificationType.values().forEach { type ->
            assertTrue("通知ID应该大于0", type.notificationId > 0)
            assertTrue("渠道ID不应该为空", type.channelId.isNotBlank())
            assertTrue("渠道名称不应该为空", type.channelName.isNotBlank())
            assertTrue("渠道描述不应该为空", type.channelDescription.isNotBlank())
        }
    }

    @Test
    fun testSpecificNotificationIds() {
        // 验证特定的通知ID值
        assertEquals(1001, NotificationManager.NotificationType.CASTING.notificationId)
        assertEquals(1002, NotificationManager.NotificationType.RECEIVING.notificationId)
        assertEquals(2001, NotificationManager.NotificationType.AUDIO_STREAMING.notificationId)
        assertEquals(2003, NotificationManager.NotificationType.FLOATING_STOPWATCH.notificationId)
    }
}
