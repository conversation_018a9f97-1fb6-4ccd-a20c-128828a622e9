package com.example.castapp.manager.windowsettings

import android.app.Activity
import android.os.Handler
import android.os.Looper
import androidx.core.net.toUri
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.utils.AppLog

/**
 * 窗口布局模块
 * 负责处理投屏窗口的布局和层级管理
 */
class WindowLayoutModule(
    private val dataModule: WindowDataModule
) {
    
    // 对话框刷新回调
    private var dialogRefreshCallback: (() -> Unit)? = null
    
    /**
     * 设置对话框刷新回调
     */
    fun setDialogRefreshCallback(callback: () -> Unit) {
        this.dialogRefreshCallback = callback
    }

    /**
     * 🏷️ 同步备注信息到SharedPreferences
     * 在应用布局时，将数据库中的备注信息同步到SharedPreferences中
     */
    private fun syncNotesToSharedPreferences(
        layoutItems: List<com.example.castapp.database.entity.WindowLayoutItemEntity>,
        activity: Activity
    ) {
        try {
            val noteManager = com.example.castapp.utils.NoteManager(activity)
            var syncCount = 0

            layoutItems.forEach { layoutItem ->
                // 如果布局项中有备注信息，同步到SharedPreferences
                if (!layoutItem.note.isNullOrBlank() && layoutItem.note != "无") {
                    val success = noteManager.saveNote(layoutItem.deviceId, layoutItem.note)
                    if (success) {
                        syncCount++
                        AppLog.d("【备注同步】成功同步备注: ${layoutItem.deviceId} -> ${layoutItem.note}")
                    } else {
                        AppLog.w("【备注同步】同步备注失败: ${layoutItem.deviceId}")
                    }
                }
            }

            AppLog.d("【备注同步】备注同步完成，成功同步 $syncCount 个备注")

        } catch (e: Exception) {
            AppLog.e("【备注同步】同步备注信息到SharedPreferences失败", e)
        }
    }

    /**
     * 📝 同步文本格式信息到TextFormatManager
     * 在应用布局时，将数据库中的文本格式信息同步到TextFormatManager中
     */
    private fun syncTextFormatsToSharedPreferences(
        layoutItems: List<com.example.castapp.database.entity.WindowLayoutItemEntity>,
        activity: Activity
    ) {
        try {
            val textFormatManager = com.example.castapp.utils.TextFormatManager(activity)
            var syncCount = 0

            layoutItems.forEach { layoutItem ->
                // 只处理文本窗口
                if (layoutItem.deviceId.startsWith("text_")) {
                    // 📝 优先恢复富文本格式
                    if (!layoutItem.richTextData.isNullOrBlank() && !layoutItem.textContent.isNullOrBlank()) {
                        try {
                            // 恢复富文本格式
                            val spannableString = android.text.SpannableString(layoutItem.textContent)
                            val jsonObject = org.json.JSONObject(layoutItem.richTextData)
                            val spansArray = jsonObject.getJSONArray("spans")

                            for (i in 0 until spansArray.length()) {
                                try {
                                    val spanObject = spansArray.getJSONObject(i)
                                    val start = spanObject.optInt("start", -1)
                                    val end = spanObject.optInt("end", -1)
                                    val flags = spanObject.optInt("flags", 0)
                                    val type = spanObject.optString("type", "")

                                    // 确保范围有效
                                    if (start < 0 || end > layoutItem.textContent.length || start >= end || type.isEmpty()) {
                                        AppLog.w("【文本格式同步】跳过无效的Span: start=$start, end=$end, type=$type")
                                        continue
                                    }

                                    val span = when (type) {
                                        "style" -> android.text.style.StyleSpan(spanObject.optInt("style", android.graphics.Typeface.NORMAL))
                                        "fontSize" -> android.text.style.AbsoluteSizeSpan(spanObject.optInt("size", 13), true) // 🎯 强制使用sp单位
                                        "color" -> android.text.style.ForegroundColorSpan(spanObject.optInt("color", android.graphics.Color.BLACK))
                                        "stroke" -> com.example.castapp.utils.StrokeSpan(
                                            spanObject.optDouble("strokeWidth", 1.0).toFloat(),
                                            spanObject.optInt("strokeColor", android.graphics.Color.BLACK)
                                        )
                                        "letterSpacing" -> com.example.castapp.utils.LetterSpacingSpan(
                                            spanObject.optDouble("letterSpacing", 0.0).toFloat()
                                        )
                                        else -> {
                                            AppLog.w("【文本格式同步】未知的Span类型: $type")
                                            null
                                        }
                                    }

                                    span?.let {
                                        spannableString.setSpan(it, start, end, flags)
                                    }

                                } catch (e: Exception) {
                                    AppLog.w("【文本格式同步】跳过无效的Span对象: ${e.message}")
                                    continue
                                }
                            }

                            // 保存富文本格式到TextFormatManager
                            textFormatManager.saveRichTextFormat(layoutItem.deviceId, spannableString)
                            syncCount++
                            AppLog.d("【文本格式同步】成功同步富文本格式: ${layoutItem.deviceId} -> 内容=${layoutItem.textContent}, 数据大小=${layoutItem.richTextData.length}")

                        } catch (e: Exception) {
                            AppLog.e("【文本格式同步】富文本格式恢复失败，使用基本格式: ${layoutItem.deviceId}", e)
                            // 后备方案：使用基本格式
                            restoreBasicTextFormat(layoutItem, textFormatManager)
                            syncCount++
                        }
                    } else {
                        // 后备方案：恢复基本文本格式
                        restoreBasicTextFormat(layoutItem, textFormatManager)
                        syncCount++
                    }
                }
            }

            AppLog.d("【文本格式同步】文本格式同步完成，成功同步 $syncCount 个文本格式")

        } catch (e: Exception) {
            AppLog.e("【文本格式同步】同步文本格式信息到TextFormatManager失败", e)
        }
    }

    /**
     * 调整窗口层级
     * 使用 bringToFront() 方法安全地调整显示层级，不破坏 TextureView 的 Surface
     * 层级规则：列表上方的设备显示在最上方，列表下方的设备显示在最下方
     * 注意：由于列表已经按照实际层级排序（顶部=最上层），所以需要反向调整
     */
    fun adjustWindowLayers(orderedWindowList: List<CastWindowInfo>) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                AppLog.d("【层级调整】开始调整窗口层级，共 ${orderedWindowList.size} 个窗口")
                AppLog.d("【层级调整】列表顺序（从上到下，序号对应视觉层级）：")
                orderedWindowList.forEachIndexed { index, windowInfo ->
                    AppLog.d("【层级调整】  序号${index + 1}(最上层=1): ${windowInfo.getDisplayTextWithDevice()}")
                }

                // 反向遍历列表，从最后一个开始调用 bringToFront()
                // 由于列表顶部显示的是最上层窗口，我们需要反向调整
                // 列表下方的设备先调用 bringToFront()，显示在最下方
                // 列表上方的设备后调用 bringToFront()，显示在最上方
                AppLog.d("【层级调整】开始反向调整，确保序号1的窗口在最上层:")
                orderedWindowList.reversed().forEachIndexed { reverseIndex, windowInfo ->
                    val originalIndex = orderedWindowList.size - 1 - reverseIndex
                    val layerNumber = originalIndex + 1
                    val transformHandler = dataModule.getWindowMapping(windowInfo.connectionId)
                    if (transformHandler != null) {
                        transformHandler.bringToFront()
                        AppLog.d("【层级调整】调整序号${layerNumber}窗口: ${windowInfo.getDisplayTextWithDevice()}")
                    } else {
                        AppLog.w("【层级调整】未找到序号${layerNumber}窗口: ${windowInfo.connectionId}")
                    }
                }

                AppLog.d("【层级调整】窗口层级调整完成！序号1的窗口应该在最上层，序号越大越在下层")

                // 层级调整完成后，延迟刷新窗口管理对话框以更新层级数值显示
                Handler(Looper.getMainLooper()).postDelayed({
                    AppLog.d("【层级调整】延迟刷新开始，检查容器子视图顺序")

                    // 调试：检查容器中子视图的当前顺序
                    val container = dataModule.getSurfaceContainer()
                    if (container != null) {
                        AppLog.d("【层级调整】容器子视图数量: ${container.childCount}")
                        for (i in 0 until container.childCount) {
                            val childView = container.getChildAt(i)
                            val connectionId = dataModule.getAllWindowMappings().entries.find { it.value == childView }?.key
                            AppLog.d("【层级调整】容器索引$i -> 连接ID: $connectionId")
                        }
                    }

                    AppLog.d("【层级调整】开始刷新窗口管理对话框，更新层级数值显示")
                    dialogRefreshCallback?.invoke()
                }, 200) // 延迟200ms确保层级调整完全生效

            } catch (e: Exception) {
                AppLog.e("【层级调整】调整窗口层级失败", e)
            }
        }
    }
    

    
    /**
     * 调整布局窗口层级
     * 根据布局项的 orderIndex 调整窗口层级顺序
     */
    private fun adjustLayoutWindowLayers(layoutItems: List<com.example.castapp.database.entity.WindowLayoutItemEntity>) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                AppLog.d("【层级修复】开始调整布局窗口层级，共 ${layoutItems.size} 个布局项")

                // 过滤出已创建的窗口对应的布局项
                val existingLayoutItems = layoutItems.filter { layoutItem ->
                    dataModule.containsWindow(layoutItem.deviceId)
                }

                if (existingLayoutItems.isEmpty()) {
                    AppLog.w("【层级修复】没有找到已创建的布局窗口")
                    return@runOnUiThread
                }

                // 按照 orderIndex 排序（orderIndex 越小越在底层）
                val sortedLayoutItems = existingLayoutItems.sortedBy { it.orderIndex }

                // 正确转换层级：orderIndex -> zOrder
                // 公式：zOrder = totalWindows - orderIndex（因为orderIndex越小越在底层，zOrder越小越在底层）
                val totalWindows = layoutItems.size
                val orderedWindowList = sortedLayoutItems.mapIndexed { index, layoutItem ->
                    // 重新计算正确的显示层级
                    val displayZOrder = totalWindows - layoutItem.orderIndex

                    AppLog.d("【层级修复】窗口 ${layoutItem.deviceId}: orderIndex=${layoutItem.orderIndex} -> zOrder=$displayZOrder")

                    // 转换为 CastWindowInfo，使用正确的 zOrder
                    CastWindowInfo(
                        connectionId = layoutItem.deviceId,
                        ipAddress = layoutItem.ipAddress,
                        port = layoutItem.port,
                        isActive = true,
                        deviceName = layoutItem.deviceName,
                        note = layoutItem.note, // 🏷️ 添加备注
                        positionX = layoutItem.positionX,
                        positionY = layoutItem.positionY,
                        scaleFactor = layoutItem.scaleFactor,
                        rotationAngle = layoutItem.rotationAngle,
                        zOrder = displayZOrder, // 使用正确计算的显示层级
                        isCropping = false,
                        cropRectRatio = null, // 层级调整时不需要裁剪信息
                        isDragEnabled = layoutItem.isDragEnabled,
                        isScaleEnabled = layoutItem.isScaleEnabled,
                        isRotationEnabled = layoutItem.isRotationEnabled,
                        isVisible = layoutItem.isVisible,
                        isMirrored = layoutItem.isMirrored,
                        cornerRadius = layoutItem.cornerRadius,
                        alpha = layoutItem.alpha,
                        isControlEnabled = layoutItem.isControlEnabled,
                        isEditEnabled = false, // 📝 布局恢复时默认不启用编辑功能
                        baseWindowWidth = layoutItem.baseWindowWidth,
                        baseWindowHeight = layoutItem.baseWindowHeight
                    )
                }

                // 反转列表，因为 adjustWindowLayers 期望顶部是最上层
                val reversedWindowList = orderedWindowList.reversed()

                AppLog.d("【层级修复】准备调整层级，窗口顺序（从上到下）:")
                reversedWindowList.forEachIndexed { index, windowInfo ->
                    AppLog.d("【层级修复】  序号${index + 1}: ${windowInfo.getDisplayTextWithDevice()} (zOrder: ${windowInfo.zOrder})")
                }

                // 使用现有的层级调整方法
                adjustWindowLayers(reversedWindowList)

                AppLog.d("【层级修复】布局窗口层级调整完成")

            } catch (e: Exception) {
                AppLog.e("【层级修复】调整布局窗口层级失败", e)
            }
        }
    }



    /**
     * 应用布局项到现有窗口（直接使用布局项列表）
     */
    fun applyLayoutItemsToExistingWindows(layoutItems: List<com.example.castapp.database.entity.WindowLayoutItemEntity>) {
        val activity = dataModule.getCurrentActivity() ?: return

        activity.runOnUiThread {
            try {
                AppLog.d("【布局恢复】开始应用布局项到现有窗口，共 ${layoutItems.size} 个布局项")

                // 🏷️ 同步备注信息到SharedPreferences
                syncNotesToSharedPreferences(layoutItems, activity)

                // 📝 同步文本格式信息到TextFormatManager
                syncTextFormatsToSharedPreferences(layoutItems, activity)

                // 遍历布局项，应用到对应的现有窗口或创建新窗口
                layoutItems.forEach { layoutItem ->
                    val transformHandler = dataModule.getWindowMapping(layoutItem.deviceId)
                    if (transformHandler != null) {
                        AppLog.d("【布局恢复】找到现有窗口，开始应用布局参数: ${layoutItem.deviceId}")

                        // 如果有裁剪参数，先隐藏窗口避免闪烁
                        val hasCropParameters = layoutItem.cropRect != null
                        if (hasCropParameters) {
                            transformHandler.alpha = 0f
                            AppLog.d("【布局恢复】检测到裁剪参数，先隐藏窗口避免闪烁")
                        }

                        // 应用布局参数（现在包含裁剪状态处理和双重偏移修复）
                        applyLayoutParametersToExistingWindow(transformHandler, layoutItem)

                        // 🎯 双重偏移修复：不再需要延迟应用裁剪参数，因为已经在applyLayoutParametersToExistingWindow中处理
                        // 恢复窗口显示
                        if (hasCropParameters) {
                            transformHandler.alpha = layoutItem.alpha
                            AppLog.d("【布局恢复】布局参数应用完成，恢复窗口显示，透明度=${layoutItem.alpha}")
                        }
                    } else {
                        // 📝 未找到现有窗口，检查是否需要创建新窗口
                        if (layoutItem.deviceId.startsWith("text_")) {
                            AppLog.d("【布局恢复】未找到文本窗口，开始创建: ${layoutItem.deviceId}")
                            createMissingTextWindow(layoutItem)
                        } else if (layoutItem.deviceId.startsWith("video_") || layoutItem.deviceId.startsWith("image_")) {
                            AppLog.d("【布局恢复】未找到媒体窗口，开始创建: ${layoutItem.deviceId}")
                            createMissingMediaWindow(layoutItem)
                        } else {
                            AppLog.w("【布局恢复】未找到对应的现有窗口: ${layoutItem.deviceId}")
                        }
                    }
                }

                // 延迟检查并调整布局窗口层级
                Handler(Looper.getMainLooper()).postDelayed({
                    adjustLayoutWindowLayers(layoutItems)
                }, 300) // 延迟300ms确保所有参数应用完成

                // 🏷️ 延迟刷新对话框以显示同步后的备注信息
                Handler(Looper.getMainLooper()).postDelayed({
                    dialogRefreshCallback?.invoke()
                    AppLog.d("【备注同步】已刷新对话框以显示同步后的备注信息")
                }, 500) // 延迟500ms确保层级调整和备注同步都完成

                AppLog.d("【布局恢复】布局项应用到现有窗口完成")
            } catch (e: Exception) {
                AppLog.e("【布局恢复】应用布局项到现有窗口失败", e)
            }
        }
    }

    /**
     * 应用布局参数到现有窗口
     */
    private fun applyLayoutParametersToExistingWindow(
        transformHandler: com.example.castapp.ui.windowsettings.TransformHandler,
        layoutItem: com.example.castapp.database.entity.WindowLayoutItemEntity
    ) {
        try {
            // 如果有裁剪参数，先隐藏窗口避免闪烁
            val hasCropParameters = layoutItem.cropRect != null
            if (hasCropParameters) {
                transformHandler.alpha = 0f
                AppLog.d("【布局恢复】检测到裁剪参数，先隐藏窗口避免闪烁")
            }

            // 🎯 双重偏移修复：先处理裁剪状态，确保setPrecisionTransform在正确的裁剪状态下执行
            if (hasCropParameters) {
                // 先应用裁剪参数，确保裁剪状态正确
                applyCropParametersToExistingWindow(transformHandler, layoutItem)
                AppLog.d("【布局恢复】先应用裁剪参数，确保裁剪状态正确: ${layoutItem.deviceId}")
            } else {
                // 布局没有裁剪参数，需要清除现有的裁剪状态
                transformHandler.setCropRectRatio(null)
                AppLog.d("【布局恢复】清除裁剪状态: ${layoutItem.deviceId}")
            }

            // 🎯 镜像修复：在setPrecisionTransform之前设置镜像状态，避免被覆盖
            transformHandler.setMirrorEnabled(layoutItem.isMirrored)
            AppLog.d("【布局恢复】镜像状态设置: ${layoutItem.deviceId}, isMirrored=${layoutItem.isMirrored}")

            // 🎯 横屏状态恢复：如果是投屏窗口，则恢复横屏状态（无论开启还是关闭都需要恢复）
            if (isRealCastWindow(layoutItem.deviceId)) {
                restoreLandscapeModeState(layoutItem.deviceId, layoutItem.isLandscapeModeEnabled)
                AppLog.d("【布局恢复】横屏状态设置: ${layoutItem.deviceId}, isLandscapeModeEnabled=${layoutItem.isLandscapeModeEnabled}")
            }

            // 修复：使用精准变换方法，正确处理位置设置
            // 现在裁剪状态和镜像状态都已正确，setPrecisionTransform可以正确处理双重偏移问题
            transformHandler.setPrecisionTransform(
                x = layoutItem.positionX,
                y = layoutItem.positionY,
                scale = layoutItem.scaleFactor,
                rotation = layoutItem.rotationAngle
            )

            // 应用可见性
            AppLog.d("【布局恢复】窗口可见性设置: ${layoutItem.deviceId}, isVisible=${layoutItem.isVisible}")
            transformHandler.visibility = if (layoutItem.isVisible) android.view.View.VISIBLE else android.view.View.GONE

            // 应用视觉效果参数
            transformHandler.setCornerRadius(layoutItem.cornerRadius)

            // 恢复边框相关状态
            transformHandler.setBorderEnabled(layoutItem.isBorderEnabled)
            transformHandler.setBorderColor(layoutItem.borderColor)
            transformHandler.setBorderWidth(layoutItem.borderWidth)

            // 设置透明度（现在可以直接设置，不需要延迟）
            transformHandler.setWindowAlpha(layoutItem.alpha)

            // 📝 如果是文本窗口，处理尺寸更新、刷新文本格式显示和窗口颜色
            if (layoutItem.deviceId.startsWith("text_")) {
                // 🎯 关键修复：更新文本窗口的基础尺寸
                if (layoutItem.baseWindowWidth > 0 && layoutItem.baseWindowHeight > 0) {
                    // 🎯 使用 TextWindowManager 的新方法来正确设置文本窗口尺寸
                    val textWindowManager = transformHandler.getTextWindowManager()
                    if (textWindowManager != null) {
                        textWindowManager.setTextWindowSize(layoutItem.baseWindowWidth, layoutItem.baseWindowHeight)
                        AppLog.d("【布局恢复】通过TextWindowManager设置文本窗口尺寸: ${layoutItem.deviceId}, 尺寸=${layoutItem.baseWindowWidth}x${layoutItem.baseWindowHeight}")
                    } else {
                        // 后备方案：直接更新 TransformHandler 尺寸
                        transformHandler.updateBaseWindowSize(layoutItem.baseWindowWidth, layoutItem.baseWindowHeight)
                        val layoutParams = transformHandler.layoutParams
                        layoutParams.width = layoutItem.baseWindowWidth
                        layoutParams.height = layoutItem.baseWindowHeight
                        transformHandler.layoutParams = layoutParams

                        // 同步更新TextSizeManager中的尺寸记录
                        val activity = dataModule.getCurrentActivity()
                        if (activity != null) {
                            val textSizeManager = com.example.castapp.utils.TextSizeManager(activity)
                            textSizeManager.saveTextWindowSize(layoutItem.deviceId, layoutItem.baseWindowWidth, layoutItem.baseWindowHeight)
                        }
                        AppLog.d("【布局恢复】使用后备方案设置文本窗口尺寸: ${layoutItem.deviceId}, 尺寸=${layoutItem.baseWindowWidth}x${layoutItem.baseWindowHeight}")
                    }
                }

                refreshTextWindowFormat(transformHandler, layoutItem)
                refreshTextWindowColor(transformHandler, layoutItem)
            }

            // 🎬 如果是视频窗口，恢复播放状态
            if (layoutItem.deviceId.startsWith("video_")) {
                restoreVideoPlaybackState(transformHandler, layoutItem)
            }

            AppLog.d("【布局恢复】精准参数应用完成: ${layoutItem.deviceId}, 最终位置(${transformHandler.getActualDisplayX()}, ${transformHandler.getActualDisplayY()})")

        } catch (e: Exception) {
            AppLog.e("【布局恢复】应用布局参数失败: ${layoutItem.deviceId}", e)
        }
    }

    /**
     * 应用裁剪参数到现有窗口
     */
    private fun applyCropParametersToExistingWindow(
        transformHandler: com.example.castapp.ui.windowsettings.TransformHandler,
        layoutItem: com.example.castapp.database.entity.WindowLayoutItemEntity
    ) {
        try {
            layoutItem.cropRect?.let { cropRectJson ->
                try {
                    // 解析裁剪区域JSON
                    val left = cropRectJson.substringAfter("\"left\":").substringBefore(",").toFloat()
                    val top = cropRectJson.substringAfter("\"top\":").substringBefore(",").toFloat()
                    val right = cropRectJson.substringAfter("\"right\":").substringBefore(",").toFloat()
                    val bottom = cropRectJson.substringAfter("\"bottom\":").substringBefore("}").toFloat()
                    val cropRect = android.graphics.RectF(left, top, right, bottom)

                    // 应用裁剪参数到现有窗口
                    transformHandler.setCropRectRatio(cropRect)
                    AppLog.d("【布局恢复】应用裁剪区域成功: $cropRectJson")
                } catch (e: Exception) {
                    AppLog.w("【布局恢复】解析裁剪区域失败: $cropRectJson", e)
                }
            }
        } catch (e: Exception) {
            AppLog.e("【布局恢复】应用裁剪参数失败", e)
        }
    }

    /**
     * 🎬 恢复视频播放状态
     */
    private fun restoreVideoPlaybackState(
        transformHandler: com.example.castapp.ui.windowsettings.TransformHandler,
        layoutItem: com.example.castapp.database.entity.WindowLayoutItemEntity
    ) {
        try {
            val connectionId = layoutItem.deviceId
            val activity = dataModule.getCurrentActivity() ?: return

            // 获取MediaSurfaceManager
            val mediaSurfaceManager = transformHandler.getMediaSurfaceManager()
            if (mediaSurfaceManager != null) {
                // 恢复播放状态到MediaSurfaceManager
                mediaSurfaceManager.setPlayEnabled(layoutItem.videoPlayEnabled)
                mediaSurfaceManager.setLoopCount(layoutItem.videoLoopCount)
                mediaSurfaceManager.setVolume(layoutItem.videoVolume)

                // 同时保存到SharedPreferences，确保UI显示一致
                val prefs = activity.getSharedPreferences("video_settings", android.content.Context.MODE_PRIVATE)
                prefs.edit().apply {
                    putBoolean("${connectionId}_play_enabled", layoutItem.videoPlayEnabled)
                    putInt("${connectionId}_loop_count", layoutItem.videoLoopCount)
                    putInt("${connectionId}_volume", layoutItem.videoVolume)
                    apply()
                }

                AppLog.d("【布局恢复】视频播放状态已恢复: $connectionId, 播放=${layoutItem.videoPlayEnabled}, 次数=${layoutItem.videoLoopCount}, 音量=${layoutItem.videoVolume}")
            } else {
                AppLog.w("【布局恢复】未找到MediaSurfaceManager，无法恢复视频播放状态: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("【布局恢复】恢复视频播放状态失败: ${layoutItem.deviceId}", e)
        }
    }

    /**
     * 📝 刷新文本窗口格式显示
     * 在布局恢复后，刷新文本窗口的格式显示以应用新的格式信息
     */
    private fun refreshTextWindowFormat(
        transformHandler: com.example.castapp.ui.windowsettings.TransformHandler,
        layoutItem: com.example.castapp.database.entity.WindowLayoutItemEntity
    ) {
        try {
            // 获取文本窗口管理器
            val textWindowManager = transformHandler.getTextWindowManager()
            if (textWindowManager != null) {
                // 📝 优先恢复扩展格式状态
                textWindowManager.restoreExtendedFormatState(
                    textContent = layoutItem.textContent,
                    isBold = layoutItem.textIsBold,
                    isItalic = layoutItem.textIsItalic,
                    fontSize = layoutItem.textFontSize,
                    fontName = layoutItem.textFontName,
                    lineSpacing = layoutItem.textLineSpacing,
                    textAlignment = layoutItem.textAlignment,
                    richTextData = layoutItem.richTextData
                )

                // 刷新文本窗口的格式显示
                textWindowManager.refreshFormatDisplay()
                AppLog.d("【文本格式刷新】文本窗口扩展格式已恢复并刷新: ${layoutItem.deviceId}")
            } else {
                // 后备方案：直接同步到TextFormatManager
                val activity = dataModule.getCurrentActivity()
                if (activity != null) {
                    val textFormatManager = com.example.castapp.utils.TextFormatManager(activity)
                    restoreExtendedTextFormat(layoutItem, textFormatManager)
                    AppLog.d("【文本格式刷新】使用后备方案恢复扩展格式: ${layoutItem.deviceId}")
                } else {
                    AppLog.w("【文本格式刷新】未找到文本窗口管理器和Activity: ${layoutItem.deviceId}")
                }
            }
        } catch (e: Exception) {
            AppLog.e("【文本格式刷新】刷新文本窗口格式失败: ${layoutItem.deviceId}", e)
        }
    }

    /**
     * 📝 恢复扩展文本格式（包含完整的字体格式信息）
     */
    private fun restoreExtendedTextFormat(
        layoutItem: com.example.castapp.database.entity.WindowLayoutItemEntity,
        textFormatManager: com.example.castapp.utils.TextFormatManager
    ) {
        val textContent = layoutItem.textContent ?: "默认文字"
        val isBold = layoutItem.textIsBold
        val isItalic = layoutItem.textIsItalic
        val fontSize = layoutItem.textFontSize
        val fontName = layoutItem.textFontName
        val fontFamily = layoutItem.textFontFamily
        val lineSpacing = layoutItem.textLineSpacing
        val textAlignment = layoutItem.textAlignment

        // 保存扩展格式到TextFormatManager
        textFormatManager.saveExtendedTextFormat(
            textId = layoutItem.deviceId,
            textContent = textContent,
            isBold = isBold,
            isItalic = isItalic,
            fontSize = fontSize,
            fontName = fontName,
            fontFamily = fontFamily,
            lineSpacing = lineSpacing,
            textAlignment = textAlignment
        )

        AppLog.d("【文本格式同步】成功同步扩展文本格式: ${layoutItem.deviceId} -> 字体=$fontName, 行间距=${lineSpacing}dp, 对齐=$textAlignment")
    }

    /**
     * 📝 恢复基本文本格式（后备方案）
     */
    private fun restoreBasicTextFormat(
        layoutItem: com.example.castapp.database.entity.WindowLayoutItemEntity,
        textFormatManager: com.example.castapp.utils.TextFormatManager
    ) {
        val textContent = layoutItem.textContent ?: "默认文字"
        val isBold = layoutItem.textIsBold
        val isItalic = layoutItem.textIsItalic
        val fontSize = layoutItem.textFontSize

        // 保存到TextFormatManager
        textFormatManager.saveTextFormat(
            textId = layoutItem.deviceId,
            textContent = textContent,
            isBold = isBold,
            isItalic = isItalic,
            fontSize = fontSize
        )

        AppLog.d("【文本格式同步】成功同步基本文本格式: ${layoutItem.deviceId} -> 内容=$textContent, 加粗=$isBold, 倾斜=$isItalic, 字号=${fontSize}sp")
    }

    /**
     * 🎨 刷新文本窗口背景颜色
     * 在布局恢复后，恢复文本窗口的背景颜色设置
     */
    private fun refreshTextWindowColor(
        transformHandler: com.example.castapp.ui.windowsettings.TransformHandler,
        layoutItem: com.example.castapp.database.entity.WindowLayoutItemEntity
    ) {
        try {
            // 获取文本窗口管理器
            val textWindowManager = transformHandler.getTextWindowManager()
            val textWindowView = textWindowManager?.getTextWindowView()

            if (textWindowView != null) {
                // 恢复窗口背景颜色设置
                textWindowView.setWindowBackgroundColor(
                    layoutItem.windowColorEnabled,
                    layoutItem.windowBackgroundColor
                )
                AppLog.d("【窗口颜色刷新】文本窗口背景颜色已恢复: ${layoutItem.deviceId}, 启用=${layoutItem.windowColorEnabled}, 颜色=${String.format("#%08X", layoutItem.windowBackgroundColor)}")
            } else {
                AppLog.w("【窗口颜色刷新】未找到文本窗口视图: ${layoutItem.deviceId}")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口颜色刷新】刷新文本窗口背景颜色失败: ${layoutItem.deviceId}", e)
        }
    }

    /**
     * 📁 创建缺失的媒体窗口
     * 当布局恢复时发现缺失媒体窗口时，根据布局项信息创建新的媒体窗口
     */
    private fun createMissingMediaWindow(layoutItem: com.example.castapp.database.entity.WindowLayoutItemEntity) {
        try {
            val activity = dataModule.getCurrentActivity() ?: return

            // 检查是否有媒体文件信息，优先使用数据库信息，然后尝试从MediaFileManager获取
            val mediaFileUri: String
            val mediaFileName: String
            val mediaContentType: String

            // 如果数据库中没有信息，尝试从MediaFileManager获取
            if (layoutItem.mediaFileUri.isNullOrBlank() || layoutItem.mediaFileName.isNullOrBlank() || layoutItem.mediaContentType.isNullOrBlank()) {
                AppLog.d("【布局恢复】数据库中缺少媒体文件信息，尝试从MediaFileManager获取: ${layoutItem.deviceId}")
                val mediaFileManager = com.example.castapp.utils.MediaFileManager(activity)
                val mediaFileInfo = mediaFileManager.getMediaFileInfo(layoutItem.deviceId)

                if (mediaFileInfo != null) {
                    mediaFileUri = mediaFileInfo.uri
                    mediaFileName = mediaFileInfo.fileName
                    mediaContentType = mediaFileInfo.contentType
                    AppLog.d("【布局恢复】从MediaFileManager获取到媒体文件信息: ${layoutItem.deviceId}")
                } else {
                    AppLog.w("【布局恢复】媒体窗口缺少必要的文件信息，无法重新创建: ${layoutItem.deviceId}")
                    return
                }
            } else {
                // 使用数据库中的信息
                mediaFileUri = layoutItem.mediaFileUri
                mediaFileName = layoutItem.mediaFileName
                mediaContentType = layoutItem.mediaContentType
            }

            // 此时已确保变量不为null，可以安全使用
            val mediaId = layoutItem.deviceId
            val fileName = mediaFileName
            val uri = mediaFileUri.toUri()
            val contentType = mediaContentType
            val mediaType = if (contentType == "video") "视频" else "图片"

            AppLog.d("【布局恢复】开始创建缺失的媒体窗口: $mediaId, 文件: $fileName, 类型: $contentType")
            AppLog.d("【布局恢复】媒体文件URI: $mediaFileUri")

            // 📁 恢复媒体文件信息到MediaFileManager
            val mediaFileManager = com.example.castapp.utils.MediaFileManager(activity)
            mediaFileManager.saveMediaFileInfo(mediaId, fileName, uri, contentType, mediaType)

            // 🏷️ 恢复备注信息到SharedPreferences
            if (!layoutItem.note.isNullOrBlank() && layoutItem.note != "无") {
                val noteManager = com.example.castapp.utils.NoteManager(activity)
                noteManager.saveNote(mediaId, layoutItem.note)
                AppLog.d("【布局恢复】媒体窗口备注信息已恢复: $mediaId -> ${layoutItem.note}")
            }

            // 📁 直接使用布局参数创建媒体窗口，避免闪烁
            val container = dataModule.getSurfaceContainer() ?: return
            val windowCreationModule = WindowCreationModule(dataModule)

            // 计算媒体窗口尺寸
            val (windowWidth, windowHeight) = windowCreationModule.calculateMediaWindowSize(uri, contentType, activity)

            // 直接调用WindowCreationModule的方法，使用布局参数创建窗口
            windowCreationModule.createMediaWindowWithLayoutParameters(
                mediaId, mediaType, fileName, uri, contentType,
                windowWidth, windowHeight, activity, container, layoutItem
            )

            AppLog.d("【布局恢复】缺失的媒体窗口创建完成（无闪烁）: $mediaId")

        } catch (e: Exception) {
            AppLog.e("【布局恢复】创建缺失的媒体窗口失败: ${layoutItem.deviceId}", e)
        }
    }

    // 📁 applyLayoutParametersToNewlyCreatedWindow 方法已移除
    // 现在直接使用 createMediaWindowWithLayoutParameters 创建窗口，无需延迟应用参数

    /**
     * 📝 创建缺失的文本窗口
     * 当布局恢复时发现缺失文本窗口时，根据布局项信息创建新的文本窗口
     */
    private fun createMissingTextWindow(layoutItem: com.example.castapp.database.entity.WindowLayoutItemEntity) {
        try {
            val activity = dataModule.getCurrentActivity() ?: return
            val container = dataModule.getSurfaceContainer() ?: return

            // 获取文本内容，如果为空则使用默认值
            val textContent = layoutItem.textContent ?: "默认文字"
            val textId = layoutItem.deviceId

            AppLog.d("【布局恢复】开始创建缺失的文本窗口: $textId, 内容: $textContent")

            // 📝 先恢复文本格式信息到TextFormatManager（使用统一的格式同步方法）
            restoreBasicTextFormat(layoutItem, com.example.castapp.utils.TextFormatManager(activity))

            // 🎨 计算窗口尺寸（基于文本内容和格式）
            val windowWidth = if (layoutItem.baseWindowWidth > 0) layoutItem.baseWindowWidth else 300
            val windowHeight = if (layoutItem.baseWindowHeight > 0) layoutItem.baseWindowHeight else 200

            // 创建TransformHandler
            val transformHandler = com.example.castapp.ui.windowsettings.TransformHandler(
                activity,
                initialWidth = windowWidth,
                initialHeight = windowHeight
            )

            // 📝 关键修复：使用正确的方法设置文本连接，这会自动创建TextWindowManager
            transformHandler.setupForTextConnection(textId, textContent)

            // 🎯 关键修复：确保TransformHandler的基础尺寸与布局恢复尺寸一致
            transformHandler.updateBaseWindowSize(windowWidth, windowHeight)

            // 设置设备信息
            transformHandler.setDeviceInfo(
                deviceName = textContent,
                ipAddress = "本地设备",
                port = 0
            )

            // 注册窗口映射
            dataModule.addWindowMapping(textId, transformHandler)

            // 添加到容器
            container.addView(transformHandler)

            // 📝 设置初始位置（在添加到容器后设置，确保布局完成）
            transformHandler.post {
                transformHandler.x = layoutItem.positionX
                transformHandler.y = layoutItem.positionY
                AppLog.d("【布局恢复】文本窗口初始位置已设置: (${layoutItem.positionX}, ${layoutItem.positionY})")
            }

            // 应用完整的布局参数
            applyLayoutParametersToExistingWindow(transformHandler, layoutItem)

            AppLog.d("【布局恢复】文本窗口创建完成: $textId")

        } catch (e: Exception) {
            AppLog.e("【布局恢复】创建缺失文本窗口失败: ${layoutItem.deviceId}", e)
        }
    }

    // 📝 移除重复的文本格式恢复方法（已统一使用restoreBasicTextFormat方法）

    /**
     * 🎯 判断是否为真实的投屏窗口（非本地窗口）
     */
    private fun isRealCastWindow(connectionId: String): Boolean {
        return !connectionId.let { id ->
            id == "front_camera" || id == "rear_camera" ||
            id.startsWith("video_") || id.startsWith("image_") ||
            id.startsWith("text_")
        }
    }

    /**
     * 🎯 恢复横屏模式状态
     * 通过WindowSettingsManager发送横屏控制消息
     */
    private fun restoreLandscapeModeState(connectionId: String, isEnabled: Boolean) {
        try {
            // 通过WindowSettingsManager的横屏开关处理机制恢复状态
            val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()

            // 模拟横屏开关变化，触发相应的控制消息发送
            windowSettingsManager.handleLandscapeModeSwitch(connectionId, isEnabled)

            AppLog.d("【布局恢复】横屏模式状态已恢复: $connectionId -> $isEnabled")

        } catch (e: Exception) {
            AppLog.e("【布局恢复】恢复横屏模式状态失败: $connectionId", e)
        }
    }
}
