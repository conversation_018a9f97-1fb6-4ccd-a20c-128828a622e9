package com.example.castapp.ui.dialog;

/**
 * 远程接收端控制对话框
 * 用于控制远程接收端设备
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00b6\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0015\u0018\u0000 j2\u00020\u0001:\u0001jB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u001a\u0012\u0004\u0012\u00020\u001a0\u0019H\u0002J\b\u0010\u001b\u001a\u00020\u001cH\u0002J\b\u0010\u001d\u001a\u00020\u001cH\u0002J\u0016\u0010\u001e\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"J\b\u0010#\u001a\u00020$H\u0002J\b\u0010%\u001a\u00020$H\u0002J\u0010\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020\'H\u0002J\b\u0010)\u001a\u00020\u001cH\u0002J\b\u0010*\u001a\u00020\u001cH\u0002J\u0018\u0010+\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0002J\b\u0010,\u001a\u00020\u001cH\u0002J\u0018\u0010-\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010.\u001a\u00020/H\u0002J\b\u00100\u001a\u00020\u001cH\u0002J\b\u00101\u001a\u00020\u001cH\u0002J\u000e\u00102\u001a\u00020\u001c2\u0006\u00103\u001a\u00020 J\u000e\u00104\u001a\u00020\u001c2\u0006\u00105\u001a\u000206J\b\u00107\u001a\u00020\u001cH\u0002J\b\u00108\u001a\u00020\u001cH\u0002J\u0010\u00109\u001a\u00020\u001c2\u0006\u0010:\u001a\u00020;H\u0002J\b\u0010<\u001a\u00020\"H\u0002J\u000e\u0010=\u001a\u00020\u001c2\u0006\u0010>\u001a\u00020\u0003J\u0012\u0010?\u001a\u00020@2\b\u0010A\u001a\u0004\u0018\u00010BH\u0016J&\u0010C\u001a\u0004\u0018\u00010;2\u0006\u0010D\u001a\u00020E2\b\u0010F\u001a\u0004\u0018\u00010G2\b\u0010A\u001a\u0004\u0018\u00010BH\u0016J\b\u0010H\u001a\u00020\u001cH\u0016J\b\u0010I\u001a\u00020\u001cH\u0016J\u001a\u0010J\u001a\u00020\u001c2\u0006\u0010:\u001a\u00020;2\b\u0010A\u001a\u0004\u0018\u00010BH\u0016J\b\u0010K\u001a\u00020\u001cH\u0002J\b\u0010L\u001a\u00020\u001cH\u0002J\b\u0010M\u001a\u00020\u001cH\u0002J\b\u0010N\u001a\u00020\u001cH\u0002J\b\u0010O\u001a\u00020\u001cH\u0002J$\u0010P\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 2\u0012\u0010Q\u001a\u000e\u0012\u0004\u0012\u00020 \u0012\u0004\u0012\u00020S0RH\u0002J\u0018\u0010T\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010U\u001a\u00020VH\u0002J \u0010W\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010X\u001a\u00020\'2\u0006\u0010Y\u001a\u00020\'H\u0002J(\u0010Z\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010[\u001a\u00020\'2\u0006\u0010X\u001a\u00020\'2\u0006\u0010Y\u001a\u00020\'H\u0002J(\u0010\\\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010]\u001a\u00020\'2\u0006\u0010X\u001a\u00020\'2\u0006\u0010Y\u001a\u00020\'H\u0002J\b\u0010^\u001a\u00020\u001cH\u0002J\b\u0010_\u001a\u00020\u001cH\u0002J\b\u0010`\u001a\u00020\u001cH\u0002J\u0018\u0010a\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010b\u001a\u00020\'H\u0002J\"\u0010c\u001a\u00020\u001c2\u0018\u0010d\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020 \u0012\u0004\u0012\u00020S0R0\bH\u0002J\u0010\u0010e\u001a\u00020\u001c2\u0006\u0010f\u001a\u00020\"H\u0002JD\u0010g\u001a\u00020\u001c2\f\u0010h\u001a\b\u0012\u0004\u0012\u00020\t0\b2.\b\u0002\u0010i\u001a(\u0012\u0004\u0012\u00020 \u0012\u001c\u0012\u001a\u0012\u0004\u0012\u00020 \u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020 \u0012\u0004\u0012\u00020S0R0\u0019\u0018\u00010RR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006k"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteReceiverControlDialog;", "Landroidx/fragment/app/DialogFragment;", "remoteReceiverConnection", "Lcom/example/castapp/model/RemoteReceiverConnection;", "(Lcom/example/castapp/model/RemoteReceiverConnection;)V", "broadcastButton", "Landroid/widget/Button;", "cachedWindowInfoList", "", "Lcom/example/castapp/model/CastWindowInfo;", "clearScreenButton", "closeButton", "Landroid/widget/ImageButton;", "dialogTitle", "Landroid/widget/TextView;", "receiveButton", "remoteControlScale", "", "saveButton", "screenshotButton", "updateButton", "windowButton", "windowVisualizationView", "Lcom/example/castapp/ui/view/WindowContainerVisualizationView;", "calculateAdaptiveDialogSize", "Lkotlin/Pair;", "", "calculateRemoteControlScale", "", "clearWindowVisualization", "controlVisualizationEditMode", "connectionId", "", "isEnabled", "", "createTemporaryUpdateHandler", "Lcom/example/castapp/ui/dialog/RemoteWindowManagerDialog;", "createTemporaryWindowManagerHandler", "dpToPx", "", "dp", "handleBroadcastClick", "handleClearScreenClick", "handleCropModeControl", "handleReceiveClick", "handleRemoteEditModeExit", "containerView", "Lcom/example/castapp/ui/view/WindowVisualizationContainerView;", "handleSaveClick", "handleScreenshotClick", "handleScreenshotError", "errorMessage", "handleScreenshotResponse", "message", "Lcom/example/castapp/websocket/ControlMessage;", "handleUpdateClick", "handleWindowClick", "initViews", "view", "Landroid/view/View;", "isSyncControlEnabled", "onConnectionStateChanged", "newReceiver", "onCreateDialog", "Landroid/app/Dialog;", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDestroy", "onStart", "onViewCreated", "recalculateRemoteControlScaleAfterLayout", "registerForConnectionStateUpdates", "requestInitialWindowInfoForVisualization", "requestScreenshotForVisualization", "requestWindowInfoForVisualization", "sendTextFormatDataToReceiver", "formatData", "", "", "sendWindowCropUpdate", "cropRatio", "Landroid/graphics/RectF;", "sendWindowPositionUpdate", "x", "y", "sendWindowRotationAndPositionUpdate", "rotationAngle", "sendWindowScaleAndPositionUpdate", "scaleFactor", "setupClickListeners", "setupUI", "setupWindowDragListener", "updateLocalVisualizationScaleFactor", "newScaleFactor", "updateScreenshotVisualization", "screenshotsData", "updateUIForConnectionState", "isConnected", "updateWindowVisualization", "windowInfoList", "textContentMap", "Companion", "app_debug"})
public final class RemoteReceiverControlDialog extends androidx.fragment.app.DialogFragment {
    @org.jetbrains.annotations.NotNull()
    private com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection;
    private android.widget.TextView dialogTitle;
    private android.widget.ImageButton closeButton;
    private android.widget.Button clearScreenButton;
    private android.widget.Button saveButton;
    private android.widget.Button broadcastButton;
    private android.widget.Button windowButton;
    private android.widget.Button receiveButton;
    private android.widget.Button screenshotButton;
    private android.widget.Button updateButton;
    private com.example.castapp.ui.view.WindowContainerVisualizationView windowVisualizationView;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.example.castapp.model.CastWindowInfo> cachedWindowInfoList;
    private double remoteControlScale = 1.0;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.ui.dialog.RemoteReceiverControlDialog.Companion Companion = null;
    
    public RemoteReceiverControlDialog(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.app.Dialog onCreateDialog(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    public void onStart() {
    }
    
    private final void initViews(android.view.View view) {
    }
    
    private final void setupClickListeners() {
    }
    
    private final void setupUI() {
    }
    
    private final void handleClearScreenClick() {
    }
    
    private final void handleSaveClick() {
    }
    
    private final void handleBroadcastClick() {
    }
    
    private final void handleWindowClick() {
    }
    
    /**
     * 🎯 处理裁剪模式控制
     * @param connectionId 窗口连接ID
     * @param isEnabled 是否启用裁剪模式
     */
    private final void handleCropModeControl(java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 🎯 更新本地可视化数据中的缩放因子
     * 确保下次缩放时基础值正确
     */
    private final void updateLocalVisualizationScaleFactor(java.lang.String connectionId, float newScaleFactor) {
    }
    
    private final void handleReceiveClick() {
    }
    
    private final void handleScreenshotClick() {
    }
    
    private final void handleUpdateClick() {
    }
    
    /**
     * 计算基于接收端屏幕分辨率的自适应对话框尺寸
     * 🚀 优化：基于接收端分辨率等比缩放，保持宽高比
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> calculateAdaptiveDialogSize() {
        return null;
    }
    
    /**
     * 🚀 优化：注册连接状态更新通知（被动监听，零功耗）
     */
    private final void registerForConnectionStateUpdates() {
    }
    
    /**
     * 🚀 被动接收连接状态更新（由父对话框主动调用）
     */
    public final void onConnectionStateChanged(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection newReceiver) {
    }
    
    /**
     * 更新UI以反映连接状态变化
     */
    private final void updateUIForConnectionState(boolean isConnected) {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    /**
     * 计算远程控制窗口的缩放比例
     * 🎯 根本修复：基于可视化容器的实际尺寸计算缩放比例
     */
    private final void calculateRemoteControlScale() {
    }
    
    /**
     * 🎯 新增：dp转px工具方法
     */
    private final float dpToPx(float dp) {
        return 0.0F;
    }
    
    /**
     * 🎯 新增：在可视化容器布局完成后重新计算精确的缩放比例
     */
    private final void recalculateRemoteControlScaleAfterLayout() {
    }
    
    /**
     * 🪟 请求初始窗口信息用于可视化（远程控制窗口创建时调用）
     */
    private final void requestInitialWindowInfoForVisualization() {
    }
    
    /**
     * 🪟 请求窗口信息用于可视化（点击窗口按钮时调用）
     */
    private final void requestWindowInfoForVisualization() {
    }
    
    /**
     * 更新窗口容器可视化
     */
    public final void updateWindowVisualization(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList, @org.jetbrains.annotations.Nullable()
    java.util.Map<java.lang.String, ? extends kotlin.Pair<java.lang.String, ? extends java.util.Map<java.lang.String, ? extends java.lang.Object>>> textContentMap) {
    }
    
    /**
     * 🪟 创建临时窗口管理处理器，用于接收初始窗口信息响应
     */
    private final com.example.castapp.ui.dialog.RemoteWindowManagerDialog createTemporaryWindowManagerHandler() {
        return null;
    }
    
    /**
     * 🔄 创建临时更新处理器，用于处理更新按钮的FULL模式窗口信息响应
     */
    private final com.example.castapp.ui.dialog.RemoteWindowManagerDialog createTemporaryUpdateHandler() {
        return null;
    }
    
    /**
     * 📸 请求截图用于可视化显示
     */
    private final void requestScreenshotForVisualization() {
    }
    
    /**
     * 📸 处理截图响应消息
     */
    public final void handleScreenshotResponse(@org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 📸 处理截图错误消息
     */
    public final void handleScreenshotError(@org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage) {
    }
    
    /**
     * 📸 更新截图可视化显示
     */
    private final void updateScreenshotVisualization(java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> screenshotsData) {
    }
    
    /**
     * 清除窗口容器可视化
     */
    private final void clearWindowVisualization() {
    }
    
    /**
     * 🎯 设置窗口拖动监听器
     */
    private final void setupWindowDragListener() {
    }
    
    /**
     * 🎯 检查同步开关状态
     */
    private final boolean isSyncControlEnabled() {
        return false;
    }
    
    /**
     * 🎯 发送窗口位置更新消息到接收端
     */
    private final void sendWindowPositionUpdate(java.lang.String connectionId, float x, float y) {
    }
    
    /**
     * 🎯 增强型同步：发送窗口缩放和位置组合更新消息到接收端
     */
    private final void sendWindowScaleAndPositionUpdate(java.lang.String connectionId, float scaleFactor, float x, float y) {
    }
    
    /**
     * 🎯 增强型同步：发送窗口旋转和位置组合更新消息到接收端
     */
    private final void sendWindowRotationAndPositionUpdate(java.lang.String connectionId, float rotationAngle, float x, float y) {
    }
    
    /**
     * 🎯 发送窗口裁剪更新消息到接收端
     */
    private final void sendWindowCropUpdate(java.lang.String connectionId, android.graphics.RectF cropRatio) {
    }
    
    /**
     * 🎯 控制可视化窗口的编辑模式
     */
    public final void controlVisualizationEditMode(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, boolean isEnabled) {
    }
    
    /**
     * 🎯 处理遥控端编辑模式退出
     */
    private final void handleRemoteEditModeExit(java.lang.String connectionId, com.example.castapp.ui.view.WindowVisualizationContainerView containerView) {
    }
    
    /**
     * 🎯 发送文本格式数据到接收端
     */
    private final void sendTextFormatDataToReceiver(java.lang.String connectionId, java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteReceiverControlDialog$Companion;", "", "()V", "newInstance", "Lcom/example/castapp/ui/dialog/RemoteReceiverControlDialog;", "receiver", "Lcom/example/castapp/model/RemoteReceiverConnection;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 创建新的远程接收端控制对话框实例
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.ui.dialog.RemoteReceiverControlDialog newInstance(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.RemoteReceiverConnection receiver) {
            return null;
        }
    }
}