<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="start"
    android:background="@drawable/crop_control_background"
    android:padding="4dp"
    android:elevation="1000dp">

    <!-- 设备信息显示 -->
    <TextView
        android:id="@+id/tv_device_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="设备名称(12345678)"
        android:textSize="10sp"
        android:textColor="#FFFFFF"
        android:padding="4dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginBottom="4dp"
        android:clickable="false"
        android:focusable="false"
        android:focusableInTouchMode="false" />

    <!-- 按钮容器 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <!-- 重置按钮 -->
        <Button
            android:id="@+id/btn_reset"
            android:layout_width="40dp"
            android:layout_height="22dp"
            android:text="重置"
            android:textSize="8sp"
            android:textColor="#FFFFFF"
            android:background="@drawable/crop_button_reset_background"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp" />

        <!-- 取消按钮 -->
        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="40dp"
            android:layout_height="22dp"
            android:text="取消"
            android:textSize="8sp"
            android:textColor="#FFFFFF"
            android:background="@drawable/crop_button_cancel_background"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp" />

        <!-- 应用按钮 -->
        <Button
            android:id="@+id/btn_apply"
            android:layout_width="40dp"
            android:layout_height="22dp"
            android:text="应用"
            android:textSize="8sp"
            android:textColor="#FFFFFF"
            android:background="@drawable/crop_button_apply_background"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp" />

    </LinearLayout>

</LinearLayout>
