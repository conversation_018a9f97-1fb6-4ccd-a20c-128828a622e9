package com.example.castapp.ui.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.example.castapp.R
import com.example.castapp.model.RemoteReceiverConnection

/**
 * 添加远程接收端对话框
 * 用于添加新的接收端设备
 */
class AddRemoteReceiverDeviceDialog(
    private val onReceiverAdded: (String, String) -> Unit
) : DialogFragment() {

    private lateinit var dialogTitle: TextView
    private lateinit var portInfoText: TextView
    private lateinit var deviceNameInput: EditText
    private lateinit var ipAddressInput: EditText
    private lateinit var confirmButton: Button
    private lateinit var cancelButton: Button

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_add_remote_device, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setupClickListeners()
        setupDialog()
    }

    private fun initViews(view: View) {
        dialogTitle = view.findViewById(R.id.dialog_title)
        portInfoText = view.findViewById(R.id.port_info_text)
        deviceNameInput = view.findViewById(R.id.device_name_input)
        ipAddressInput = view.findViewById(R.id.ip_address_input)
        confirmButton = view.findViewById(R.id.confirm_button)
        cancelButton = view.findViewById(R.id.cancel_button)

        // 设置接收端特定内容
        dialogTitle.text = "添加接收端设备"
        portInfoText.text = "接收端固定使用端口 7777"
    }

    private fun setupClickListeners() {
        confirmButton.setOnClickListener {
            handleAddReceiver()
        }
        
        cancelButton.setOnClickListener {
            dismiss()
        }
    }

    private fun handleAddReceiver() {
        val deviceName = deviceNameInput.text.toString().trim()
        val ipAddress = ipAddressInput.text.toString().trim()

        // 验证输入
        if (deviceName.isEmpty()) {
            Toast.makeText(requireContext(), "请输入设备名称", Toast.LENGTH_SHORT).show()
            return
        }

        if (ipAddress.isEmpty()) {
            Toast.makeText(requireContext(), "请输入IP地址", Toast.LENGTH_SHORT).show()
            return
        }

        if (!RemoteReceiverConnection.isValidIpAddress(ipAddress)) {
            Toast.makeText(requireContext(), "IP地址格式不正确", Toast.LENGTH_SHORT).show()
            return
        }

        if (!RemoteReceiverConnection.isValidDeviceName(deviceName)) {
            Toast.makeText(requireContext(), "设备名称长度应在1-50字符之间", Toast.LENGTH_SHORT).show()
            return
        }

        // 回调添加结果
        onReceiverAdded(ipAddress, deviceName)
        dismiss()
    }

    private fun setupDialog() {
        dialog?.window?.let { window ->
            val displayMetrics = resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val dialogWidth = (screenWidth * 0.9).toInt().coerceAtLeast((320 * displayMetrics.density).toInt())

            window.setLayout(
                dialogWidth,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        }
    }
}
