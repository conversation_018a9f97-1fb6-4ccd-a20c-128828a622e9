package com.example.castapp.ui.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.ui.adapter.LayerManagerAdapter
import com.example.castapp.utils.AppLog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

/**
 * 层级管理BottomSheet对话框
 * 专门用于调整投屏窗口的层级顺序
 */
class LayerManagerDialog(
    private val windowInfoProvider: () -> List<CastWindowInfo>,
    private val onWindowOrderChanged: ((List<CastWindowInfo>) -> Unit)? = null,
    private val onNoteChanged: ((String, String) -> Unit)? = null // 🏷️ 备注变更回调
) : BottomSheetDialogFragment() {

    private lateinit var rvLayerDevices: RecyclerView
    private lateinit var layoutEmptyState: View
    private lateinit var btnClose: ImageButton
    private lateinit var adapter: LayerManagerAdapter
    private lateinit var itemTouchHelper: ItemTouchHelper

    // 对话框关闭回调
    var onDialogDismissed: (() -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_layer_manager, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews(view)
        setupRecyclerView()
        setupClickListeners()
        refreshDeviceList()

        AppLog.d("【层级管理】对话框初始化完成")
    }

    /**
     * 初始化视图
     */
    private fun initViews(view: View) {
        rvLayerDevices = view.findViewById(R.id.rv_layer_devices)
        layoutEmptyState = view.findViewById(R.id.layout_empty_state)
        btnClose = view.findViewById(R.id.btn_close)
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = LayerManagerAdapter()
        rvLayerDevices.layoutManager = LinearLayoutManager(context)
        rvLayerDevices.adapter = adapter

        // 设置拖拽排序监听器
        adapter.setOnItemMoveListener(object : LayerManagerAdapter.OnItemMoveListener {
            override fun onItemMove(fromPosition: Int, toPosition: Int) {
                AppLog.d("【层级拖拽】窗口从位置 $fromPosition 移动到 $toPosition")
                // 获取当前排序后的列表
                val currentList = adapter.getCurrentWindowList()
                onWindowOrderChanged?.invoke(currentList)
            }
        })

        // 🏷️ 设置备注变更监听器
        adapter.setOnNoteChangeListener(object : LayerManagerAdapter.OnNoteChangeListener {
            override fun onNoteChanged(connectionId: String, note: String) {
                AppLog.d("【层级管理-备注】连接 $connectionId 备注变更为: $note")
                onNoteChanged?.invoke(connectionId, note)

                // 🔄 刷新设备列表以更新底层数据
                refreshDeviceList()
                AppLog.d("【层级管理-备注】已刷新设备列表以同步备注数据")
            }
        })

        // 设置ItemTouchHelper
        val callback = LayerManagerAdapter.ItemTouchHelperCallback(adapter)
        itemTouchHelper = ItemTouchHelper(callback)
        itemTouchHelper.attachToRecyclerView(rvLayerDevices)

        // 设置拖动手柄监听器
        adapter.registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
            override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
                setupDragHandles()
            }
            override fun onItemRangeChanged(positionStart: Int, itemCount: Int) {
                setupDragHandles()
            }
        })
    }

    /**
     * 设置拖动手柄
     */
    private fun setupDragHandles() {
        for (i in 0 until rvLayerDevices.childCount) {
            val viewHolder = rvLayerDevices.getChildViewHolder(rvLayerDevices.getChildAt(i))
            if (viewHolder is LayerManagerAdapter.LayerViewHolder) {
                viewHolder.dragHandle.setOnTouchListener { _, event ->
                    if (event.action == android.view.MotionEvent.ACTION_DOWN) {
                        itemTouchHelper.startDrag(viewHolder)
                    }
                    false
                }
            }
        }
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        btnClose.setOnClickListener {
            AppLog.d("【层级管理】用户点击关闭按钮")
            dismiss()
        }
    }

    /**
     * 刷新设备列表
     */
    fun refreshDeviceList() {
        try {
            val windowInfoList = windowInfoProvider()
            AppLog.d("【层级管理】刷新设备列表，共 ${windowInfoList.size} 个设备")

            // 更新适配器数据
            adapter.submitList(windowInfoList)

            // 显示/隐藏空状态
            if (windowInfoList.isEmpty()) {
                rvLayerDevices.visibility = View.GONE
                layoutEmptyState.visibility = View.VISIBLE
                AppLog.d("【层级管理】显示空状态")
            } else {
                rvLayerDevices.visibility = View.VISIBLE
                layoutEmptyState.visibility = View.GONE
                AppLog.d("【层级管理】显示设备列表")

                // 延迟设置拖动手柄，确保ViewHolder已创建
                rvLayerDevices.post {
                    setupDragHandles()
                }
            }

        } catch (e: Exception) {
            AppLog.e("【层级管理】刷新设备列表失败", e)
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDialogDismissed?.invoke()
        AppLog.d("【层级管理】对话框已关闭")
    }
}
