package com.example.castapp.manager

import android.annotation.SuppressLint
import android.content.Context
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import androidx.core.app.ActivityCompat
import android.Manifest
import android.content.pm.PackageManager
import com.example.castapp.utils.AppLog
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.ConcurrentHashMap

/**
 * 🔥 新增：麦克风管理器
 * 类似MediaProjectionManager，管理麦克风AudioRecord的生命周期
 * 在APP启动时预创建实例，避免后台录音权限问题
 */
class MicrophoneManager private constructor(private val context: Context) {

    companion object {
        // 音频参数
        const val SAMPLE_RATE = 48000
        const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT

        @Volatile
        @SuppressLint("StaticFieldLeak") // 使用ApplicationContext，安全持有
        private var INSTANCE: MicrophoneManager? = null

        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): MicrophoneManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MicrophoneManager(context.applicationContext).also {
                    INSTANCE = it
                }
            }
        }
    }

    // 麦克风AudioRecord实例（全局唯一）
    private var microphoneAudioRecord: AudioRecord? = null
    
    // 麦克风状态管理
    private var isMicrophoneReady = false
    private var bufferSize = 0
    
    // 使用状态管理
    private val isCapturing = AtomicBoolean(false)
    private var captureThread: Thread? = null
    private val dataCallbacks = ConcurrentHashMap<String, (ByteArray) -> Unit>()

    /**
     * 初始化麦克风管理器
     * 在APP启动时调用，预创建麦克风实例
     */
    fun initialize(): Boolean {
        return try {
            AppLog.d("【MicrophoneManager】开始初始化麦克风管理器")
            
            // 检查权限
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO)
                != PackageManager.PERMISSION_GRANTED) {
                AppLog.e("【MicrophoneManager】缺少录音权限，无法初始化")
                return false
            }

            // 获取缓冲区大小
            bufferSize = AudioRecord.getMinBufferSize(
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT
            )

            if (bufferSize == AudioRecord.ERROR || bufferSize == AudioRecord.ERROR_BAD_VALUE) {
                AppLog.e("【MicrophoneManager】无法获取麦克风缓冲区大小")
                return false
            }

            // 预创建AudioRecord实例
            createMicrophoneInstance()

        } catch (e: Exception) {
            AppLog.e("【MicrophoneManager】初始化失败", e)
            false
        }
    }

    /**
     * 创建麦克风AudioRecord实例
     */
    private fun createMicrophoneInstance(): Boolean {
        return try {
            // 清理现有实例
            cleanupMicrophoneInstance()

            AppLog.d("【MicrophoneManager】创建麦克风AudioRecord实例")
            
            microphoneAudioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT,
                bufferSize * 2 // 使用2倍缓冲区大小
            )

            if (microphoneAudioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                AppLog.e("【MicrophoneManager】麦克风AudioRecord初始化失败")
                cleanupMicrophoneInstance()
                return false
            }

            isMicrophoneReady = true
            AppLog.d("【MicrophoneManager】麦克风实例创建成功，状态: ${microphoneAudioRecord?.state}")
            true

        } catch (e: SecurityException) {
            AppLog.e("【MicrophoneManager】创建麦克风实例失败：权限被拒绝", e)
            false
        } catch (e: Exception) {
            AppLog.e("【MicrophoneManager】创建麦克风实例失败", e)
            false
        }
    }

    /**
     * 检查麦克风是否准备就绪
     */
    fun isMicrophoneReady(): Boolean {
        return isMicrophoneReady && microphoneAudioRecord?.state == AudioRecord.STATE_INITIALIZED
    }

    /**
     * 开始麦克风捕获
     * 使用预创建的AudioRecord实例
     */
    fun startCapture(connectionId: String, onDataCallback: (ByteArray) -> Unit): Boolean {
        return try {
            if (!isMicrophoneReady()) {
                AppLog.e("【MicrophoneManager】麦克风未准备就绪，无法开始捕获")
                return false
            }

            // 添加数据回调
            dataCallbacks[connectionId] = onDataCallback
            AppLog.d("【MicrophoneManager】添加数据回调: $connectionId")

            // 如果已经在捕获，直接返回成功
            if (isCapturing.get()) {
                AppLog.d("【MicrophoneManager】麦克风已在捕获中，复用现有捕获")
                return true
            }

            // 启动录音
            microphoneAudioRecord?.startRecording()
            val recordingState = microphoneAudioRecord?.recordingState
            
            if (recordingState != AudioRecord.RECORDSTATE_RECORDING) {
                AppLog.e("【MicrophoneManager】启动录音失败，状态: $recordingState")
                dataCallbacks.remove(connectionId)
                return false
            }

            // 启动捕获线程
            startCaptureThread()
            
            AppLog.d("【MicrophoneManager】麦克风捕获启动成功: $connectionId")
            true

        } catch (e: SecurityException) {
            AppLog.e("【MicrophoneManager】启动麦克风捕获失败：权限被拒绝", e)
            dataCallbacks.remove(connectionId)
            false
        } catch (e: Exception) {
            AppLog.e("【MicrophoneManager】启动麦克风捕获失败", e)
            dataCallbacks.remove(connectionId)
            false
        }
    }

    /**
     * 停止指定连接的麦克风捕获
     */
    fun stopCapture(connectionId: String) {
        dataCallbacks.remove(connectionId)
        AppLog.d("【MicrophoneManager】移除数据回调: $connectionId，剩余: ${dataCallbacks.size}")

        // 如果没有其他连接在使用，停止捕获
        if (dataCallbacks.isEmpty()) {
            stopCaptureThread()
            microphoneAudioRecord?.stop()
            AppLog.d("【MicrophoneManager】所有连接已断开，停止麦克风捕获")
        }
    }

    /**
     * 启动捕获线程
     */
    private fun startCaptureThread() {
        if (isCapturing.get()) {
            return
        }

        isCapturing.set(true)
        captureThread = Thread {
            android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_URGENT_AUDIO)
            captureMicrophoneAudio()
        }.apply {
            name = "MicrophoneManagerCaptureThread"
            priority = Thread.MAX_PRIORITY
            start()
        }
        
        AppLog.d("【MicrophoneManager】麦克风捕获线程已启动")
    }

    /**
     * 停止捕获线程
     */
    private fun stopCaptureThread() {
        isCapturing.set(false)
        
        captureThread?.let { thread ->
            try {
                thread.interrupt()
                thread.join(1000)
                AppLog.d("【MicrophoneManager】麦克风捕获线程已停止")
            } catch (e: Exception) {
                AppLog.w("【MicrophoneManager】停止捕获线程时发生异常", e)
            }
        }
        captureThread = null
    }

    /**
     * 麦克风音频捕获线程
     */
    private fun captureMicrophoneAudio() {
        val buffer = ByteArray(bufferSize)
        
        while (isCapturing.get()) {
            try {
                val bytesRead = microphoneAudioRecord?.read(buffer, 0, buffer.size) ?: 0
                
                if (bytesRead > 0) {
                    // 将数据分发给所有回调
                    val audioData = buffer.copyOf(bytesRead)
                    dataCallbacks.values.forEach { callback ->
                        try {
                            callback(audioData)
                        } catch (e: Exception) {
                            AppLog.w("【MicrophoneManager】数据回调异常", e)
                        }
                    }
                } else if (bytesRead < 0) {
                    AppLog.w("【MicrophoneManager】麦克风读取错误: $bytesRead")
                    break
                }
                
            } catch (e: Exception) {
                AppLog.e("【MicrophoneManager】麦克风捕获异常", e)
                break
            }
        }
        
        AppLog.d("【MicrophoneManager】麦克风捕获线程结束")
    }

    /**
     * 清理麦克风实例
     */
    private fun cleanupMicrophoneInstance() {
        try {
            microphoneAudioRecord?.let { record ->
                if (record.state == AudioRecord.STATE_INITIALIZED) {
                    if (record.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                        record.stop()
                    }
                    record.release()
                }
            }
            microphoneAudioRecord = null
            isMicrophoneReady = false
            AppLog.d("【MicrophoneManager】麦克风实例已清理")
        } catch (e: Exception) {
            AppLog.w("【MicrophoneManager】清理麦克风实例时发生异常", e)
        }
    }

    /**
     * 销毁麦克风管理器
     * 在APP销毁时调用
     */
    fun destroy() {
        AppLog.d("【MicrophoneManager】开始销毁麦克风管理器")
        
        // 停止所有捕获
        dataCallbacks.clear()
        stopCaptureThread()
        
        // 清理实例
        cleanupMicrophoneInstance()
        
        AppLog.d("【MicrophoneManager】麦克风管理器已销毁")
    }
}
