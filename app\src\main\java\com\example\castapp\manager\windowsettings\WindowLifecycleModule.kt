package com.example.castapp.manager.windowsettings

import com.example.castapp.utils.AppLog

/**
 * 窗口生命周期模块
 * 负责管理投屏窗口的生命周期和事件处理
 */
class WindowLifecycleModule(
    private val dataModule: WindowDataModule,
    private val creationModule: WindowCreationModule
) {
    
    // 精准控制面板更新回调
    private var precisionControlUpdateCallback: (() -> Unit)? = null
    
    // 对话框刷新回调
    private var dialogRefreshCallback: (() -> Unit)? = null
    
    /**
     * 设置精准控制面板更新回调
     */
    fun setPrecisionControlUpdateCallback(callback: () -> Unit) {
        this.precisionControlUpdateCallback = callback
    }
    
    /**
     * 设置对话框刷新回调
     */
    fun setDialogRefreshCallback(callback: () -> Unit) {
        this.dialogRefreshCallback = callback
    }
    
    /**
     * 处理新连接事件
     */
    fun handleNewConnection(connectionId: String) {
        AppLog.d("收到新连接事件: $connectionId")

        // 检查是否已有正确的分辨率信息
        val resolution = dataModule.getScreenResolution(connectionId)
        if (resolution != null && resolution.first > 0 && resolution.second > 0) {
            // 已有有效分辨率，立即创建窗口
            creationModule.createWindowForConnection(connectionId)
            AppLog.d("已有有效分辨率，立即创建投屏窗口: $connectionId")
        } else {
            // 没有有效分辨率，标记为等待创建
            dataModule.addPendingCreation(connectionId)
            AppLog.d("等待有效分辨率信息，暂缓创建投屏窗口: $connectionId")
        }
    }
    
    /**
     * 处理屏幕分辨率事件
     */
    fun handleScreenResolution(connectionId: String, width: Int, height: Int) {
        // 异步处理分辨率事件，避免阻塞主线程
        Thread {
            try {
                dataModule.setScreenResolution(connectionId, width, height)
                AppLog.d("收到发送端原始屏幕分辨率: $connectionId ${width}x${height}")
                
                // 接受任何有效的分辨率
                if (width > 0 && height > 0) {
                    AppLog.d("收到有效的发送端原始分辨率信息: $connectionId ${width}x${height}")
                    
                    // 切换回主线程进行UI操作
                    dataModule.getCurrentActivity()?.runOnUiThread {
                        // 如果该连接在等待创建窗口，现在创建它
                        if (dataModule.isPendingCreation(connectionId)) {
                            dataModule.removePendingCreation(connectionId)
                            creationModule.createWindowForConnection(connectionId)
                            AppLog.d("基于发送端原始分辨率创建投屏窗口: $connectionId")
                        } else {
                            // 重要：不更新现有窗口尺寸！
                            // 窗口容器尺寸应该始终基于发送端原始屏幕分辨率×0.4，保持固定
                            AppLog.d("投屏窗口容器尺寸保持固定，不随编码分辨率调整而改变: $connectionId")
                        }
                    }
                } else {
                    AppLog.d("收到无效分辨率信息，忽略: $connectionId ${width}x${height}")
                }
            } catch (e: Exception) {
                AppLog.e("处理分辨率事件失败", e)
            }
        }.start()
    }
    
    // 🚀 窗口移除状态管理
    private val removingWindows = mutableSetOf<String>()

    /**
     * 移除指定连接的投屏窗口 - 优化版本
     * 添加状态检查，避免重复移除
     */
    fun removeWindowForConnection(connectionId: String) {
        val activity = dataModule.getCurrentActivity() ?: return
        val container = dataModule.getSurfaceContainer() ?: return

        // 🚀 防重复移除检查
        synchronized(removingWindows) {
            if (removingWindows.contains(connectionId)) {
                AppLog.w("【窗口移除】连接 $connectionId 正在移除中，跳过重复移除")
                return
            }
            removingWindows.add(connectionId)
        }

        AppLog.d("【窗口移除】收到移除请求: $connectionId")
        AppLog.d("【窗口移除】当前映射中的窗口: ${dataModule.getAllConnectionIds()}")
        AppLog.d("【窗口移除】容器中的子视图数量: ${container.childCount}")

        activity.runOnUiThread {
            try {
                val transformHandler = dataModule.getWindowMapping(connectionId)
                if (transformHandler != null) {
                    AppLog.d("【窗口移除】找到对应的TransformHandler，开始移除")

                    // 🚀 优化：先从映射中移除，避免其他操作访问到正在清理的Handler
                    dataModule.removeWindowMapping(connectionId)

                    // 清理TransformHandler
                    transformHandler.cleanup()

                    // 从容器中移除视图
                    container.removeView(transformHandler)

                    // 📁 不要立即清理媒体文件信息，保留用于布局恢复
                    // 媒体文件信息将在APP重启或手动清理时被清除
                    if (connectionId.startsWith("video_") || connectionId.startsWith("image_")) {
                        AppLog.d("【窗口移除】媒体窗口已移除，但保留文件信息用于布局恢复: $connectionId")
                    }

                    AppLog.d("【窗口移除】投屏窗口移除完成: $connectionId")
                    AppLog.d("【窗口移除】移除后容器中的子视图数量: ${container.childCount}")
                    AppLog.d("【窗口移除】移除后映射中的窗口: ${dataModule.getAllConnectionIds()}")
                } else {
                    AppLog.w("【窗口移除】未找到连接ID对应的投屏窗口: $connectionId")
                }

                // 清理相关数据
                dataModule.removeScreenResolution(connectionId)
                dataModule.removePendingCreation(connectionId)
                dataModule.removeControlState(connectionId)

                // 🎯 清理横屏模式状态
                val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
                windowSettingsManager.clearLandscapeModeState(connectionId)

                // 刷新窗口管理对话框
                dialogRefreshCallback?.invoke()

                // 通知精准控制面板更新
                precisionControlUpdateCallback?.invoke()

            } catch (e: Exception) {
                AppLog.e("【窗口移除】移除投屏窗口时发生异常: $connectionId", e)
                // 即使发生异常，也要确保清理映射
                dataModule.removeWindowMapping(connectionId)
                dataModule.removeScreenResolution(connectionId)
                dataModule.removePendingCreation(connectionId)
                dataModule.removeControlState(connectionId)

                // 🎯 清理横屏模式状态
                val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
                windowSettingsManager.clearLandscapeModeState(connectionId)

                // 即使异常也要刷新窗口管理对话框
                dialogRefreshCallback?.invoke()

                // 通知精准控制面板更新
                precisionControlUpdateCallback?.invoke()
            } finally {
                // 🚀 确保移除状态被清理
                synchronized(removingWindows) {
                    removingWindows.remove(connectionId)
                }
            }
        }
    }
    
    /**
     * 移除所有投屏窗口
     */
    fun removeAllWindows() {
        val activity = dataModule.getCurrentActivity() ?: return
        val container = dataModule.getSurfaceContainer() ?: return
        
        activity.runOnUiThread {
            try {
                AppLog.d("开始移除所有投屏窗口，当前窗口数量: ${dataModule.getWindowCount()}")
                
                // 清理所有TransformHandler
                dataModule.getAllWindowMappings().values.forEach { transformHandler ->
                    try {
                        transformHandler.cleanup()
                        container.removeView(transformHandler)
                    } catch (e: Exception) {
                        AppLog.e("清理单个TransformHandler时发生异常", e)
                    }
                }
                
                // 清理所有映射
                dataModule.clearAllWindowMappings()
                dataModule.clearAllScreenResolutions()
                dataModule.clearAllPendingCreations()
                dataModule.clearAllControlStates()

                AppLog.d("所有投屏窗口移除完成")

                // 刷新窗口管理对话框
                dialogRefreshCallback?.invoke()

                // 通知精准控制面板更新
                precisionControlUpdateCallback?.invoke()
                
            } catch (e: Exception) {
                AppLog.e("移除所有投屏窗口时发生异常", e)
                // 即使发生异常，也要确保清理映射
                dataModule.clearAllWindowMappings()
                dataModule.clearAllScreenResolutions()
                dataModule.clearAllControlStates()

                // 即使异常也要刷新窗口管理对话框
                dialogRefreshCallback?.invoke()

                // 通知精准控制面板更新
                precisionControlUpdateCallback?.invoke()
            }
        }
    }
    
    /**
     * 清理生命周期模块
     */
    fun cleanup() {
        removeAllWindows()
        precisionControlUpdateCallback = null
        dialogRefreshCallback = null
        AppLog.d("WindowLifecycleModule清理完成")
    }
}
