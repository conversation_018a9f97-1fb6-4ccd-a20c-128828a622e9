package com.example.castapp.manager

import android.view.Surface
import com.example.castapp.rtp.RtpReceiver
import com.example.castapp.rtp.MultiConnectionManager
import com.example.castapp.websocket.WebSocketServer
import com.example.castapp.websocket.ControlMessage
import com.example.castapp.utils.AppLog

/**
 * 视频接收管理器
 * 负责管理完整的视频流接收和显示业务
 */
class VideoReceivingManager(
    private val stateManager: StateManager,
    private val callback: VideoReceivingCallback
) {
    
    /**
     * 视频接收回调接口
     */
    interface VideoReceivingCallback {
        fun onNewVideoConnection(connectionId: String)
        fun onVideoConnectionDisconnected(connectionId: String)
        fun onVideoWindowRemoved(connectionId: String) // 新增：仅移除视频窗口，不断开连接
        fun onScreenResolution(connectionId: String, width: Int, height: Int)
        fun onVideoError(connectionId: String, error: String)
        fun onResolutionAdjustmentComplete(connectionId: String, width: Int, height: Int, success: Boolean, error: String?)
        fun onVideoOrientationChanged(connectionId: String, orientation: Int, videoWidth: Int, videoHeight: Int) // 🎯 横竖屏适配：方向变化回调（包含分辨率）
    }

    // 核心组件
    private var rtpReceiver: RtpReceiver? = null
    private var multiConnectionManager: MultiConnectionManager? = null
    private var webSocketServer: WebSocketServer? = null
    
    // 运行状态
    private var isRunning = false
    
    /**
     * 启动视频接收服务
     */
    fun startVideoReceiving(port: Int, webSocketServerInstance: WebSocketServer?): Boolean {
        if (isRunning) {
            AppLog.service("视频接收服务已在运行")
            return true
        }

        try {
            this.webSocketServer = webSocketServerInstance
            
            // 初始化多连接管理器
            multiConnectionManager = MultiConnectionManager()

            // 注册显示回调，当有新连接时创建显示窗口
            multiConnectionManager!!.registerDisplayCallback("default") { connectionId ->
                AppLog.service("【视频管理器】新连接请求显示窗口: $connectionId")

                // 通知回调有新的视频连接
                callback.onNewVideoConnection(connectionId)

                AppLog.service("【视频管理器】等待UI为连接 $connectionId 设置Surface")
            }

            // 注册断开回调，当连接断开时移除显示窗口
            multiConnectionManager!!.registerDisconnectCallback("default") { connectionId ->
                AppLog.service("【视频管理器】连接断开，移除显示窗口: $connectionId")
                callback.onVideoConnectionDisconnected(connectionId)
            }

            // 注册屏幕分辨率回调
            multiConnectionManager!!.registerScreenResolutionCallback("default") { connectionId, width, height ->
                AppLog.service("【视频管理器】处理屏幕分辨率信息: $connectionId, ${width}x${height}")
                callback.onScreenResolution(connectionId, width, height)
            }

            // 初始化RTP接收器
            rtpReceiver = RtpReceiver(port, multiConnectionManager!!) {
                stateManager.getAllConnections() // 直接返回所有连接，让RtpReceiver自己计算
            }

            if (!rtpReceiver!!.start()) {
                throw Exception("启动RTP接收器失败")
            }

            isRunning = true
            AppLog.service("【视频管理器】视频接收服务启动成功: UDP端口$port")
            return true

        } catch (e: Exception) {
            AppLog.e("【视频管理器】启动视频接收服务失败", e)
            stopVideoReceiving()
            return false
        }
    }

    /**
     * 停止视频接收服务
     */
    fun stopVideoReceiving() {
        isRunning = false

        // 停止RTP接收器
        rtpReceiver?.stop()
        rtpReceiver = null

        // 清理多连接管理器
        multiConnectionManager?.cleanup()
        multiConnectionManager = null

        webSocketServer = null

        AppLog.service("【视频管理器】视频接收服务已停止")
    }

    /**
     * 为指定连接设置Surface
     */
    fun setSurfaceForConnection(connectionId: String, surface: Surface?) {
        try {
            AppLog.service("【视频管理器】为连接 $connectionId 设置Surface: surface=$surface, isValid=${surface?.isValid}")

            if (surface != null) {
                // 设置Surface到多连接管理器
                multiConnectionManager?.setSurface(connectionId, surface)
                AppLog.service("【视频管理器】为连接 $connectionId 设置Surface完成")
            } else {
                AppLog.w("【视频管理器】为连接 $connectionId 设置的Surface为null")
            }

        } catch (e: Exception) {
            AppLog.e("【视频管理器】为连接 $connectionId 设置Surface失败", e)
            callback.onVideoError(connectionId, "设置Surface失败: ${e.message}")
        }
    }

    /**
     * 清理指定连接的断开标记
     */
    fun clearDisconnectionFlag(connectionId: String) {
        try {
            multiConnectionManager?.clearDisconnectionFlag(connectionId)
            AppLog.service("【视频管理器】已清理连接 $connectionId 的断开标记")
        } catch (e: Exception) {
            AppLog.e("【视频管理器】清理连接 $connectionId 的断开标记失败", e)
        }
    }

    /**
     * 立即停止所有MediaCodec解码器
     */
    fun immediateStopAllDecoders() {
        try {
            AppLog.service("【视频管理器】【立即停止解码器】开始立即停止所有MediaCodec解码器")

            // 立即停止多连接管理器中的所有解码器
            multiConnectionManager?.let { manager ->
                val allConnectionIds = manager.getAllConnectionIds()
                AppLog.service("【视频管理器】【立即停止解码器】发现 ${allConnectionIds.size} 个活跃连接的解码器")

                allConnectionIds.forEach { connectionId ->
                    try {
                        // 立即停止该连接的视频解码器
                        manager.handleVideoStreamStop(connectionId)
                        AppLog.service("【视频管理器】【立即停止解码器】已停止连接 $connectionId 的解码器")
                    } catch (e: Exception) {
                        AppLog.e("【视频管理器】【立即停止解码器】停止连接 $connectionId 的解码器失败", e)
                    }
                }

                // 给解码器一点时间完成停止操作
                try {
                    Thread.sleep(100) // 等待100ms确保解码器完全停止
                    AppLog.service("【视频管理器】【立即停止解码器】已等待解码器完全停止")
                } catch (_: InterruptedException) {
                    Thread.currentThread().interrupt()
                }
            }

            AppLog.service("【视频管理器】【立即停止解码器】所有MediaCodec解码器已立即停止")
        } catch (e: Exception) {
            AppLog.e("【视频管理器】【立即停止解码器】立即停止所有解码器失败", e)
        }
    }

    /**
     * 🚀 优雅停止特定连接的MediaCodec解码器（用于窗口删除）
     */
    fun gracefulStopDecoder(connectionId: String) {
        try {
            AppLog.service("【视频管理器】【优雅停止解码器】开始优雅停止连接 $connectionId 的MediaCodec解码器")

            multiConnectionManager?.let { manager ->
                // 检查连接是否存在
                if (manager.getAllConnectionIds().contains(connectionId)) {
                    // 优雅停止该连接的视频解码器
                    manager.handleVideoStreamStop(connectionId)
                    AppLog.service("【视频管理器】【优雅停止解码器】已停止连接 $connectionId 的解码器")

                    // 给解码器更多时间完成清理
                    try {
                        Thread.sleep(150) // 等待150ms确保解码器完全停止
                        AppLog.service("【视频管理器】【优雅停止解码器】已等待连接 $connectionId 的解码器完全停止")
                    } catch (_: InterruptedException) {
                        Thread.currentThread().interrupt()
                    }
                } else {
                    AppLog.service("【视频管理器】【优雅停止解码器】连接 $connectionId 不存在，跳过停止操作")
                }
            }

            AppLog.service("【视频管理器】【优雅停止解码器】连接 $connectionId 的MediaCodec解码器已优雅停止")
        } catch (e: Exception) {
            AppLog.e("【视频管理器】【优雅停止解码器】优雅停止连接 $connectionId 的解码器失败", e)
        }
    }



    /**
     * 🎯 横竖屏适配：处理包含方向信息的H.264配置数据
     */
    fun handleH264ConfigWithOrientation(connectionId: String, spsData: ByteArray?, ppsData: ByteArray?, width: Int?, height: Int?, orientation: Int?) {
        try {
            val orientationName = when (orientation) {
                android.content.res.Configuration.ORIENTATION_LANDSCAPE -> "横屏"
                android.content.res.Configuration.ORIENTATION_PORTRAIT -> "竖屏"
                null -> "未指定"
                else -> "未知($orientation)"
            }

            if (width != null && height != null) {
                AppLog.service("【视频管理器】🎯 收到H.264配置数据: connectionId=$connectionId, SPS: ${spsData?.size ?: 0} bytes, PPS: ${ppsData?.size ?: 0} bytes, 分辨率: ${width}x${height}, 方向: $orientationName")
            } else {
                AppLog.service("【视频管理器】🎯 收到H.264配置数据: connectionId=$connectionId, SPS: ${spsData?.size ?: 0} bytes, PPS: ${ppsData?.size ?: 0} bytes, 方向: $orientationName")
            }

            // 将配置数据传递给多连接管理器处理
            if (spsData != null || ppsData != null) {
                multiConnectionManager?.let { manager ->
                    val connection = stateManager.findConnectionById(connectionId)
                    if (connection != null) {
                        // 将WebSocket接收到的H.264配置数据和分辨率信息传递给解码器
                        manager.handleWebSocketH264ConfigWithResolution(connection.connectionId, spsData, ppsData, width, height)
                        AppLog.service("【视频管理器】已将WebSocket H.264配置数据传递给多连接管理器: ${connection.connectionId}")
                    } else {
                        AppLog.w("【视频管理器】未找到connectionId对应的连接，无法处理H.264配置: $connectionId")
                        callback.onVideoError(connectionId, "未找到对应连接")
                    }
                }
            }

            // 🎯 横竖屏适配：通知UI层方向变化（包含分辨率信息）
            orientation?.let { orientationValue ->
                val videoWidth = width ?: 0
                val videoHeight = height ?: 0
                callback.onVideoOrientationChanged(connectionId, orientationValue, videoWidth, videoHeight)
                AppLog.service("【视频管理器】🎯 已通知UI层方向变化: $connectionId, 方向: $orientationName, 分辨率: ${videoWidth}×${videoHeight}")
            }
        } catch (e: Exception) {
            AppLog.e("【视频管理器】处理H.264配置数据失败: $connectionId", e)
            callback.onVideoError(connectionId, "处理H.264配置失败: ${e.message}")
        }
    }

    /**
     * 处理视频流停止
     */
    fun handleVideoStreamStop(connectionId: String) {
        try {
            AppLog.service("【视频管理器】收到视频流停止消息: $connectionId")

            // 停止视频解码器，但保持连接（因为可能还有音频流）
            multiConnectionManager?.let { manager ->
                manager.handleVideoStreamStop(connectionId)
                AppLog.service("【视频管理器】视频流已停止，解码器已清理: $connectionId")
            }

            AppLog.service("【视频管理器】视频流停止处理完成: $connectionId")
        } catch (e: Exception) {
            AppLog.e("【视频管理器】处理视频流停止失败: $connectionId", e)
            callback.onVideoError(connectionId, "停止视频流失败: ${e.message}")
        }
    }

    /**
     * 处理屏幕分辨率信息
     */
    fun handleScreenResolution(connectionId: String, width: Int, height: Int) {
        try {
            AppLog.service("【视频管理器】处理屏幕分辨率信息: $connectionId, ${width}x${height}")
            
            // 通过回调通知上层处理
            callback.onScreenResolution(connectionId, width, height)
        } catch (e: Exception) {
            AppLog.e("【视频管理器】处理屏幕分辨率失败: $connectionId", e)
            callback.onVideoError(connectionId, "处理屏幕分辨率失败: ${e.message}")
        }
    }

    /**
     * 处理分辨率变化（实时调整）
     */
    fun handleResolutionChange(connectionId: String, newWidth: Int, newHeight: Int) {
        try {
            AppLog.service("【视频管理器】处理分辨率变化: $connectionId, 新分辨率: ${newWidth}x${newHeight}")

            var success = false
            var errorMessage: String? = null

            // 通知多连接管理器更新解码器分辨率
            multiConnectionManager?.let { manager ->
                success = manager.updateDecoderResolution(connectionId, newWidth, newHeight)
                if (success) {
                    AppLog.service("【视频管理器】解码器分辨率更新成功: $connectionId, ${newWidth}x${newHeight}")
                } else {
                    AppLog.w("【视频管理器】解码器分辨率更新失败: $connectionId, ${newWidth}x${newHeight}")
                    errorMessage = "解码器分辨率更新失败"
                }
            } ?: run {
                errorMessage = "多连接管理器未初始化"
                AppLog.w("【视频管理器】多连接管理器未初始化，无法更新分辨率")
            }

            // 发送分辨率调整完成通知给发送端
            sendResolutionAdjustmentComplete(connectionId, newWidth, newHeight, success, errorMessage)
            
            // 通知回调
            callback.onResolutionAdjustmentComplete(connectionId, newWidth, newHeight, success, errorMessage)

        } catch (e: Exception) {
            AppLog.e("【视频管理器】处理分辨率变化失败: $connectionId", e)
            val errorMsg = e.message ?: "未知错误"
            sendResolutionAdjustmentComplete(connectionId, newWidth, newHeight, false, errorMsg)
            callback.onVideoError(connectionId, "处理分辨率变化失败: $errorMsg")
        }
    }

    /**
     * 发送分辨率调整完成通知给发送端
     */
    private fun sendResolutionAdjustmentComplete(connectionId: String, width: Int, height: Int, success: Boolean, error: String?) {
        try {
            AppLog.service("【视频管理器】发送分辨率调整完成通知: connectionId=$connectionId")

            val message = ControlMessage.createResolutionAdjustmentComplete(connectionId, width, height, success, error)
            val sent = webSocketServer?.sendMessageToClient(connectionId, message) ?: false

            if (sent) {
                AppLog.service("【视频管理器】已发送分辨率调整完成通知: $connectionId, ${width}x${height}, 成功: $success")
            } else {
                AppLog.w("【视频管理器】发送分辨率调整完成通知失败: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("【视频管理器】发送分辨率调整完成通知异常: $connectionId", e)
        }
    }

    /**
     * 处理视频功能控制
     */
    fun handleVideoFunctionControl(connectionId: String, isEnabled: Boolean) {
        try {
            AppLog.service("【视频管理器】处理视频功能控制: $connectionId, 启用: $isEnabled")

            if (isEnabled) {
                // 启用视频功能，立即创建投屏窗口
                multiConnectionManager?.let { manager ->
                    // 清理视频流停止状态
                    manager.clearVideoStreamStoppedState(connectionId)
                    AppLog.service("【视频管理器】已清理视频流停止状态: $connectionId")

                    // 立即触发显示回调
                    manager.triggerDisplayCallback(connectionId)
                    AppLog.service("【视频管理器】功能控制：启用视频功能，立即创建投屏窗口: $connectionId")
                }
            } else {
                // 禁用视频功能，移除投屏窗口但保持连接
                AppLog.service("【视频管理器】功能控制：禁用视频功能: $connectionId")

                // 停止视频解码器，但保持连接（因为可能还有音频流）
                multiConnectionManager?.let { manager ->
                    manager.handleVideoStreamStop(connectionId)
                    AppLog.service("【视频管理器】视频流已停止，解码器已清理: $connectionId")
                }

                // 通知回调移除投屏窗口（但不断开连接，不清理音频）
                callback.onVideoWindowRemoved(connectionId)
                AppLog.service("【视频管理器】已通知移除投屏窗口: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("【视频管理器】处理视频功能控制失败: $connectionId", e)
            callback.onVideoError(connectionId, "处理视频功能控制失败: ${e.message}")
        }
    }

    /**
     * 处理连接断开
     */
    fun handleConnectionDisconnect(connectionId: String) {
        try {
            AppLog.service("【视频管理器】处理连接断开: $connectionId")
            
            // 处理多连接管理器的断开逻辑（会自动调用断开回调）
            multiConnectionManager?.handleConnectionDisconnect(connectionId)
            
            AppLog.service("【视频管理器】连接断开处理完成: $connectionId")
        } catch (e: Exception) {
            AppLog.e("【视频管理器】处理连接断开失败: $connectionId", e)
            callback.onVideoError(connectionId, "处理连接断开失败: ${e.message}")
        }
    }

    /**
     * 获取运行状态
     */
    fun isRunning(): Boolean = isRunning


}
