package com.example.castapp.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit

/**
 * 文本窗口尺寸管理器
 * 负责保存和恢复文本窗口的自定义尺寸
 */
class TextSizeManager(context: Context) {
    
    companion object {
        private const val PREF_NAME = "text_window_sizes"
        private const val KEY_WIDTH_SUFFIX = "_width"
        private const val KEY_HEIGHT_SUFFIX = "_height"
        
        // 默认尺寸
        const val DEFAULT_WIDTH = 300
        const val DEFAULT_HEIGHT = 200
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    
    /**
     * 保存文本窗口尺寸
     */
    fun saveTextWindowSize(textId: String, width: Int, height: Int) {
        sharedPreferences.edit {
            putInt(textId + KEY_WIDTH_SUFFIX, width)
            putInt(textId + KEY_HEIGHT_SUFFIX, height)
        }

        AppLog.d("【文本尺寸管理器】已保存尺寸: ID=$textId, 尺寸=${width}x${height}")
    }
    
    /**
     * 获取文本窗口尺寸
     */
    fun getTextWindowSize(textId: String): Pair<Int, Int> {
        val width = sharedPreferences.getInt(textId + KEY_WIDTH_SUFFIX, DEFAULT_WIDTH)
        val height = sharedPreferences.getInt(textId + KEY_HEIGHT_SUFFIX, DEFAULT_HEIGHT)
        
        AppLog.d("【文本尺寸管理器】已获取尺寸: ID=$textId, 尺寸=${width}x${height}")
        return Pair(width, height)
    }
}
