package com.example.castapp.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import android.widget.*
import com.example.castapp.R
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.utils.AppLog
import java.util.Locale
import kotlin.math.max
import kotlin.math.min

/**
 * 精准控制面板
 * 提供坐标、缩放、旋转的精确数值控制
 */
class PrecisionControlPanel @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    // UI组件
    private lateinit var titleBar: LinearLayout
    private lateinit var dragHandle: ImageView
    private lateinit var tvPanelTitle: TextView
    private lateinit var btnClosePanel: ImageView
    private lateinit var etPositionX: EditText
    private lateinit var etPositionY: EditText
    private lateinit var etScaleFactor: EditText
    private lateinit var etRotationAngle: EditText
    private lateinit var btnApplyTransform: Button
    private lateinit var btnResetTransform: Button

    // 数据 - 简化为单窗口控制
    private var boundWindow: CastWindowInfo? = null
    private var isUpdatingFromCode = false

    // 拖拽相关
    private var isDragging = false
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    private var initialX = 0f
    private var initialY = 0f

    // 回调接口
    interface OnTransformChangeListener {
        fun onTransformChanged(connectionId: String, x: Float, y: Float, scale: Float, rotation: Float)
        fun onResetTransform(connectionId: String)
        fun onPanelClosed(connectionId: String) // 面板关闭时的回调，传递connectionId
    }

    private var transformChangeListener: OnTransformChangeListener? = null

    init {
        initView()
        setupListeners()
    }

    private fun initView() {
        LayoutInflater.from(context).inflate(R.layout.precision_control_panel, this, true)

        titleBar = findViewById(R.id.title_bar)
        dragHandle = findViewById(R.id.drag_handle)
        tvPanelTitle = findViewById(R.id.tv_panel_title)
        btnClosePanel = findViewById(R.id.btn_close_panel)
        etPositionX = findViewById(R.id.et_position_x)
        etPositionY = findViewById(R.id.et_position_y)
        etScaleFactor = findViewById(R.id.et_scale_factor)
        etRotationAngle = findViewById(R.id.et_rotation_angle)
        btnApplyTransform = findViewById(R.id.btn_apply_transform)
        btnResetTransform = findViewById(R.id.btn_reset_transform)
    }

    private fun setupListeners() {
        // 关闭按钮
        btnClosePanel.setOnClickListener {
            val connectionId = boundWindow?.connectionId
            if (connectionId != null) {
                transformChangeListener?.onPanelClosed(connectionId)
                AppLog.d("【精准控制面板】用户点击关闭按钮: $connectionId")
            }
        }

        // 设置标题栏点击监听器（支持无障碍访问）
        titleBar.setOnClickListener {
            AppLog.d("【精准控制面板】标题栏被点击")
        }

        // 设置标题栏拖拽监听
        setupDragListeners()

        // 应用按钮
        btnApplyTransform.setOnClickListener {
            applyTransform()
        }

        // 重置按钮
        btnResetTransform.setOnClickListener {
            resetTransform()
        }

        // 移除实时输入监听，改为手动应用
        // setupRealTimeInputListeners()
    }

    /**
     * 绑定窗口信息
     */
    fun bindWindow(windowInfo: CastWindowInfo) {
        boundWindow = windowInfo
        updateControlValues()
        updatePanelTitle()
        AppLog.d("【精准控制面板】绑定窗口: ${windowInfo.connectionId}")
    }

    /**
     * 更新绑定窗口的信息
     */
    fun updateBoundWindow(windowInfo: CastWindowInfo) {
        if (boundWindow?.connectionId == windowInfo.connectionId) {
            boundWindow = windowInfo
            updateControlValues()
            updatePanelTitle()
            AppLog.d("【精准控制面板】更新绑定窗口信息: ${windowInfo.connectionId}")
        }
    }

    /**
     * 更新控件数值显示
     */
    private fun updateControlValues() {
        val window = boundWindow ?: return

        isUpdatingFromCode = true

        etPositionX.setText(String.format(Locale.US, "%.1f", window.positionX))
        etPositionY.setText(String.format(Locale.US, "%.1f", window.positionY))
        etScaleFactor.setText(String.format(Locale.US, "%.2f", window.scaleFactor))
        etRotationAngle.setText(String.format(Locale.US, "%.1f", window.rotationAngle))

        isUpdatingFromCode = false

        AppLog.d("【精准控制面板】更新控件数值: X=${window.positionX}, Y=${window.positionY}, Scale=${window.scaleFactor}, Rotation=${window.rotationAngle}")
    }

    /**
     * 更新面板标题显示
     */
    private fun updatePanelTitle() {
        val window = boundWindow
        if (window != null) {
            val deviceName = window.deviceName ?: "未知设备"
            val shortId = window.getShortConnectionId()
            val title = "$deviceName($shortId)"
            tvPanelTitle.text = title
            AppLog.d("【精准控制面板】更新标题: $title")
        } else {
            tvPanelTitle.text = "未绑定窗口"
        }
    }

    /**
     * 应用变换
     */
    private fun applyTransform() {
        val window = boundWindow ?: return
        
        try {
            val x = etPositionX.text.toString().toFloatOrNull() ?: 0f
            val y = etPositionY.text.toString().toFloatOrNull() ?: 0f
            val scale = etScaleFactor.text.toString().toFloatOrNull() ?: 1f
            val rotation = etRotationAngle.text.toString().toFloatOrNull() ?: 0f
            
            // 限制数值范围
            val clampedScale = scale.coerceIn(0.1f, 10f)
            val clampedRotation = ((rotation % 360f) + 360f) % 360f
            
            transformChangeListener?.onTransformChanged(window.connectionId, x, y, clampedScale, clampedRotation)
            
            AppLog.d("【精准控制面板】应用变换: ${window.connectionId}, X=$x, Y=$y, Scale=$clampedScale, Rotation=$clampedRotation")
            
        } catch (e: Exception) {
            AppLog.e("【精准控制面板】应用变换失败", e)
            Toast.makeText(context, "输入数值格式错误", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 重置变换
     */
    private fun resetTransform() {
        val window = boundWindow ?: return
        
        isUpdatingFromCode = true
        etPositionX.setText("0.0")
        etPositionY.setText("0.0")
        etScaleFactor.setText("1.0")
        etRotationAngle.setText("0.0")
        isUpdatingFromCode = false
        
        transformChangeListener?.onResetTransform(window.connectionId)
        
        AppLog.d("【精准控制面板】重置变换: ${window.connectionId}")
    }

    /**
     * 设置变换监听器
     */
    fun setOnTransformChangeListener(listener: OnTransformChangeListener?) {
        transformChangeListener = listener
    }

    /**
     * 显示面板
     */
    fun showPanel() {
        visibility = VISIBLE
        AppLog.d("【精准控制面板】显示面板")
    }

    /**
     * 更新指定窗口的变换数值显示
     */
    fun updateTransformValues(connectionId: String, x: Float, y: Float, scale: Float, rotation: Float) {
        // 只有绑定的窗口才更新显示
        if (boundWindow?.connectionId == connectionId) {
            isUpdatingFromCode = true

            etPositionX.setText(String.format(Locale.US, "%.1f", x))
            etPositionY.setText(String.format(Locale.US, "%.1f", y))
            etScaleFactor.setText(String.format(Locale.US, "%.2f", scale))
            etRotationAngle.setText(String.format(Locale.US, "%.1f", rotation))

            isUpdatingFromCode = false

            AppLog.d("【精准控制面板】更新变换数值显示: $connectionId, X=$x, Y=$y, Scale=$scale, Rotation=$rotation")
        }
    }

    /**
     * 设置拖拽监听器
     */
    private fun setupDragListeners() {
        titleBar.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    isDragging = true
                    lastTouchX = event.rawX
                    lastTouchY = event.rawY
                    initialX = x
                    initialY = y
                    AppLog.d("【精准控制面板】开始拖拽: X=${event.rawX}, Y=${event.rawY}")
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    if (isDragging) {
                        val newX = initialX + (event.rawX - lastTouchX)
                        val newY = initialY + (event.rawY - lastTouchY)

                        // 边界检测
                        val parentView = parent as? ViewGroup
                        if (parentView != null) {
                            val maxX = parentView.width - width
                            val maxY = parentView.height - height

                            val clampedX = max(0f, min(newX, maxX.toFloat()))
                            val clampedY = max(0f, min(newY, maxY.toFloat()))

                            x = clampedX
                            y = clampedY
                        } else {
                            x = newX
                            y = newY
                        }

                        AppLog.d("【精准控制面板】拖拽移动: X=$x, Y=$y")
                    }
                    true
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    if (isDragging) {
                        isDragging = false
                        titleBar.performClick()
                        AppLog.d("【精准控制面板】结束拖拽: 最终位置 X=$x, Y=$y")
                    }
                    true
                }
                else -> false
            }
        }
    }
}
