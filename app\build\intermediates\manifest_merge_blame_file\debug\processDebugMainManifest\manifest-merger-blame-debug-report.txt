1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.castapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 基本权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
14-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:8:5-79
14-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:8:22-76
15    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
15-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:9:5-76
15-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:9:22-73
16    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
16-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:10:5-76
16-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:10:22-73
17
18    <!-- 媒体相关权限 -->
19    <uses-permission android:name="android.permission.RECORD_AUDIO" />
19-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:13:5-71
19-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:13:22-68
20    <uses-permission android:name="android.permission.CAMERA" />
20-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:14:5-65
20-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:14:22-62
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:15:5-77
21-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:15:22-74
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
22-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:16:5-94
22-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:16:22-91
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
23-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:17:5-92
23-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:17:22-89
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
24-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:18:5-88
24-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:18:22-85
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
25-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:19:5-89
25-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:19:22-86
26    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
26-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:20:5-77
26-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:20:22-74
27
28    <!-- 存储权限 -->
29    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
29-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:23:5-24:40
29-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:23:22-79
30    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
30-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:25:5-76
30-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:25:22-73
31    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
31-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:26:5-75
31-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:26:22-72
32
33    <!-- 系统窗口权限 -->
34    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
34-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:29:5-78
34-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:29:22-75
35
36    <!-- 硬件特性 -->
37    <uses-feature
37-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:32:5-82
38        android:name="android.hardware.wifi"
38-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:32:19-55
39        android:required="true" />
39-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:32:56-79
40    <uses-feature
40-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:33:5-85
41        android:name="android.hardware.camera"
41-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:33:19-57
42        android:required="false" />
42-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:33:58-82
43    <uses-feature
43-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:34:5-95
44        android:name="android.hardware.screen.landscape"
44-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:34:19-67
45        android:required="false" />
45-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:34:68-92
46    <uses-feature
46-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:35:5-94
47        android:name="android.hardware.screen.portrait"
47-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:35:19-66
48        android:required="false" />
48-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:35:67-91
49
50    <!-- 修改音频设置的权限 -->
51    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
51-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:38:5-80
51-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:38:22-77
52
53    <permission
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
54        android:name="com.example.castapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
55        android:protectionLevel="signature" />
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
56
57    <uses-permission android:name="com.example.castapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
58
59    <application
59-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:40:5-100:19
60        android:allowBackup="true"
60-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:41:9-35
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
62        android:debuggable="true"
63        android:extractNativeLibs="false"
64        android:hardwareAccelerated="true"
64-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:45:9-43
65        android:label="CastAPP"
65-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:42:9-32
66        android:largeHeap="true"
66-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:46:9-33
67        android:supportsRtl="true"
67-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:43:9-35
68        android:testOnly="true"
69        android:theme="@style/Theme.AppCompat.Light.NoActionBar"
69-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:44:9-65
70        android:usesCleartextTraffic="true" >
70-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:47:9-44
71        <activity
71-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:50:9-58:20
72            android:name="com.example.castapp.ui.MainActivity"
72-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:51:13-44
73            android:exported="true"
73-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:52:13-36
74            android:screenOrientation="portrait" >
74-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:53:13-49
75            <intent-filter>
75-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:54:13-57:29
76                <action android:name="android.intent.action.MAIN" />
76-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:55:17-69
76-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:55:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:56:17-77
78-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:56:27-74
79            </intent-filter>
80        </activity>
81
82        <service
82-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:60:9-64:63
83            android:name="com.example.castapp.service.CastingService"
83-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:61:13-51
84            android:enabled="true"
84-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:62:13-35
85            android:exported="false"
85-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:63:13-37
86            android:foregroundServiceType="mediaProjection" />
86-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:64:13-60
87        <service
87-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:66:9-70:61
88            android:name="com.example.castapp.service.ReceivingService"
88-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:67:13-53
89            android:enabled="true"
89-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:68:13-35
90            android:exported="false"
90-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:69:13-37
91            android:foregroundServiceType="mediaPlayback" />
91-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:70:13-58
92        <service
92-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:72:9-76:74
93            android:name="com.example.castapp.service.AudioService"
93-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:73:13-49
94            android:enabled="true"
94-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:74:13-35
95            android:exported="false"
95-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:75:13-37
96            android:foregroundServiceType="mediaProjection|microphone" />
96-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:76:13-71
97        <service
97-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:78:9-87:19
98            android:name="com.example.castapp.service.FloatingStopwatchService"
98-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:79:13-61
99            android:enabled="true"
99-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:80:13-35
100            android:exported="false"
100-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:81:13-37
101            android:foregroundServiceType="specialUse" >
101-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:82:13-55
102
103            <!-- Required for "specialUse" type. Describe your use case for app store review -->
104            <property
104-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:84:13-86:42
105                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
105-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:85:17-76
106                android:value="悬浮秒表服务" />
106-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:86:17-39
107        </service>
108        <service
108-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:89:9-98:19
109            android:name="com.example.castapp.service.RemoteReceiverService"
109-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:90:13-58
110            android:enabled="true"
110-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:91:13-35
111            android:exported="false"
111-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:92:13-37
112            android:foregroundServiceType="specialUse" >
112-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:93:13-55
113
114            <!-- Required for "specialUse" type. Describe your use case for app store review -->
115            <property
115-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:84:13-86:42
116                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
116-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:85:17-76
117                android:value="远程接收端控制服务" />
117-->D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:86:17-39
118        </service>
119
120        <provider
120-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
121            android:name="androidx.startup.InitializationProvider"
121-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
122            android:authorities="com.example.castapp.androidx-startup"
122-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
123            android:exported="false" >
123-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
124            <meta-data
124-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.emoji2.text.EmojiCompatInitializer"
125-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
126                android:value="androidx.startup" />
126-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
127            <meta-data
127-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
128-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
129                android:value="androidx.startup" />
129-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
130            <meta-data
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
132                android:value="androidx.startup" />
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
133        </provider>
134
135        <service
135-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
136            android:name="androidx.room.MultiInstanceInvalidationService"
136-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
137            android:directBootAware="true"
137-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
138            android:exported="false" />
138-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
139
140        <receiver
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
141            android:name="androidx.profileinstaller.ProfileInstallReceiver"
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
142            android:directBootAware="false"
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
143            android:enabled="true"
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
144            android:exported="true"
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
145            android:permission="android.permission.DUMP" >
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
147                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
148            </intent-filter>
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
150                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
151            </intent-filter>
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
153                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
154            </intent-filter>
155            <intent-filter>
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
156                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
157            </intent-filter>
158        </receiver>
159    </application>
160
161</manifest>
