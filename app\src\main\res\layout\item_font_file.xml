<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground"
    android:minHeight="48dp">

    <!-- 文件图标 -->
    <ImageView
        android:id="@+id/iv_file_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_font"
        android:layout_marginEnd="12dp"
        app:tint="#666666" />

    <!-- 文件名 -->
    <TextView
        android:id="@+id/tv_file_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="字体文件.ttf"
        android:textSize="14sp"
        android:textColor="#333333"
        android:ellipsize="end"
        android:singleLine="true" />

</LinearLayout>
