package com.example.castapp.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import com.example.castapp.utils.AppLog

/**
 * 文本编辑边框视图
 * 为文本编辑模式提供视觉边框指示和拖动调整大小功能
 */
class TextEditBorderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val BORDER_WIDTH = 2f // 边框宽度(dp) - 优雅的细边框
        private const val TOUCH_THRESHOLD = 20f // 边框触摸区域阈值(dp)
        private const val MIN_SIZE = 30f // 最小尺寸(dp)
        private const val HANDLE_RADIUS = 4f // 拖动手柄半径(dp) - 小圆点设计
        private const val CORNER_RADIUS = 6f // 边框圆角半径(dp)
    }

    // 绘制相关
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val handlePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    // 预分配的绘制对象，避免在 onDraw 中重复创建
    private val backgroundRect = RectF()
    private val borderRect = RectF()

    // 拖动状态
    private var isDragging = false
    private var dragMode = DragMode.NONE
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 拖动模式枚举
    private enum class DragMode {
        NONE, LEFT, RIGHT, TOP, BOTTOM,
        TOP_LEFT, TOP_RIGHT, BOTTOM_LEFT, BOTTOM_RIGHT
    }

    // 尺寸变化监听器
    private var onSizeChangeListener: ((Int, Int) -> Unit)? = null

    // 像素密度
    private val density = context.resources.displayMetrics.density

    init {
        setupPaints()
        AppLog.d("【文本编辑边框】TextEditBorderView 初始化完成")
    }

    /**
     * 设置尺寸变化监听器
     */
    fun setOnSizeChangeListener(listener: (Int, Int) -> Unit) {
        this.onSizeChangeListener = listener
    }

    /**
     * 设置绘制画笔
     */
    private fun setupPaints() {
        // 边框画笔 - 优雅的细边框
        borderPaint.apply {
            color = 0xFF2196F3.toInt() // Material Design 蓝色
            strokeWidth = BORDER_WIDTH * density
            style = Paint.Style.STROKE
        }

        // 拖动手柄画笔 - 小圆点设计
        handlePaint.apply {
            color = 0xFF2196F3.toInt() // 与边框同色，保持一致性
            style = Paint.Style.FILL
        }

        // 背景画笔 - 轻微的背景色提示
        backgroundPaint.apply {
            color = 0x0A2196F3 // 极淡的蓝色背景
            style = Paint.Style.FILL
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val width = width.toFloat()
        val height = height.toFloat()
        val cornerRadius = CORNER_RADIUS * density
        val handleRadius = HANDLE_RADIUS * density

        // 1. 绘制轻微的背景色（可选的视觉提示）
        backgroundRect.set(0f, 0f, width, height)
        canvas.drawRoundRect(backgroundRect, cornerRadius, cornerRadius, backgroundPaint)

        // 2. 绘制圆角边框
        val borderOffset = BORDER_WIDTH * density / 2f
        borderRect.set(
            borderOffset,
            borderOffset,
            width - borderOffset,
            height - borderOffset
        )
        canvas.drawRoundRect(borderRect, cornerRadius, cornerRadius, borderPaint)

        // 3. 绘制优雅的拖动手柄
        drawElegantHandles(canvas, width, height, handleRadius)
    }

    /**
     * 绘制优雅的拖动手柄
     */
    private fun drawElegantHandles(canvas: Canvas, width: Float, height: Float, handleRadius: Float) {
        // 四个角落的圆点手柄
        canvas.drawCircle(handleRadius * 2, handleRadius * 2, handleRadius, handlePaint) // 左上
        canvas.drawCircle(width - handleRadius * 2, handleRadius * 2, handleRadius, handlePaint) // 右上
        canvas.drawCircle(handleRadius * 2, height - handleRadius * 2, handleRadius, handlePaint) // 左下
        canvas.drawCircle(width - handleRadius * 2, height - handleRadius * 2, handleRadius, handlePaint) // 右下

        // 边框中间的小圆点（更小更精致）
        val midHandleRadius = handleRadius * 0.7f

        // 上边中间
        canvas.drawCircle(width / 2, handleRadius * 2, midHandleRadius, handlePaint)

        // 下边中间
        canvas.drawCircle(width / 2, height - handleRadius * 2, midHandleRadius, handlePaint)

        // 左边中间
        canvas.drawCircle(handleRadius * 2, height / 2, midHandleRadius, handlePaint)

        // 右边中间
        canvas.drawCircle(width - handleRadius * 2, height / 2, midHandleRadius, handlePaint)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                lastTouchX = event.x
                lastTouchY = event.y
                dragMode = detectDragMode(event.x, event.y)

                if (dragMode != DragMode.NONE) {
                    isDragging = true
                    AppLog.d("【文本编辑边框】开始拖动调整大小: $dragMode")
                    return true
                }
                return false
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDragging && dragMode != DragMode.NONE) {
                    val deltaX = event.x - lastTouchX
                    val deltaY = event.y - lastTouchY

                    performResize(deltaX, deltaY)

                    lastTouchX = event.x
                    lastTouchY = event.y
                    return true
                }
                return false
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isDragging) {
                    isDragging = false
                    dragMode = DragMode.NONE
                    AppLog.d("【文本编辑边框】拖动调整大小结束")
                    return true
                }
                return false
            }
        }
        return super.onTouchEvent(event)
    }

    /**
     * 检测拖动模式
     */
    private fun detectDragMode(x: Float, y: Float): DragMode {
        val threshold = TOUCH_THRESHOLD * density
        val width = width.toFloat()
        val height = height.toFloat()

        val isLeft = x <= threshold
        val isRight = x >= width - threshold
        val isTop = y <= threshold
        val isBottom = y >= height - threshold

        return when {
            isLeft && isTop -> DragMode.TOP_LEFT
            isRight && isTop -> DragMode.TOP_RIGHT
            isLeft && isBottom -> DragMode.BOTTOM_LEFT
            isRight && isBottom -> DragMode.BOTTOM_RIGHT
            isLeft -> DragMode.LEFT
            isRight -> DragMode.RIGHT
            isTop -> DragMode.TOP
            isBottom -> DragMode.BOTTOM
            else -> DragMode.NONE
        }
    }

    /**
     * 执行调整大小
     */
    private fun performResize(deltaX: Float, deltaY: Float) {
        val layoutParams = this.layoutParams as? FrameLayout.LayoutParams ?: return

        val minSizePx = MIN_SIZE * density
        var newWidth = this.width.toFloat()
        var newHeight = this.height.toFloat()
        var newX = x
        var newY = y

        when (dragMode) {
            DragMode.LEFT -> {
                val proposedWidth = newWidth - deltaX
                if (proposedWidth >= minSizePx) {
                    newWidth = proposedWidth
                    newX += deltaX
                }
            }
            DragMode.RIGHT -> {
                newWidth = (newWidth + deltaX).coerceAtLeast(minSizePx)
            }
            DragMode.TOP -> {
                val proposedHeight = newHeight - deltaY
                if (proposedHeight >= minSizePx) {
                    newHeight = proposedHeight
                    newY += deltaY
                }
            }
            DragMode.BOTTOM -> {
                newHeight = (newHeight + deltaY).coerceAtLeast(minSizePx)
            }
            DragMode.TOP_LEFT -> {
                val proposedWidth = newWidth - deltaX
                val proposedHeight = newHeight - deltaY
                if (proposedWidth >= minSizePx) {
                    newWidth = proposedWidth
                    newX += deltaX
                }
                if (proposedHeight >= minSizePx) {
                    newHeight = proposedHeight
                    newY += deltaY
                }
            }
            DragMode.TOP_RIGHT -> {
                newWidth = (newWidth + deltaX).coerceAtLeast(minSizePx)
                val proposedHeight = newHeight - deltaY
                if (proposedHeight >= minSizePx) {
                    newHeight = proposedHeight
                    newY += deltaY
                }
            }
            DragMode.BOTTOM_LEFT -> {
                val proposedWidth = newWidth - deltaX
                if (proposedWidth >= minSizePx) {
                    newWidth = proposedWidth
                    newX += deltaX
                }
                newHeight = (newHeight + deltaY).coerceAtLeast(minSizePx)
            }
            DragMode.BOTTOM_RIGHT -> {
                newWidth = (newWidth + deltaX).coerceAtLeast(minSizePx)
                newHeight = (newHeight + deltaY).coerceAtLeast(minSizePx)
            }
            else -> return
        }

        // 应用新的尺寸和位置
        layoutParams.width = newWidth.toInt()
        layoutParams.height = newHeight.toInt()
        this.layoutParams = layoutParams
        this.x = newX
        this.y = newY

        // 通知尺寸变化
        onSizeChangeListener?.invoke(newWidth.toInt(), newHeight.toInt())

        AppLog.d("【文本编辑边框】尺寸已调整: ${newWidth.toInt()}x${newHeight.toInt()}")
    }
}
