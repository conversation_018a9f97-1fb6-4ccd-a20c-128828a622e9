package com.example.castapp.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.core.graphics.toColorInt
import com.example.castapp.utils.AppLog
import kotlin.math.*

/**
 * 简单的裁剪覆盖层视图
 * 提供基本的裁剪框和拖拽功能
 */
class CropOverlayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 裁剪区域（相对于视图的像素坐标）
    private var cropRect = RectF()

    // 待设置的裁剪比例（当View尺寸未确定时缓存）
    private var pendingCropRatio: RectF? = null

    // 画笔
    private val borderPaint = Paint().apply {
        color = "#4CAF50".toColorInt()  // 绿色边框
        style = Paint.Style.STROKE
        strokeWidth = 8f  // 🎯 增加边框线条粗细：4f → 8f
        isAntiAlias = true
    }
    
    private val handlePaint = Paint().apply {
        color = Color.WHITE  // 🎯 改为白色手柄，更专业
        style = Paint.Style.STROKE
        strokeWidth = 10f  // 🎯 增大L型手柄线条粗细：6f → 10f
        isAntiAlias = true
    }

    // 🎯 新增：手柄边框画笔（黑色描边，增强对比度）
    private val handleBorderPaint = Paint().apply {
        color = Color.BLACK
        style = Paint.Style.STROKE
        strokeWidth = 12f  // 🎯 增大边框粗细：8f → 12f
        isAntiAlias = true
    }
    
    private val maskPaint = Paint().apply {
        color = "#80000000".toColorInt()  // 半透明黑色遮罩
        style = Paint.Style.FILL
    }

    // 手柄大小
    private val handleSize = 80f  // 🎯 增大触摸区域：60f → 80f
    private val handleLength = 50f  // 🎯 增大L型手柄长度：30f → 50f
    private val minCropSize = 100f

    // 触摸状态
    private var activeHandle = HandleType.NONE
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 手柄类型
    private enum class HandleType {
        NONE, TOP_LEFT, TOP_RIGHT, BOTTOM_LEFT, BOTTOM_RIGHT,
        TOP, BOTTOM, LEFT, RIGHT, CENTER
    }

    // 回调接口
    interface CropChangeListener {
        fun onCropChanged(cropRect: RectF)
    }

    private var cropChangeListener: CropChangeListener? = null

    fun setCropChangeListener(listener: CropChangeListener?) {
        cropChangeListener = listener
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // 如果有待设置的裁剪比例，优先使用它
        pendingCropRatio?.let { ratio ->
            cropRect.set(
                ratio.left * w,
                ratio.top * h,
                ratio.right * w,
                ratio.bottom * h
            )
            pendingCropRatio = null // 清除缓存
            AppLog.d("尺寸变化: ${w}x${h}, 恢复裁剪区域: $cropRect")
        } ?: run {
            // 没有待设置的比例，初始化为整个视图
            cropRect.set(0f, 0f, w.toFloat(), h.toFloat())
            AppLog.d("尺寸变化: ${w}x${h}, 初始裁剪区域: $cropRect")
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        if (width == 0 || height == 0) return

        // 绘制遮罩（裁剪区域外的半透明覆盖）
        drawMask(canvas)
        
        // 绘制裁剪边框
        canvas.drawRect(cropRect, borderPaint)
        
        // 绘制手柄
        drawHandles(canvas)
    }

    private fun drawMask(canvas: Canvas) {
        // 上方遮罩
        canvas.drawRect(0f, 0f, width.toFloat(), cropRect.top, maskPaint)
        // 下方遮罩
        canvas.drawRect(0f, cropRect.bottom, width.toFloat(), height.toFloat(), maskPaint)
        // 左侧遮罩
        canvas.drawRect(0f, cropRect.top, cropRect.left, cropRect.bottom, maskPaint)
        // 右侧遮罩
        canvas.drawRect(cropRect.right, cropRect.top, width.toFloat(), cropRect.bottom, maskPaint)
    }

    private fun drawHandles(canvas: Canvas) {
        // 🎯 绘制L型角落手柄（专业图片编辑器风格）
        drawLShapeHandle(canvas, cropRect.left, cropRect.top, isLeft = true, isTop = true)      // 左上角
        drawLShapeHandle(canvas, cropRect.right, cropRect.top, isLeft = false, isTop = true)    // 右上角
        drawLShapeHandle(canvas, cropRect.left, cropRect.bottom, isLeft = true, isTop = false)  // 左下角
        drawLShapeHandle(canvas, cropRect.right, cropRect.bottom, isLeft = false, isTop = false) // 右下角

        // 🎯 绘制边缘短线段手柄
        val centerX = cropRect.centerX()
        val centerY = cropRect.centerY()
        val lineLength = handleLength * 1.5f  // 🎯 增大边缘手柄长度：0.6f → 1.2f

        // 上边缘手柄
        drawLineHandle(canvas, centerX - lineLength/2, cropRect.top, centerX + lineLength/2, cropRect.top)
        // 下边缘手柄
        drawLineHandle(canvas, centerX - lineLength/2, cropRect.bottom, centerX + lineLength/2, cropRect.bottom)
        // 左边缘手柄
        drawLineHandle(canvas, cropRect.left, centerY - lineLength/2, cropRect.left, centerY + lineLength/2)
        // 右边缘手柄
        drawLineHandle(canvas, cropRect.right, centerY - lineLength/2, cropRect.right, centerY + lineLength/2)
    }

    /**
     * 🎯 绘制L型手柄
     * @param canvas 画布
     * @param x 角落X坐标
     * @param y 角落Y坐标
     * @param isLeft 是否为左侧（true=左侧，false=右侧）
     * @param isTop 是否为顶部（true=顶部，false=底部）
     */
    private fun drawLShapeHandle(canvas: Canvas, x: Float, y: Float, isLeft: Boolean, isTop: Boolean) {
        val length = handleLength

        // 计算L型手柄的两条线段端点
        val horizontalEndX = if (isLeft) x + length else x - length
        val verticalEndY = if (isTop) y + length else y - length

        // 先绘制黑色边框（描边效果）
        canvas.drawLine(x, y, horizontalEndX, y, handleBorderPaint)  // 水平线
        canvas.drawLine(x, y, x, verticalEndY, handleBorderPaint)    // 垂直线

        // 再绘制白色手柄
        canvas.drawLine(x, y, horizontalEndX, y, handlePaint)  // 水平线
        canvas.drawLine(x, y, x, verticalEndY, handlePaint)    // 垂直线
    }

    /**
     * 🎯 绘制短线段手柄（边缘中心）
     */
    private fun drawLineHandle(canvas: Canvas, startX: Float, startY: Float, endX: Float, endY: Float) {
        // 先绘制黑色边框
        canvas.drawLine(startX, startY, endX, endY, handleBorderPaint)
        // 再绘制白色手柄
        canvas.drawLine(startX, startY, endX, endY, handlePaint)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                activeHandle = getHandleAt(event.x, event.y)
                lastTouchX = event.x
                lastTouchY = event.y
                return activeHandle != HandleType.NONE
            }
            
            MotionEvent.ACTION_MOVE -> {
                if (activeHandle != HandleType.NONE) {
                    val deltaX = event.x - lastTouchX
                    val deltaY = event.y - lastTouchY
                    
                    updateCropRect(activeHandle, deltaX, deltaY)
                    
                    lastTouchX = event.x
                    lastTouchY = event.y
                    invalidate()
                    
                    // 通知裁剪变化
                    cropChangeListener?.onCropChanged(cropRect)
                    return true
                }
            }
            
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                activeHandle = HandleType.NONE
                performClick()
                return true
            }
        }
        return super.onTouchEvent(event)
    }

    override fun performClick(): Boolean {
        super.performClick()
        return true
    }

    private fun getHandleAt(x: Float, y: Float): HandleType {
        val tolerance = handleSize / 2f  // 🎯 减小容差，更精确
        val lTolerance = handleLength + 10f  // 🎯 L型手柄专用容差

        // 🎯 检查L型角落手柄（优化检测区域）
        if (isInLShapeArea(x, y, cropRect.left, cropRect.top, isLeft = true, isTop = true, lTolerance)) {
            return HandleType.TOP_LEFT
        }
        if (isInLShapeArea(x, y, cropRect.right, cropRect.top, isLeft = false, isTop = true, lTolerance)) {
            return HandleType.TOP_RIGHT
        }
        if (isInLShapeArea(x, y, cropRect.left, cropRect.bottom, isLeft = true, isTop = false, lTolerance)) {
            return HandleType.BOTTOM_LEFT
        }
        if (isInLShapeArea(x, y, cropRect.right, cropRect.bottom, isLeft = false, isTop = false, lTolerance)) {
            return HandleType.BOTTOM_RIGHT
        }

        // 🎯 检查边框中心短线段手柄
        val centerX = cropRect.centerX()
        val centerY = cropRect.centerY()
        val lineLength = handleLength * 1.5f  // 🎯 保持与绘制时一致的长度

        // 上边缘手柄（水平线段）
        if (isNear(y, cropRect.top, tolerance) &&
            x >= centerX - lineLength/2 - tolerance && x <= centerX + lineLength/2 + tolerance) {
            return HandleType.TOP
        }
        // 下边缘手柄（水平线段）
        if (isNear(y, cropRect.bottom, tolerance) &&
            x >= centerX - lineLength/2 - tolerance && x <= centerX + lineLength/2 + tolerance) {
            return HandleType.BOTTOM
        }
        // 左边缘手柄（垂直线段）
        if (isNear(x, cropRect.left, tolerance) &&
            y >= centerY - lineLength/2 - tolerance && y <= centerY + lineLength/2 + tolerance) {
            return HandleType.LEFT
        }
        // 右边缘手柄（垂直线段）
        if (isNear(x, cropRect.right, tolerance) &&
            y >= centerY - lineLength/2 - tolerance && y <= centerY + lineLength/2 + tolerance) {
            return HandleType.RIGHT
        }

        // 检查是否在裁剪区域内（用于整体移动）
        if (cropRect.contains(x, y)) {
            return HandleType.CENTER
        }

        return HandleType.NONE
    }

    /**
     * 🎯 检查点是否在L型手柄区域内
     */
    private fun isInLShapeArea(x: Float, y: Float, cornerX: Float, cornerY: Float,
                              isLeft: Boolean, isTop: Boolean, tolerance: Float): Boolean {
        val length = handleLength

        // L型手柄的两条线段区域
        val horizontalEndX = if (isLeft) cornerX + length else cornerX - length
        val verticalEndY = if (isTop) cornerY + length else cornerY - length

        // 检查是否在水平线段附近
        val inHorizontalLine = isNear(y, cornerY, tolerance) &&
            if (isLeft) x >= cornerX - tolerance && x <= horizontalEndX + tolerance
            else x >= horizontalEndX - tolerance && x <= cornerX + tolerance

        // 检查是否在垂直线段附近
        val inVerticalLine = isNear(x, cornerX, tolerance) &&
            if (isTop) y >= cornerY - tolerance && y <= verticalEndY + tolerance
            else y >= verticalEndY - tolerance && y <= cornerY + tolerance

        return inHorizontalLine || inVerticalLine
    }

    private fun isNear(value1: Float, value2: Float, tolerance: Float): Boolean {
        return abs(value1 - value2) <= tolerance
    }

    private fun updateCropRect(handle: HandleType, deltaX: Float, deltaY: Float) {
        val newRect = RectF(cropRect)
        
        when (handle) {
            HandleType.TOP_LEFT -> {
                newRect.left += deltaX
                newRect.top += deltaY
            }
            HandleType.TOP_RIGHT -> {
                newRect.right += deltaX
                newRect.top += deltaY
            }
            HandleType.BOTTOM_LEFT -> {
                newRect.left += deltaX
                newRect.bottom += deltaY
            }
            HandleType.BOTTOM_RIGHT -> {
                newRect.right += deltaX
                newRect.bottom += deltaY
            }
            HandleType.TOP -> {
                newRect.top += deltaY
            }
            HandleType.BOTTOM -> {
                newRect.bottom += deltaY
            }
            HandleType.LEFT -> {
                newRect.left += deltaX
            }
            HandleType.RIGHT -> {
                newRect.right += deltaX
            }
            HandleType.CENTER -> {
                newRect.offset(deltaX, deltaY)
            }
            else -> return
        }
        
        // 确保裁剪区域在视图边界内且满足最小尺寸
        constrainRect(newRect)
        cropRect.set(newRect)
    }

    private fun constrainRect(rect: RectF) {
        // 🎯 修复边界约束逻辑：先约束边界，再检查最小尺寸

        // 第一步：约束边界到视图范围内（不移动对面边界）
        rect.left = rect.left.coerceAtLeast(0f)
        rect.top = rect.top.coerceAtLeast(0f)
        rect.right = rect.right.coerceAtMost(width.toFloat())
        rect.bottom = rect.bottom.coerceAtMost(height.toFloat())

        // 第二步：确保最小尺寸（只有在尺寸太小时才调整）
        val currentWidth = rect.width()
        val currentHeight = rect.height()

        if (currentWidth < minCropSize) {
            val center = rect.centerX()
            val halfMinSize = minCropSize / 2f

            // 尝试以中心点扩展
            var newLeft = center - halfMinSize
            var newRight = center + halfMinSize

            // 如果扩展后超出边界，则贴边调整
            if (newLeft < 0f) {
                newLeft = 0f
                newRight = minCropSize.coerceAtMost(width.toFloat())
            } else if (newRight > width) {
                newRight = width.toFloat()
                newLeft = (width - minCropSize).coerceAtLeast(0f)
            }

            rect.left = newLeft
            rect.right = newRight
        }

        if (currentHeight < minCropSize) {
            val center = rect.centerY()
            val halfMinSize = minCropSize / 2f

            // 尝试以中心点扩展
            var newTop = center - halfMinSize
            var newBottom = center + halfMinSize

            // 如果扩展后超出边界，则贴边调整
            if (newTop < 0f) {
                newTop = 0f
                newBottom = minCropSize.coerceAtMost(height.toFloat())
            } else if (newBottom > height) {
                newBottom = height.toFloat()
                newTop = (height - minCropSize).coerceAtLeast(0f)
            }

            rect.top = newTop
            rect.bottom = newBottom
        }
    }

    /**
     * 获取当前裁剪区域（相对于视图的比例）
     */
    fun getCropRectRatio(): RectF {
        return if (width > 0 && height > 0) {
            RectF(
                cropRect.left / width,
                cropRect.top / height,
                cropRect.right / width,
                cropRect.bottom / height
            )
        } else {
            RectF(0f, 0f, 1f, 1f)
        }
    }

    /**
     * 设置裁剪区域（使用相对比例）
     */
    fun setCropRectRatio(ratioRect: RectF) {
        if (width > 0 && height > 0) {
            // View尺寸已确定，直接设置
            cropRect.set(
                ratioRect.left * width,
                ratioRect.top * height,
                ratioRect.right * width,
                ratioRect.bottom * height
            )
            pendingCropRatio = null // 清除缓存
            invalidate()
            AppLog.d("直接设置裁剪区域: $cropRect")
        } else {
            // View尺寸未确定，缓存比例等待onSizeChanged时设置
            pendingCropRatio = RectF(ratioRect)
            AppLog.d("缓存裁剪比例，等待尺寸确定: $ratioRect")
        }
    }

    /**
     * 重置裁剪区域到全屏
     * @param notifyChange 是否通知裁剪变化，默认为true
     */
    fun resetCrop(notifyChange: Boolean = true) {
        cropRect.set(0f, 0f, width.toFloat(), height.toFloat())
        invalidate()
        if (notifyChange) {
            cropChangeListener?.onCropChanged(cropRect)
        }
    }
}
