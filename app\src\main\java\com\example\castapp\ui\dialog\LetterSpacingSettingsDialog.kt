package com.example.castapp.ui.dialog

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.LetterSpacingPresetManager

/**
 * 字间距设置对话框
 * 提供字间距管理功能，包括添加、删除、选择字间距
 */
class LetterSpacingSettingsDialog(
    private val context: Context,
    private val currentLetterSpacing: Float,
    private val existingLetterSpacings: List<Float>,
    private val onLetterSpacingSelected: (Float) -> Unit,
    private val onLetterSpacingAdded: ((Float) -> Unit)? = null,
    private val onLetterSpacingDeleted: ((Float) -> Unit)? = null,
    private val onResetToDefault: (() -> Unit)? = null
) {

    private var dialog: Dialog? = null
    private lateinit var recyclerView: RecyclerView
    private lateinit var letterSpacingAdapter: LetterSpacingAdapter
    private lateinit var btnClose: ImageView
    private lateinit var btnAddLetterSpacing: Button
    private lateinit var btnResetDefault: Button
    private lateinit var tvCurrentLetterSpacing: TextView
    private lateinit var etNewLetterSpacing: EditText

    // 预设字间距列表
    private val presetLetterSpacings = LetterSpacingPresetManager.PRESET_LETTER_SPACINGS

    // 字间距列表数据
    private val letterSpacingList = mutableListOf<LetterSpacingItem>()
    private var selectedLetterSpacing = currentLetterSpacing

    /**
     * 字间距项数据类
     */
    data class LetterSpacingItem(
        val letterSpacing: Float,
        val isPreset: Boolean,
        var isSelected: Boolean
    )

    /**
     * 显示对话框
     */
    fun show() {
        try {
            // 创建对话框
            val view = LayoutInflater.from(context).inflate(R.layout.dialog_letter_spacing_settings, null)

            dialog = Dialog(context)
            dialog?.setContentView(view)

            // 初始化视图
            initViews(view)
            initData()
            setupListeners()

            // 显示对话框
            dialog?.show()

            AppLog.d("【字间距设置对话框】对话框已显示")

        } catch (e: Exception) {
            AppLog.e("【字间距设置对话框】显示对话框失败", e)
        }
    }

    /**
     * 初始化视图
     */
    private fun initViews(view: View) {
        recyclerView = view.findViewById(R.id.rv_letter_spacing_list)
        btnClose = view.findViewById(R.id.btn_close)
        btnAddLetterSpacing = view.findViewById(R.id.btn_add_letter_spacing)
        btnResetDefault = view.findViewById(R.id.btn_reset_default)
        tvCurrentLetterSpacing = view.findViewById(R.id.tv_current_letter_spacing)
        etNewLetterSpacing = view.findViewById(R.id.et_new_letter_spacing)

        // 设置RecyclerView
        letterSpacingAdapter = LetterSpacingAdapter()
        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerView.adapter = letterSpacingAdapter

        AppLog.d("【字间距设置对话框】视图初始化完成")
    }

    /**
     * 初始化数据
     */
    private fun initData() {
        // 添加所有现有字间距（包括预设和自定义）
        existingLetterSpacings.forEach { letterSpacing ->
            val isPreset = presetLetterSpacings.contains(letterSpacing)
            val isSelected = letterSpacing == selectedLetterSpacing
            letterSpacingList.add(LetterSpacingItem(letterSpacing, isPreset, isSelected))
        }

        // 按字间距大小排序
        letterSpacingList.sortBy { it.letterSpacing }

        // 更新显示
        updateCurrentLetterSpacingDisplay()
        letterSpacingAdapter.notifyDataSetChanged()

        AppLog.d("【字间距设置对话框】初始化数据完成，共${letterSpacingList.size}个字间距")
    }

    /**
     * 设置监听器
     */
    private fun setupListeners() {
        // 关闭按钮
        btnClose.setOnClickListener {
            dialog?.dismiss()
        }

        // 添加字间距按钮
        btnAddLetterSpacing.setOnClickListener {
            addNewLetterSpacing()
        }

        // 恢复默认按钮
        btnResetDefault.setOnClickListener {
            resetToDefault()
        }
    }

    /**
     * 添加新字间距
     */
    private fun addNewLetterSpacing() {
        try {
            val input = etNewLetterSpacing.text.toString().trim()
            if (input.isEmpty()) {
                Toast.makeText(context, "请输入字间距值", Toast.LENGTH_SHORT).show()
                return
            }

            val letterSpacing = input.toFloatOrNull()
            if (letterSpacing == null) {
                Toast.makeText(context, "请输入有效的数字", Toast.LENGTH_SHORT).show()
                return
            }

            // 检查范围
            if (letterSpacing < -0.5f || letterSpacing > 2.0f) {
                Toast.makeText(context, "字间距范围应在-0.5到2.0之间", Toast.LENGTH_SHORT).show()
                return
            }

            // 检查是否已存在
            if (letterSpacingList.any { it.letterSpacing == letterSpacing }) {
                Toast.makeText(context, "该字间距已存在", Toast.LENGTH_SHORT).show()
                return
            }

            // 添加到列表
            val isPreset = presetLetterSpacings.contains(letterSpacing)
            letterSpacingList.add(LetterSpacingItem(letterSpacing, isPreset, false))
            letterSpacingList.sortBy { it.letterSpacing }

            // 通知适配器更新
            letterSpacingAdapter.notifyDataSetChanged()

            // 清空输入框
            etNewLetterSpacing.setText("")

            // 回调通知
            onLetterSpacingAdded?.invoke(letterSpacing)

            Toast.makeText(context, "字间距${letterSpacing}em已添加", Toast.LENGTH_SHORT).show()
            AppLog.d("【字间距设置对话框】已添加新字间距: ${letterSpacing}em")

        } catch (e: Exception) {
            AppLog.e("【字间距设置对话框】添加字间距失败", e)
            Toast.makeText(context, "添加失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 选择字间距
     */
    private fun selectLetterSpacing(letterSpacing: Float) {
        try {
            // 更新选中状态
            letterSpacingList.forEach { it.isSelected = (it.letterSpacing == letterSpacing) }
            selectedLetterSpacing = letterSpacing

            // 更新显示
            updateCurrentLetterSpacingDisplay()
            letterSpacingAdapter.notifyDataSetChanged()

            // 回调通知
            onLetterSpacingSelected(letterSpacing)

            AppLog.d("【字间距设置对话框】已选择字间距: ${letterSpacing}em")

        } catch (e: Exception) {
            AppLog.e("【字间距设置对话框】选择字间距失败", e)
        }
    }

    /**
     * 删除字间距
     */
    private fun deleteLetterSpacing(letterSpacing: Float, position: Int) {
        try {
            // 检查是否为预设字间距
            if (presetLetterSpacings.contains(letterSpacing)) {
                Toast.makeText(context, "无法删除预设字间距", Toast.LENGTH_SHORT).show()
                return
            }

            // 从列表中删除
            letterSpacingList.removeAt(position)
            letterSpacingAdapter.notifyItemRemoved(position)

            // 如果删除的是当前选中的字间距，选择默认字间距
            if (letterSpacing == selectedLetterSpacing) {
                selectLetterSpacing(0.0f) // 选择默认字间距
            }

            // 回调通知
            onLetterSpacingDeleted?.invoke(letterSpacing)

            Toast.makeText(context, "字间距${letterSpacing}em已删除", Toast.LENGTH_SHORT).show()
            AppLog.d("【字间距设置对话框】已删除字间距: ${letterSpacing}em")

        } catch (e: Exception) {
            AppLog.e("【字间距设置对话框】删除字间距失败", e)
        }
    }

    /**
     * 重置为默认设置
     */
    private fun resetToDefault() {
        try {
            // 移除所有自定义字间距
            letterSpacingList.removeAll { !it.isPreset }

            // 重置选择为默认字间距
            selectLetterSpacing(0.0f)

            // 通知适配器更新
            letterSpacingAdapter.notifyDataSetChanged()

            // 回调通知
            onResetToDefault?.invoke()

            Toast.makeText(context, "已重置为默认字间距设置", Toast.LENGTH_SHORT).show()
            AppLog.d("【字间距设置对话框】已重置为默认设置")

        } catch (e: Exception) {
            AppLog.e("【字间距设置对话框】重置默认设置失败", e)
        }
    }

    /**
     * 更新当前字间距显示
     */
    private fun updateCurrentLetterSpacingDisplay() {
        tvCurrentLetterSpacing.text = "当前字间距: ${selectedLetterSpacing}em"
    }

    /**
     * 字间距适配器
     */
    private inner class LetterSpacingAdapter : RecyclerView.Adapter<LetterSpacingAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(context).inflate(R.layout.item_letter_spacing, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.bind(letterSpacingList[position])
        }

        override fun getItemCount(): Int = letterSpacingList.size

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            private val tvLetterSpacing: TextView = itemView.findViewById(R.id.tv_letter_spacing)
            private val tvPresetTag: TextView = itemView.findViewById(R.id.tv_preset_tag)
            private val ivCurrentIndicator: ImageView = itemView.findViewById(R.id.iv_current_indicator)
            private val btnDelete: ImageView = itemView.findViewById(R.id.btn_delete)

            fun bind(item: LetterSpacingItem) {
                tvLetterSpacing.text = "${item.letterSpacing}em"

                // 显示预设标识
                tvPresetTag.visibility = if (item.isPreset) View.VISIBLE else View.GONE

                // 显示当前选中标识
                ivCurrentIndicator.visibility = if (item.isSelected) View.VISIBLE else View.GONE

                // 显示删除按钮（只有自定义字间距才显示）
                btnDelete.visibility = if (!item.isPreset) View.VISIBLE else View.GONE

                // 设置背景色
                itemView.setBackgroundColor(
                    if (item.isSelected) 0x20_4CAF50 else 0x00_000000
                )

                // 点击选择字间距
                itemView.setOnClickListener {
                    selectLetterSpacing(item.letterSpacing)
                }

                // 删除按钮点击
                btnDelete.setOnClickListener {
                    deleteLetterSpacing(item.letterSpacing, bindingAdapterPosition)
                }

                // 长按删除（自定义字间距）
                if (!item.isPreset) {
                    itemView.setOnLongClickListener {
                        deleteLetterSpacing(item.letterSpacing, bindingAdapterPosition)
                        true
                    }
                }
            }
        }
    }
}
