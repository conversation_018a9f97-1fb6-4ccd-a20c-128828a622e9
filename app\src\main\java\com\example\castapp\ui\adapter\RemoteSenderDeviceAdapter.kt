package com.example.castapp.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.model.RemoteSenderConnection

/**
 * 远程连接发送端设备列表适配器
 */
class RemoteSenderDeviceAdapter(
    private val connections: MutableList<RemoteSenderConnection>,
    private val onConnectClick: (RemoteSenderConnection) -> Unit,
    private val onControlClick: (RemoteSenderConnection) -> Unit,
    private val onEditClick: (RemoteSenderConnection) -> Unit,
    private val onDeleteClick: (RemoteSenderConnection) -> Unit
) : RecyclerView.Adapter<RemoteSenderDeviceAdapter.ViewHolder>() {

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val deviceIcon: ImageView = itemView.findViewById(R.id.device_icon)
        val deviceNameText: TextView = itemView.findViewById(R.id.device_name_text)
        val deviceAddressText: TextView = itemView.findViewById(R.id.device_address_text)
        val connectionStatusText: TextView = itemView.findViewById(R.id.connection_status_text)
        val connectButton: Button = itemView.findViewById(R.id.connect_button)
        val controlButton: Button = itemView.findViewById(R.id.control_button)
        val editButton: Button = itemView.findViewById(R.id.edit_button)
        val deleteButton: Button = itemView.findViewById(R.id.delete_button)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_remote_connection, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val connection = connections[position]
        
        // 设置设备信息
        holder.deviceNameText.text = connection.deviceName
        holder.deviceAddressText.text = connection.getDisplayText()

        // 设置设备图标颜色 - 连接成功时变为绿色
        holder.deviceIcon.imageTintList = holder.itemView.context.getColorStateList(
            if (connection.isConnected) android.R.color.holo_green_light
            else android.R.color.darker_gray
        )

        // 设置连接状态
        holder.connectionStatusText.text = connection.getStatusText()
        holder.connectionStatusText.setTextColor(
            if (connection.isConnected) {
                holder.itemView.context.getColor(android.R.color.holo_green_dark)
            } else {
                holder.itemView.context.getColor(android.R.color.darker_gray)
            }
        )
        
        // 设置按钮状态
        holder.connectButton.text = if (connection.isConnected) "断开" else "连接"
        holder.connectButton.backgroundTintList = holder.itemView.context.getColorStateList(
            if (connection.isConnected) android.R.color.holo_red_light 
            else android.R.color.holo_green_light
        )
        
        // 控制按钮只有在连接时才可用
        holder.controlButton.isEnabled = connection.isConnected
        holder.controlButton.alpha = if (connection.isConnected) 1.0f else 0.5f

        // 编辑按钮在已连接时禁用，防止编辑活跃连接
        holder.editButton.isEnabled = !connection.isConnected
        holder.editButton.alpha = if (connection.isConnected) 0.5f else 1.0f

        // 删除按钮在已连接时禁用，防止意外删除活跃连接
        holder.deleteButton.isEnabled = !connection.isConnected
        holder.deleteButton.alpha = if (connection.isConnected) 0.5f else 1.0f

        // 设置点击事件
        holder.connectButton.setOnClickListener {
            onConnectClick(connection)
        }

        holder.controlButton.setOnClickListener {
            if (connection.isConnected) {
                onControlClick(connection)
            }
        }

        holder.editButton.setOnClickListener {
            if (!connection.isConnected) {
                onEditClick(connection)
            }
        }

        holder.deleteButton.setOnClickListener {
            if (!connection.isConnected) {
                onDeleteClick(connection)
            }
        }
    }

    override fun getItemCount(): Int = connections.size

    /**
     * 更新连接状态
     */
    fun updateConnection(updatedConnection: RemoteSenderConnection) {
        val index = connections.indexOfFirst { it.id == updatedConnection.id }
        if (index != -1) {
            connections[index] = updatedConnection
            notifyItemChanged(index)
        }
    }

    /**
     * 添加连接
     */
    fun addConnection(connection: RemoteSenderConnection) {
        connections.add(connection)
        notifyItemInserted(connections.size - 1)
    }

    /**
     * 删除连接
     */
    fun removeConnection(connection: RemoteSenderConnection) {
        val index = connections.indexOfFirst { it.id == connection.id }
        if (index != -1) {
            connections.removeAt(index)
            notifyItemRemoved(index)
        }
    }
}
