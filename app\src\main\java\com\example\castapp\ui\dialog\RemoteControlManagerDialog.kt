package com.example.castapp.ui.dialog

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.Toast
import androidx.core.content.edit
import androidx.fragment.app.DialogFragment
import androidx.viewpager2.widget.ViewPager2
import com.example.castapp.R
import com.example.castapp.ui.adapter.RemoteTabPagerAdapter
import com.example.castapp.model.RemoteSenderConnection
import com.example.castapp.model.RemoteReceiverConnection
import com.example.castapp.manager.RemoteConnectionManager
import com.example.castapp.manager.RemoteSenderManager
import com.example.castapp.manager.RemoteReceiverManager
import com.example.castapp.utils.AppLog
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 🐾 遥控管理对话框 - 精简版
 *
 * 重构后的职责：
 * - UI初始化和Fragment管理
 * - 事件分发到对应的管理器
 * - 对话框生命周期管理
 *
 * 业务逻辑已分离到：
 * - RemoteConnectionManager: 全局连接状态管理
 * - RemoteSenderManager: 发送端业务逻辑
 * - RemoteReceiverManager: 接收端业务逻辑
 */
class RemoteControlManagerDialog : DialogFragment() {

    // UI组件
    private lateinit var closeButton: ImageButton
    private lateinit var tabLayout: TabLayout
    private lateinit var viewPager: ViewPager2
    private lateinit var tabPagerAdapter: RemoteTabPagerAdapter

    // 管理器实例
    private val connectionManager = RemoteConnectionManager.getInstance()
    private val senderManager = RemoteSenderManager()
    private val receiverManager = RemoteReceiverManager.getInstance()

    // 本地数据（用于UI显示）
    private val connections = mutableListOf<RemoteSenderConnection>()
    private val receivers = mutableListOf<RemoteReceiverConnection>()
    private val gson = Gson()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.DialogTheme)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_remote_control_manager, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setupTabLayout()
        setupClickListeners()

        // 延迟加载数据，确保Fragment完全初始化
        viewPager.post {
            loadConnections()
            loadReceivers()
        }
    }

    private fun initViews(view: View) {
        closeButton = view.findViewById(R.id.close_dialog_button)
        tabLayout = view.findViewById(R.id.tab_layout)
        viewPager = view.findViewById(R.id.view_pager)
    }

    private fun setupTabLayout() {
        // 创建标签页适配器
        tabPagerAdapter = RemoteTabPagerAdapter(childFragmentManager, lifecycle)
        viewPager.adapter = tabPagerAdapter

        // 连接TabLayout和ViewPager2
        TabLayoutMediator(tabLayout, viewPager) { tab, position ->
            tab.text = tabPagerAdapter.getTabTitle(position)
        }.attach()

        // 延迟设置Fragment回调，确保ViewPager2完全初始化
        viewPager.post {
            setupFragmentCallbacks()
        }
    }

    private fun setupFragmentCallbacks() {
        // 设置发送端Fragment的回调
        tabPagerAdapter.getSenderFragment().apply {
            setOnConnectClickListener { connection -> handleConnectClick(connection) }
            setOnControlClickListener { connection -> handleControlClick(connection) }
            setOnEditClickListener { connection -> handleEditClick(connection) }
            setOnDeleteClickListener { connection -> handleDeleteClick(connection) }
            setOnAddConnectionClickListener { showAddConnectionDialog() }
        }

        // 设置接收端Fragment的回调
        tabPagerAdapter.getReceiverFragment().apply {
            setOnConnectClickListener { receiver -> handleReceiverConnectClick(receiver) }
            setOnControlClickListener { receiver -> handleReceiverControlClick(receiver) }
            setOnEditClickListener { receiver -> handleReceiverEditClick(receiver) }
            setOnDeleteClickListener { receiver -> handleReceiverDeleteClick(receiver) }
            setOnAddReceiverClickListener { showAddReceiverDialog() }
        }
    }

    private fun setupClickListeners() {
        closeButton.setOnClickListener {
            dismiss()
        }
    }

    private fun handleConnectClick(connection: RemoteSenderConnection) {
        if (connection.isConnected) {
            // 断开连接
            senderManager.disconnectFromRemoteDevice(connection, requireContext()) { updatedConnection, _ ->
                activity?.runOnUiThread {
                    updateConnectionInUI(updatedConnection)
                }
            }
        } else {
            // 建立连接
            senderManager.connectToRemoteDevice(connection, requireContext()) { updatedConnection, _ ->
                activity?.runOnUiThread {
                    updateConnectionInUI(updatedConnection)
                }
            }
        }
    }

    private fun handleControlClick(connection: RemoteSenderConnection) {
        if (connection.isConnected) {
            senderManager.showRemoteSenderControlDialog(connection, parentFragmentManager, requireContext())
        } else {
            Toast.makeText(requireContext(), "请先连接到设备", Toast.LENGTH_SHORT).show()
        }
    }

    private fun handleEditClick(connection: RemoteSenderConnection) {
        val editDialog = EditRemoteSenderDeviceDialog(connection) { connectionId, newIpAddress, newDeviceName ->
            // 更新连接信息，固定使用9999端口
            val updatedConnection = connection.copy(
                ipAddress = newIpAddress,
                port = 9999,
                deviceName = newDeviceName
            )

            // 🐾 更新全局状态副本
            connectionManager.updateGlobalConnectionState(updatedConnection)

            // 更新Fragment中的连接
            updateConnectionInUI(updatedConnection)

            // 保存到本地存储
            saveConnections()

            Toast.makeText(requireContext(), "设备信息已更新", Toast.LENGTH_SHORT).show()
            AppLog.d("编辑远程连接: ${updatedConnection.getDisplayText()}")
        }
        editDialog.show(parentFragmentManager, EditRemoteSenderDeviceDialog.TAG)
    }

    private fun handleDeleteClick(connection: RemoteSenderConnection) {
        // 如果已连接，先断开
        if (connection.isConnected) {
            senderManager.disconnectFromRemoteDevice(connection, requireContext())
        }

        // 删除连接
        tabPagerAdapter.getSenderFragment().removeConnection(connection)
        connections.removeAll { it.id == connection.id }

        // 🐾 从全局状态中移除
        connectionManager.removeGlobalConnectionState(connection.id)

        saveConnections()

        Toast.makeText(requireContext(), "已删除连接：${connection.deviceName}", Toast.LENGTH_SHORT).show()
        AppLog.d("删除远程连接: ${connection.getDisplayText()}")
    }



    private fun showAddConnectionDialog() {
        val dialog = AddRemoteSenderDeviceDialog { ipAddress, deviceName ->
            val newConnection = RemoteSenderConnection.create(ipAddress, 9999, deviceName)

            // 🐾 添加到全局状态管理器
            connectionManager.updateGlobalConnectionState(newConnection)

            tabPagerAdapter.getSenderFragment().addConnection(newConnection)
            connections.add(newConnection)
            saveConnections()

            Toast.makeText(requireContext(), "已添加设备：$deviceName", Toast.LENGTH_SHORT).show()
            AppLog.d("添加新的远程连接: ${newConnection.getDisplayText()}")
        }
        dialog.show(parentFragmentManager, AddRemoteSenderDeviceDialog.TAG)
    }

    // ========== 接收端相关方法 ==========

    /**
     * 注册接收端控制对话框，用于接收连接状态变化通知
     */
    fun registerReceiverControlDialog(receiverId: String, dialog: RemoteReceiverControlDialog) {
        receiverManager.registerReceiverControlDialog(receiverId, dialog)
    }

    /**
     * 注销接收端控制对话框
     */
    fun unregisterReceiverControlDialog(receiverId: String) {
        receiverManager.unregisterReceiverControlDialog(receiverId)
    }

    private fun handleReceiverConnectClick(receiver: RemoteReceiverConnection) {
        if (receiver.isConnected) {
            // 断开连接
            receiverManager.disconnectReceiver(receiver, requireContext()) { updatedReceiver, _ ->
                activity?.runOnUiThread {
                    updateReceiverInUI(updatedReceiver)
                }
            }
        } else {
            // 建立连接
            receiverManager.connectReceiver(receiver, requireContext()) { updatedReceiver, _ ->
                activity?.runOnUiThread {
                    updateReceiverInUI(updatedReceiver)
                }
            }
        }
    }

    private fun handleReceiverControlClick(receiver: RemoteReceiverConnection) {
        if (!receiver.isConnected) {
            Toast.makeText(requireContext(), "设备未连接，无法进行控制", Toast.LENGTH_SHORT).show()
            return
        }

        // 📐 使用缓存的分辨率信息直接显示控制对话框
        val cachedResolution = receiverManager.getCachedScreenResolution(receiver.id)
        if (cachedResolution != null) {
            val updatedReceiver = receiver.withScreenResolution(cachedResolution.first, cachedResolution.second)
            AppLog.d("📐 使用缓存的屏幕分辨率: ${receiver.deviceName} -> ${cachedResolution.first}×${cachedResolution.second}")
            showReceiverControlDialog(updatedReceiver)
        } else {
            // 理论上不应该到达这里，因为连接时已经自动缓存了分辨率
            AppLog.w("📐 警告：没有找到缓存的分辨率信息，这不应该发生: ${receiver.deviceName}")
            Toast.makeText(requireContext(), "分辨率信息缺失，请重新连接设备", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 📐 直接显示远程控制对话框
     */
    private fun showReceiverControlDialog(receiver: RemoteReceiverConnection) {
        try {
            val controlDialog = RemoteReceiverControlDialog.newInstance(receiver)
            controlDialog.show(parentFragmentManager, "RemoteReceiverControlDialog")
            AppLog.d("📐 直接显示远程控制对话框: ${receiver.deviceName}")
        } catch (e: Exception) {
            AppLog.e("📐 显示远程控制对话框失败", e)
            Toast.makeText(requireContext(), "打开控制窗口失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }







    private fun handleReceiverEditClick(receiver: RemoteReceiverConnection) {
        val dialog = EditRemoteReceiverDeviceDialog(receiver) { receiverId, ipAddress, deviceName ->
            val updatedReceiver = receiver.copy(
                ipAddress = ipAddress,
                deviceName = deviceName
            )
            updateReceiverInUI(updatedReceiver)
            saveReceivers()

            Toast.makeText(requireContext(), "已更新设备：$deviceName", Toast.LENGTH_SHORT).show()
            AppLog.d("编辑接收端: ${updatedReceiver.getDisplayText()}")
        }
        dialog.show(parentFragmentManager, "EditRemoteReceiverDialog")
    }

    private fun handleReceiverDeleteClick(receiver: RemoteReceiverConnection) {
        tabPagerAdapter.getReceiverFragment().removeReceiver(receiver)
        receivers.removeAll { it.id == receiver.id }
        saveReceivers()

        Toast.makeText(requireContext(), "已删除设备：${receiver.deviceName}", Toast.LENGTH_SHORT).show()
        AppLog.d("删除接收端: ${receiver.getDisplayText()}")
    }

    private fun showAddReceiverDialog() {
        val dialog = AddRemoteReceiverDeviceDialog { ipAddress, deviceName ->
            val newReceiver = RemoteReceiverConnection.create(ipAddress, deviceName)
            tabPagerAdapter.getReceiverFragment().addReceiver(newReceiver)
            receivers.add(newReceiver)
            saveReceivers()

            Toast.makeText(requireContext(), "已添加设备：$deviceName", Toast.LENGTH_SHORT).show()
            AppLog.d("添加新的接收端: ${newReceiver.getDisplayText()}")
        }
        dialog.show(parentFragmentManager, "AddRemoteReceiverDialog")
    }

    private fun loadConnections() {
        // 🐾 先初始化全局连接状态
        connectionManager.initializeGlobalConnectionStates(requireContext())

        val sharedPrefs = requireContext().getSharedPreferences("remote_control_settings", Context.MODE_PRIVATE)
        val connectionsJson = sharedPrefs.getString("remote_connections", "[]")

        try {
            val type = object : TypeToken<List<RemoteSenderConnection>>() {}.type
            val savedConnections: List<RemoteSenderConnection> = gson.fromJson(connectionsJson, type) ?: emptyList()

            connections.clear()

            // 🐾 优先使用全局状态副本，然后与WebSocket状态对比验证
            val globalStates = connectionManager.getGlobalConnectionStates().associateBy { it.id }
            val actualConnectionStates = connectionManager.getAllRemoteConnectionStates()

            val syncedConnections = savedConnections.map { connection ->
                // 优先使用全局状态副本中的连接状态
                val globalState = globalStates[connection.id]
                val globalConnectionState = globalState?.isConnected ?: false

                // 验证WebSocket客户端是否真实存在且连接
                val hasActiveWebSocket = actualConnectionStates.containsKey(connection.id)
                val webSocketConnected = if (hasActiveWebSocket) {
                    connectionManager.getRemoteClient(connection.id)?.getConnectionStatus() ?: false
                } else {
                    false
                }

                // 🐾 关键逻辑：只有当全局状态为已连接且WebSocket确实连接时，才认为是已连接
                val finalConnectionState = globalConnectionState && webSocketConnected
                val syncedConnection = connection.withConnectionState(finalConnectionState)

                // 🐾 更新全局状态副本为最终状态
                connectionManager.updateGlobalConnectionState(syncedConnection)

                AppLog.d("🔄 同步连接状态: ${connection.deviceName} -> 全局:$globalConnectionState, WebSocket:$webSocketConnected, 最终:$finalConnectionState")
                syncedConnection
            }

            connections.addAll(syncedConnections)

            // 🐾 立即保存同步后的状态
            connectionManager.saveConnectionsToPreferences(requireContext())

            // 更新发送端Fragment
            tabPagerAdapter.getSenderFragment().updateConnections(connections)

            AppLog.d("✅ 加载了 ${connections.size} 个远程连接，其中 ${actualConnectionStates.size} 个处于连接状态")
        } catch (e: Exception) {
            AppLog.e("❌ 加载远程连接失败", e)
        }
    }

    private fun loadReceivers() {
        val sharedPrefs = requireContext().getSharedPreferences("remote_control_settings", Context.MODE_PRIVATE)
        val receiversJson = sharedPrefs.getString("remote_receivers", "[]")

        try {
            val type = object : TypeToken<List<RemoteReceiverConnection>>() {}.type
            val savedReceivers: List<RemoteReceiverConnection> = gson.fromJson(receiversJson, type) ?: emptyList()

            receivers.clear()
            receivers.addAll(savedReceivers)

            // 解决对话框重新创建时状态不同步的问题
            var hasStateChanged = false
            for (i in receivers.indices) {
                val receiver = receivers[i]
                val client = connectionManager.getReceiverClient(receiver.id)
                val actualConnectionState = client?.getConnectionStatus() ?: false

                if (receiver.isConnected != actualConnectionState) {
                    receivers[i] = receiver.withConnectionState(actualConnectionState)
                    hasStateChanged = true
                    AppLog.d("🔄 同步接收端连接状态: ${receiver.deviceName} -> $actualConnectionState")
                }
            }

            // 如果状态有变化，重新保存到SharedPreferences
            if (hasStateChanged) {
                saveReceivers()
                AppLog.d("✅ 接收端状态同步完成，已更新SharedPreferences")
            }

            // 更新接收端Fragment
            tabPagerAdapter.getReceiverFragment().updateReceivers(receivers)

            AppLog.d("加载了 ${receivers.size} 个接收端设备，状态同步完成")
        } catch (e: Exception) {
            AppLog.e("加载接收端设备失败", e)
        }
    }

    private fun saveConnections() {
        try {
            // 🐾 使用全局状态管理器保存
            connectionManager.saveConnectionsToPreferences(requireContext())
        } catch (_: Exception) {
            // 🐾 备用方案：尝试从Fragment获取数据
            try {
                val sharedPrefs = requireContext().getSharedPreferences("remote_control_settings", Context.MODE_PRIVATE)
                val connectionsJson = gson.toJson(tabPagerAdapter.getSenderFragment().getAllConnections())

                sharedPrefs.edit {
                    putString("remote_connections", connectionsJson)
                }

                AppLog.d("⚠️ 使用备用方案保存了 ${tabPagerAdapter.getSenderFragment().getAllConnections().size} 个远程连接")
            } catch (fallbackException: Exception) {
                AppLog.e("❌ 保存连接失败，Fragment可能已销毁", fallbackException)
            }
        }
    }

    private fun saveReceivers() {
        val sharedPrefs = requireContext().getSharedPreferences("remote_control_settings", Context.MODE_PRIVATE)
        val receiversJson = gson.toJson(receivers)

        sharedPrefs.edit {
            putString("remote_receivers", receiversJson)
        }

        AppLog.d("保存了 ${receivers.size} 个接收端设备")
    }

    // ========== UI更新辅助方法 ==========

    /**
     * 更新连接在UI中的显示
     */
    private fun updateConnectionInUI(updatedConnection: RemoteSenderConnection) {
        try {
            tabPagerAdapter.getSenderFragment().updateConnection(updatedConnection)
            // 更新本地列表
            val index = connections.indexOfFirst { it.id == updatedConnection.id }
            if (index != -1) {
                connections[index] = updatedConnection
            }
        } catch (e: Exception) {
            AppLog.w("更新连接UI失败，Fragment可能已销毁", e)
        }
    }

    /**
     * 更新接收端在UI中的显示
     */
    private fun updateReceiverInUI(updatedReceiver: RemoteReceiverConnection) {
        try {
            tabPagerAdapter.getReceiverFragment().updateReceiver(updatedReceiver)
            // 更新本地列表
            val index = receivers.indexOfFirst { it.id == updatedReceiver.id }
            if (index != -1) {
                receivers[index] = updatedReceiver
            }
        } catch (e: Exception) {
            AppLog.w("更新接收端UI失败，Fragment可能已销毁", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // 🐾 对话框销毁时主动保存当前状态
        try {
            // 确保当前连接状态同步到全局状态管理器
            connections.forEach { connection ->
                connectionManager.updateGlobalConnectionState(connection)
            }

            // 立即保存状态到SharedPreferences
            connectionManager.saveConnectionsToPreferences(requireContext())
            AppLog.d("✅ 对话框销毁时已保存连接状态")
        } catch (e: Exception) {
            AppLog.e("❌ 对话框销毁时保存状态失败", e)
        }

        AppLog.d("🐾 遥控管理对话框销毁，WebSocket连接由全局管理器维护")

        val activeConnections = connectionManager.getAllRemoteConnectionStates().size
        AppLog.d("遥控管理对话框已销毁，${activeConnections} 个WebSocket连接继续在全局管理器中保持")
    }
}