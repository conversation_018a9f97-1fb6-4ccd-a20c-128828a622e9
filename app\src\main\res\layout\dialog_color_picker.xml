<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="6dp">

    <!-- 紧凑标题 -->
    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🎨 边框颜色"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <!-- 色轮和自定义色板水平布局 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <!-- 左侧：HSV色轮选择器 -->
        <com.skydoves.colorpickerview.ColorPickerView
            android:id="@+id/colorPickerView"
            android:layout_width="180dp"
            android:layout_height="180dp"
            android:layout_marginEnd="8dp"
            app:palette="@drawable/palette"
            app:selector="@drawable/wheel" />

        <!-- 右侧：自定义色板区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"

            android:background="#10E1E1E1"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_custom_palette"
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp"
                android:gravity="end">

                <Button
                    android:id="@+id/btn_delete_color"
                    android:layout_width="40dp"
                    android:layout_height="22dp"
                    android:text="删除"
                    android:textSize="9sp"
                    android:background="@drawable/button_cancel_background"
                    android:textColor="#666666"
                    android:layout_marginEnd="8dp" />

                <Button
                    android:id="@+id/btn_save_color"
                    android:layout_width="40dp"
                    android:layout_height="22dp"
                    android:text="保存"
                    android:textSize="9sp"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/button_background"
                    android:textColor="#FFFFFF"/>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>



    <!-- 紧凑型颜色预览和数值输入区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <!-- 颜色预览 -->
        <View
            android:id="@+id/view_color_preview"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/color_circle_background" />

        <!-- 颜色值显示和输入 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 显示区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/tv_hex_value"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="#6B6B6B"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/black" />

                <TextView
                    android:id="@+id/tv_rgb_value"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="RGB(107, 107, 107)"
                    android:textSize="10sp"
                    android:textColor="@android:color/darker_gray" />

            </LinearLayout>

            <!-- HEX输入 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="3dp">

                <TextView
                    android:layout_width="35dp"
                    android:layout_height="wrap_content"
                    android:text="HEX:"
                    android:textSize="10sp"
                    android:textColor="@android:color/black" />

                <EditText
                    android:id="@+id/et_hex_input"
                    android:layout_width="0dp"
                    android:layout_height="28dp"
                    android:layout_weight="1"
                    android:hint="#AARRGGBB"
                    android:textSize="10sp"
                    android:padding="3dp"
                    android:background="@drawable/edittext_background"
                    android:inputType="text"
                    android:maxLength="9" />

            </LinearLayout>

            <!-- RGB输入 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="35dp"
                    android:layout_height="wrap_content"
                    android:text="RGB:"
                    android:textSize="10sp"
                    android:textColor="@android:color/black" />

                <EditText
                    android:id="@+id/et_r_input"
                    android:layout_width="0dp"
                    android:layout_height="28dp"
                    android:layout_weight="1"
                    android:hint="R"
                    android:textSize="10sp"
                    android:padding="3dp"
                    android:background="@drawable/edittext_background"
                    android:inputType="number"
                    android:maxLength="3"
                    android:layout_marginEnd="2dp" />

                <EditText
                    android:id="@+id/et_g_input"
                    android:layout_width="0dp"
                    android:layout_height="28dp"
                    android:layout_weight="1"
                    android:hint="G"
                    android:textSize="10sp"
                    android:padding="3dp"
                    android:background="@drawable/edittext_background"
                    android:inputType="number"
                    android:maxLength="3"
                    android:layout_marginEnd="2dp" />

                <EditText
                    android:id="@+id/et_b_input"
                    android:layout_width="0dp"
                    android:layout_height="28dp"
                    android:layout_weight="1"
                    android:hint="B"
                    android:textSize="10sp"
                    android:padding="3dp"
                    android:background="@drawable/edittext_background"
                    android:inputType="number"
                    android:maxLength="3" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- 透明度控制区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🔍 透明度:"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginEnd="8dp" />

        <SeekBar
            android:id="@+id/seekbar_alpha"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:max="100"
            android:progress="100"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/tv_alpha_value"
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            android:text="100%"
            android:textSize="11sp"
            android:textColor="@android:color/black"
            android:gravity="center" />

    </LinearLayout>



    <!-- 描边控制区域（仅在描边模式下显示） -->
    <LinearLayout
        android:id="@+id/stroke_control_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="8dp"
        android:visibility="gone">

        <!-- 描边开关 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="启用描边："
                android:textSize="12sp"
                android:textColor="@android:color/black" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_stroke_enabled"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- 描边宽度 -->
        <LinearLayout
            android:id="@+id/stroke_width_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:enabled="false">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="描边宽度："
                android:textSize="12sp"
                android:textColor="@android:color/black" />

            <EditText
                android:id="@+id/et_stroke_width"
                android:layout_width="60dp"
                android:layout_height="32dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="4dp"
                android:text="2.0"
                android:textSize="12sp"
                android:inputType="numberDecimal"
                android:background="@drawable/edittext_background"
                android:padding="4dp"
                android:gravity="center" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="px"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray" />

        </LinearLayout>

    </LinearLayout>

    <!-- 窗口颜色控制区域（仅在窗口颜色模式下显示） -->
    <LinearLayout
        android:id="@+id/window_color_control_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="8dp"
        android:visibility="gone">

        <!-- 窗口颜色开关 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="启用窗口颜色："
                android:textSize="12sp"
                android:textColor="@android:color/black" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_window_color_enabled"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 紧凑型按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="55dp"
            android:layout_height="30dp"
            android:text="取消"
            android:textSize="11sp"
            android:background="@drawable/button_cancel_background"
            android:textColor="#666666"
            android:layout_marginEnd="6dp" />
        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="55dp"
            android:layout_height="30dp"
            android:text="确定"
            android:textSize="11sp"
            android:background="@drawable/button_background"
            android:textColor="#FFFFFF" />

    </LinearLayout>

</LinearLayout>

</ScrollView>
