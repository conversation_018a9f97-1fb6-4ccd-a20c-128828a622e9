package com.example.castapp.ui.dialog

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.ToastUtils

/**
 * 字号设置对话框
 * 提供字号管理功能，包括添加、删除、选择字号
 */
class FontSizeSettingsDialog(
    private val context: Context,
    private val currentFontSize: Int,
    private val existingFontSizes: List<Int>,
    private val onFontSizeSelected: (Int) -> Unit,
    private val onFontSizeAdded: ((Int) -> Unit)? = null,
    private val onFontSizeDeleted: ((Int) -> Unit)? = null,
    private val onResetToDefault: (() -> Unit)? = null
) {
    
    private var dialog: AlertDialog? = null
    private lateinit var tvCurrentFontSize: TextView
    private lateinit var rvFontSizes: RecyclerView
    private lateinit var etNewFontSize: EditText
    private lateinit var btnAddFontSize: Button
    private lateinit var btnResetDefault: Button
    private lateinit var btnClose: ImageView
    
    // 字号数据
    private val fontSizeList = mutableListOf<FontSizeItem>()
    private lateinit var fontSizeAdapter: FontSizeAdapter
    private var selectedFontSize = currentFontSize
    
    // 预设字号
    private val presetFontSizes = listOf(10, 11, 12, 13, 14)
    
    // 字号范围
    private val minFontSize = 4
    private val maxFontSize = 90
    
    /**
     * 字号数据项
     */
    data class FontSizeItem(
        val fontSize: Int,
        val isPreset: Boolean,
        var isSelected: Boolean = false
    )
    
    /**
     * 显示对话框
     */
    fun show() {
        try {
            val view = LayoutInflater.from(context).inflate(R.layout.dialog_font_size_settings, null)
            
            // 初始化视图
            initViews(view)
            
            // 初始化数据
            initData()
            
            // 设置监听器
            setupListeners()
            
            // 创建对话框
            dialog = AlertDialog.Builder(context)
                .setView(view)
                .setCancelable(true)
                .create()
            
            dialog?.show()
            
            AppLog.d("【字号设置对话框】对话框已显示，当前字号: ${currentFontSize}sp")
            
        } catch (e: Exception) {
            AppLog.e("【字号设置对话框】显示对话框失败", e)
            ToastUtils.showToast(context, "显示字号设置对话框失败")
        }
    }
    
    /**
     * 初始化视图
     */
    private fun initViews(view: View) {
        tvCurrentFontSize = view.findViewById(R.id.tv_current_font_size)
        rvFontSizes = view.findViewById(R.id.rv_font_sizes)
        etNewFontSize = view.findViewById(R.id.et_new_font_size)
        btnAddFontSize = view.findViewById(R.id.btn_add_font_size)
        btnResetDefault = view.findViewById(R.id.btn_reset_default)
        btnClose = view.findViewById(R.id.btn_close)
        
        // 设置RecyclerView
        rvFontSizes.layoutManager = LinearLayoutManager(context)
        fontSizeAdapter = FontSizeAdapter()
        rvFontSizes.adapter = fontSizeAdapter
    }
    
    /**
     * 初始化数据
     */
    private fun initData() {
        // 添加所有现有字号（包括预设和自定义）
        existingFontSizes.forEach { fontSize ->
            val isPreset = presetFontSizes.contains(fontSize)
            val isSelected = fontSize == selectedFontSize
            fontSizeList.add(FontSizeItem(fontSize, isPreset, isSelected))
        }

        // 按字号大小排序
        fontSizeList.sortBy { it.fontSize }

        // 更新显示
        updateCurrentFontSizeDisplay()
        fontSizeAdapter.notifyDataSetChanged()

        AppLog.d("【字号设置对话框】初始化数据完成，共${fontSizeList.size}个字号")
    }
    
    /**
     * 设置监听器
     */
    private fun setupListeners() {
        // 关闭按钮
        btnClose.setOnClickListener {
            dialog?.dismiss()
        }
        
        // 添加字号按钮
        btnAddFontSize.setOnClickListener {
            addNewFontSize()
        }
        
        // 恢复默认按钮
        btnResetDefault.setOnClickListener {
            resetToDefault()
        }
    }
    
    /**
     * 添加新字号
     */
    private fun addNewFontSize() {
        try {
            val input = etNewFontSize.text.toString().trim()
            
            if (input.isEmpty()) {
                ToastUtils.showToast(context, "请输入字号")
                return
            }
            
            val fontSize = input.toInt()
            
            if (fontSize < minFontSize || fontSize > maxFontSize) {
                ToastUtils.showToast(context, "字号范围应在${minFontSize}-${maxFontSize}之间")
                return
            }
            
            // 检查是否已存在
            if (fontSizeList.any { it.fontSize == fontSize }) {
                ToastUtils.showToast(context, "字号${fontSize}sp已存在")
                return
            }
            
            // 添加新字号
            fontSizeList.add(FontSizeItem(fontSize, false, false))
            fontSizeList.sortBy { it.fontSize }

            // 立即同步到TextEditPanel的Spinner数据源
            onFontSizeAdded?.invoke(fontSize)

            // 清空输入框
            etNewFontSize.setText("")

            // 更新列表
            fontSizeAdapter.notifyDataSetChanged()

            ToastUtils.showToast(context, "已添加字号${fontSize}sp")
            AppLog.d("【字号设置对话框】添加新字号并同步到Spinner: ${fontSize}sp")
            
        } catch (e: NumberFormatException) {
            ToastUtils.showToast(context, "请输入有效的数字")
        } catch (e: Exception) {
            AppLog.e("【字号设置对话框】添加字号失败", e)
            ToastUtils.showToast(context, "添加字号失败")
        }
    }
    
    /**
     * 恢复默认设置
     */
    private fun resetToDefault() {
        try {
            AlertDialog.Builder(context)
                .setTitle("恢复默认")
                .setMessage("确定要恢复到默认字号设置吗？这将删除所有自定义字号。")
                .setPositiveButton("确定") { _, _ ->
                    // 清除所有数据
                    fontSizeList.clear()

                    // 重新添加预设字号
                    presetFontSizes.forEach { fontSize ->
                        fontSizeList.add(FontSizeItem(fontSize, true, fontSize == 13))
                    }

                    // 设置默认选中字号
                    selectedFontSize = 13

                    // 立即同步恢复默认到TextEditPanel
                    onResetToDefault?.invoke()

                    // 更新显示
                    updateCurrentFontSizeDisplay()
                    fontSizeAdapter.notifyDataSetChanged()

                    // 立即应用默认字号
                    onFontSizeSelected(13)

                    ToastUtils.showToast(context, "已恢复默认字号设置")
                    AppLog.d("【字号设置对话框】已恢复默认设置并同步到Spinner")
                }
                .setNegativeButton("取消", null)
                .show()
                
        } catch (e: Exception) {
            AppLog.e("【字号设置对话框】恢复默认设置失败", e)
        }
    }
    
    /**
     * 更新当前字号显示
     */
    private fun updateCurrentFontSizeDisplay() {
        tvCurrentFontSize.text = "${selectedFontSize}sp"
    }

    /**
     * 字号列表适配器
     */
    private inner class FontSizeAdapter : RecyclerView.Adapter<FontSizeAdapter.FontSizeViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FontSizeViewHolder {
            val view = LayoutInflater.from(context).inflate(R.layout.item_font_size_setting, parent, false)
            return FontSizeViewHolder(view)
        }

        override fun onBindViewHolder(holder: FontSizeViewHolder, position: Int) {
            val item = fontSizeList[position]
            holder.bind(item)
        }

        override fun getItemCount(): Int = fontSizeList.size

        inner class FontSizeViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            private val tvFontSize: TextView = itemView.findViewById(R.id.tv_font_size)
            private val tvPresetTag: TextView = itemView.findViewById(R.id.tv_preset_tag)
            private val ivCurrentIndicator: ImageView = itemView.findViewById(R.id.iv_current_indicator)
            private val btnDelete: ImageView = itemView.findViewById(R.id.btn_delete)

            fun bind(item: FontSizeItem) {
                tvFontSize.text = "${item.fontSize}sp"

                // 显示预设标识
                tvPresetTag.visibility = if (item.isPreset) View.VISIBLE else View.GONE

                // 显示当前选中标识
                ivCurrentIndicator.visibility = if (item.isSelected) View.VISIBLE else View.GONE

                // 显示删除按钮（只有自定义字号才显示）
                btnDelete.visibility = if (!item.isPreset) View.VISIBLE else View.GONE

                // 设置背景色
                itemView.setBackgroundColor(
                    if (item.isSelected) 0x20_4CAF50 else 0x00_000000
                )

                // 点击选择字号
                itemView.setOnClickListener {
                    selectFontSize(item.fontSize)
                }

                // 删除按钮点击
                btnDelete.setOnClickListener {
                    deleteFontSize(item.fontSize, bindingAdapterPosition)
                }

                // 长按删除（自定义字号）
                if (!item.isPreset) {
                    itemView.setOnLongClickListener {
                        deleteFontSize(item.fontSize, bindingAdapterPosition)
                        true
                    }
                }
            }
        }
    }

    /**
     * 选择字号
     */
    private fun selectFontSize(fontSize: Int) {
        // 更新选中状态
        fontSizeList.forEach { it.isSelected = (it.fontSize == fontSize) }
        selectedFontSize = fontSize

        // 更新显示
        updateCurrentFontSizeDisplay()
        fontSizeAdapter.notifyDataSetChanged()

        // 立即应用字号变化
        onFontSizeSelected(fontSize)

        AppLog.d("【字号设置对话框】选择字号并立即应用: ${fontSize}sp")
    }

    /**
     * 删除字号
     */
    private fun deleteFontSize(fontSize: Int, position: Int) {
        try {
            // 检查是否为预设字号
            if (presetFontSizes.contains(fontSize)) {
                ToastUtils.showToast(context, "预设字号不能删除")
                return
            }

            AlertDialog.Builder(context)
                .setTitle("删除字号")
                .setMessage("确定要删除字号 ${fontSize}sp 吗？")
                .setPositiveButton("删除") { _, _ ->
                    // 从列表中移除
                    fontSizeList.removeAt(position)
                    fontSizeAdapter.notifyItemRemoved(position)

                    // 立即同步删除到TextEditPanel的Spinner数据源
                    onFontSizeDeleted?.invoke(fontSize)

                    // 如果删除的是当前选中的字号，选择默认字号
                    if (selectedFontSize == fontSize) {
                        selectFontSize(13) // 选择默认字号
                    }

                    ToastUtils.showToast(context, "已删除字号 ${fontSize}sp")
                    AppLog.d("【字号设置对话框】删除字号并同步到Spinner: ${fontSize}sp")
                }
                .setNegativeButton("取消", null)
                .show()

        } catch (e: Exception) {
            AppLog.e("【字号设置对话框】删除字号失败", e)
            ToastUtils.showToast(context, "删除字号失败")
        }
    }
}
