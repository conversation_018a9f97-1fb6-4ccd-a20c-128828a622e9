package com.example.castapp.ui.dialog

import android.app.AlertDialog
import android.content.Context
import android.view.LayoutInflater
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.example.castapp.R
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.NoteManager
import com.example.castapp.utils.ToastUtils

/**
 * 🏷️ 备注编辑对话框
 * 用于编辑备注信息
 */
class NoteEditDialog(
    private val context: Context,
    private val connectionId: String,
    private val deviceName: String?,
    private val currentNote: String?,
    private val onNoteChanged: (String) -> Unit
) {

    private val noteManager = NoteManager(context)

    /**
     * 🏷️ 显示备注编辑对话框
     */
    fun show() {
        try {
            val view = LayoutInflater.from(context).inflate(R.layout.dialog_note_edit, null)
            
            // 获取视图组件
            val deviceInfoText = view.findViewById<TextView>(R.id.tv_device_info)
            val noteInput = view.findViewById<EditText>(R.id.et_device_note)
            val btnClose = view.findViewById<ImageView>(R.id.btn_close)
            
            // 设置设备信息显示
            val deviceDisplayName = if (!deviceName.isNullOrBlank()) {
                deviceName
            } else {
                "未知设备"
            }
            val shortId = connectionId.takeLast(8)
            deviceInfoText.text = "$deviceDisplayName（ID:$shortId）"
            
            // 设置当前备注内容
            val currentNoteText = if (!currentNote.isNullOrBlank() && currentNote != "无") {
                currentNote
            } else {
                ""
            }
            noteInput.setText(currentNoteText)

            // 确保EditText能正确显示长文本
            noteInput.post {
                noteInput.setSelection(noteInput.text.length) // 光标移到末尾
                // 如果文本较长，滚动到顶部以显示完整内容
                if (currentNoteText.isNotEmpty()) {
                    noteInput.scrollTo(0, 0)
                }
            }
            
            // 创建对话框（无默认标题）
            val dialog = AlertDialog.Builder(context)
                .setView(view)
                .setPositiveButton("保存") { _, _ ->
                    val newNote = noteInput.text.toString().trim()
                    saveNote(newNote)
                }
                .setNegativeButton("取消", null)
                .setNeutralButton("删除备注") { _, _ ->
                    deleteNote()
                }
                .create()

            // 设置关闭按钮点击事件
            btnClose.setOnClickListener {
                dialog.dismiss()
            }
            
            dialog.show()
            
            // 自动弹出键盘
            noteInput.requestFocus()
            
            AppLog.d("🏷️ 备注编辑对话框已显示: $connectionId")
            
        } catch (e: Exception) {
            AppLog.e("🏷️ 显示备注编辑对话框失败", e)
            ToastUtils.showToast(context, "显示对话框失败")
        }
    }

    /**
     * 🏷️ 保存备注
     */
    private fun saveNote(note: String) {
        try {
            val success = noteManager.saveNote(connectionId, note)
            if (success) {
                val finalNote = note.ifEmpty { "无" }
                onNoteChanged(finalNote)
                ToastUtils.showToast(context, "备注保存成功")
                AppLog.d("🏷️ 备注保存成功: $connectionId -> $finalNote")
            } else {
                ToastUtils.showToast(context, "备注保存失败")
                AppLog.e("🏷️ 备注保存失败: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("🏷️ 保存备注异常", e)
            ToastUtils.showToast(context, "保存失败")
        }
    }

    /**
     * 🏷️ 删除备注
     */
    private fun deleteNote() {
        try {
            val success = noteManager.deleteNote(connectionId)
            if (success) {
                onNoteChanged("无")
                ToastUtils.showToast(context, "备注已删除")
                AppLog.d("🏷️ 备注删除成功: $connectionId")
            } else {
                ToastUtils.showToast(context, "删除失败")
                AppLog.e("🏷️ 备注删除失败: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("🏷️ 删除备注异常", e)
            ToastUtils.showToast(context, "删除失败")
        }
    }
}
