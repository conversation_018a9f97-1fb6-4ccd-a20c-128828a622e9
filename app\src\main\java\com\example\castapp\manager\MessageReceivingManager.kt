package com.example.castapp.manager

import com.example.castapp.model.Connection
import com.example.castapp.websocket.ControlMessage
import com.example.castapp.websocket.WebSocketServer
import com.example.castapp.utils.AppLog

/**
 * 消息接收管理器
 * 负责处理来自发送端的各种控制消息
 */
class MessageReceivingManager(
    private val audioReceivingManager: AudioReceivingManager,
    private val videoReceivingManager: VideoReceivingManager,
    private val stateManager: StateManager,
    private val callback: MessageReceivingCallback
) {

    /**
     * 消息接收回调接口
     * 用于与ReceivingService进行交互
     */
    interface MessageReceivingCallback {
        fun onNewConnection(connectionId: String)
        fun onConnectionDisconnected(connectionId: String)
        fun onVideoWindowRemoved(connectionId: String)
        fun onScreenResolution(connectionId: String, width: Int, height: Int)
        fun markAsRemoteControlConnection(connectionId: String)
        fun isRemoteControlConnection(connectionId: String): Boolean
        fun sendScreenResolutionToRemoteController(connectionId: String)
        fun addProcessedDisconnection(connectionId: String): Boolean
        fun getWebSocketServer(): WebSocketServer?
    }

    /**
     * 处理WebSocket控制消息
     */
    fun handleMessage(controlMessage: ControlMessage) {
        AppLog.service("收到WebSocket控制消息: ${controlMessage.type}, 连接ID: ${controlMessage.connectionId}")

        when (controlMessage.type) {
            ControlMessage.TYPE_CONNECTION_REQUEST -> {
                handleConnectionRequest(controlMessage)
            }

            ControlMessage.TYPE_SCREEN_RESOLUTION -> {
                handleScreenResolution(controlMessage)
            }

            ControlMessage.TYPE_H264_CONFIG -> {
                handleH264Config(controlMessage)
            }

            ControlMessage.TYPE_AAC_CONFIG -> {
                handleAacConfig(controlMessage)
            }

            ControlMessage.TYPE_SSRC_MAPPING -> {
                handleSsrcMapping(controlMessage)
            }

            ControlMessage.TYPE_CASTING_STATE -> {
                handleCastingState(controlMessage)
            }

            ControlMessage.TYPE_BITRATE_CONTROL -> {
                handleBitrateControl(controlMessage)
            }

            ControlMessage.TYPE_MEDIA_AUDIO_CONTROL -> {
                handleMediaAudioControl(controlMessage)
            }

            ControlMessage.TYPE_MIC_AUDIO_CONTROL -> {
                handleMicAudioControl(controlMessage)
            }

            ControlMessage.TYPE_VIDEO_STREAM_STOP -> {
                handleVideoStreamStop(controlMessage)
            }

            ControlMessage.TYPE_DISCONNECT -> {
                handleDisconnect(controlMessage)
            }

            ControlMessage.TYPE_FUNCTION_CONTROL -> {
                handleFunctionControl(controlMessage)
            }

            ControlMessage.TYPE_RESOLUTION_CHANGE -> {
                handleResolutionChange(controlMessage)
            }

            ControlMessage.TYPE_HEARTBEAT -> {
                // 处理心跳消息，不记录日志以减少输出
            }

            "remote_control_request" -> {
                handleRemoteControlRequest(controlMessage)
            }

            else -> {
                AppLog.w("未知的控制消息类型: ${controlMessage.type}")
            }
        }
    }

    /**
     * 处理连接请求
     */
    private fun handleConnectionRequest(controlMessage: ControlMessage) {
        val connectionId = controlMessage.connectionId
        AppLog.service("处理纯净连接请求: WebSocket层connectionId=$connectionId")

        // 检查是否为遥控连接，避免进入投屏状态管理
        if (callback.isRemoteControlConnection(connectionId)) {
            AppLog.service("【连接类型】遥控连接不进入投屏状态管理: $connectionId")
            return
        }

        var foundConnection = stateManager.findConnectionById(connectionId)
        if (foundConnection == null) {
            AppLog.service("connectionId对应的连接不存在，尝试自动创建: $connectionId")

            // 从WebSocket服务器获取发送端IP
            val clientIP = callback.getWebSocketServer()?.getClientIP(connectionId) ?: "未知IP"

            if (clientIP != "未知IP") {
                // 自动创建连接，使用真实的WebSocket端口
                val clientPort = callback.getWebSocketServer()?.getClientPort(connectionId) ?: 8888

                foundConnection = Connection(
                    connectionId = connectionId,
                    ipAddress = clientIP,
                    port = clientPort
                )
                // 使用addExistingConnection来确保连接被正确建立
                foundConnection = stateManager.addExistingConnection(foundConnection)
                AppLog.service("自动创建连接成功: ${foundConnection.getDisplayText()}")
                AppLog.service("连接ID: ${foundConnection.connectionId}")
            } else {
                AppLog.e("无法获取发送端IP，无法自动创建连接")
                return
            }
        }

        AppLog.service("连接ID: $connectionId")

        // 设备名称已在 handleConnectionRequest 回调中统一处理，这里不再重复处理
        AppLog.service("【消息处理】设备名称已在连接请求回调中处理")

        // 清理视频接收管理器中的断开标记
        videoReceivingManager.clearDisconnectionFlag(connectionId)
        AppLog.service("清理断开标记: $connectionId")

        // 直接从连接获取SSRC，无需映射表
        foundConnection.let { connection ->
            val ssrc = connection.getSSRC()
            AppLog.service("连接建立，SSRC: $connectionId -> $ssrc")
        }

        // 连接建立完成，等待功能控制消息
        AppLog.service("连接建立完成，等待功能启用消息: $connectionId")
    }

    /**
     * 处理屏幕分辨率信息
     */
    private fun handleScreenResolution(controlMessage: ControlMessage) {
        val connectionId = controlMessage.connectionId
        val width = controlMessage.getIntData("width") ?: 0
        val height = controlMessage.getIntData("height") ?: 0

        if (width > 0 && height > 0) {
            videoReceivingManager.handleScreenResolution(connectionId, width, height)
        } else {
            AppLog.w("无效的屏幕分辨率信息: ${width}x${height}")
        }
    }

    /**
     * 处理H.264配置数据
     */
    private fun handleH264Config(controlMessage: ControlMessage) {
        val senderConnectionId = controlMessage.connectionId
        val spsData = controlMessage.getByteArrayData("sps")
        val ppsData = controlMessage.getByteArrayData("pps")
        val width = controlMessage.getIntData("width")
        val height = controlMessage.getIntData("height")
        val orientation = controlMessage.getIntData("orientation") // 🎯 横竖屏适配：获取方向信息

        // 🎯 转发给视频接收管理器处理，包含方向信息
        videoReceivingManager.handleH264ConfigWithOrientation(senderConnectionId, spsData, ppsData, width, height, orientation)
    }

    /**
     * 处理AAC配置数据
     */
    private fun handleAacConfig(controlMessage: ControlMessage) {
        val senderConnectionId = controlMessage.connectionId
        val configData = controlMessage.getByteArrayData("config")

        AppLog.service("收到AAC配置数据: 发送端connectionId=$senderConnectionId, 配置数据: ${configData?.size ?: 0} bytes")

        if (configData != null) {
            val connection = stateManager.findConnectionById(senderConnectionId)
            if (connection != null) {
                val ssrc = connection.getSSRC()

                // 根据AAC配置数据的通道数判断音频类型
                val audioType = audioReceivingManager.determineAudioTypeFromConfig(configData)
                when (audioType) {
                    "media_audio" -> {
                        audioReceivingManager.processMediaAudioConfig(connection.connectionId, configData)
                        AppLog.service("已处理WebSocket媒体音频AAC配置数据: connectionId=${connection.connectionId}, ssrc=$ssrc")
                    }
                    "mic_audio" -> {
                        audioReceivingManager.processMicAudioConfig(connection.connectionId, configData)
                        AppLog.service("已处理WebSocket麦克风音频AAC配置数据: connectionId=${connection.connectionId}, ssrc=$ssrc")
                    }
                    else -> {
                        AppLog.w("无法确定AAC配置数据的音频类型: $senderConnectionId")
                    }
                }
            } else {
                AppLog.w("未找到connectionId对应的连接，无法处理AAC配置: $senderConnectionId")
            }
        } else {
            AppLog.w("AAC配置数据为空: $senderConnectionId")
        }
    }

    /**
     * 处理SSRC映射
     */
    private fun handleSsrcMapping(controlMessage: ControlMessage) {
        val senderConnectionId = controlMessage.connectionId
        val receivedSsrc = controlMessage.getLongData("ssrc") ?: 0L

        val connection = stateManager.findConnectionById(senderConnectionId)
        if (connection != null) {
            val expectedSsrc = connection.getSSRC()
            if (receivedSsrc == expectedSsrc) {
                AppLog.service("SSRC验证成功: $senderConnectionId -> SSRC=$expectedSsrc")
                // 处理缓存的配置数据
                audioReceivingManager.processPendingAudioConfigs(connection.connectionId, expectedSsrc)
            } else {
                AppLog.w("SSRC不匹配: 期望=$expectedSsrc, 收到=$receivedSsrc")
            }
        } else {
            AppLog.w("未找到connectionId对应的连接: $senderConnectionId")
        }
    }

    /**
     * 处理投屏状态同步消息
     */
    private fun handleCastingState(controlMessage: ControlMessage) {
        val connectionId = controlMessage.connectionId
        val isCasting = controlMessage.getBooleanData("is_casting") ?: false
        val reason = controlMessage.getStringData("reason") ?: ""

        AppLog.service("收到投屏状态同步: $connectionId, 投屏中: $isCasting, 原因: $reason")

        // 可以根据状态更新UI或执行相应操作
        if (isCasting) {
            AppLog.service("连接 $connectionId 开始投屏: $reason")
        } else {
            AppLog.service("连接 $connectionId 停止投屏: $reason")
        }
    }

    /**
     * 处理码率控制消息
     */
    private fun handleBitrateControl(controlMessage: ControlMessage) {
        val connectionId = controlMessage.connectionId
        val bitrate = controlMessage.getIntData("bitrate") ?: 0
        val targetConnectionId = controlMessage.getStringData("target_connection_id")

        AppLog.service("收到码率控制消息: $connectionId, 码率: ${bitrate}bps, 目标连接: $targetConnectionId")

        // 这里可以根据需要处理码率控制，比如调整解码器参数或通知UI
        if (bitrate > 0) {
            val bitrateMbps = bitrate / 1_000_000
            AppLog.service("接收端收到码率调整通知: ${bitrateMbps}Mbps")
        }
    }

    /**
     * 处理媒体音频控制消息
     */
    private fun handleMediaAudioControl(controlMessage: ControlMessage) {
        val connectionId = controlMessage.connectionId
        val isEnabled = controlMessage.getBooleanData("enabled") ?: false

        AppLog.service("收到媒体音频控制消息: $connectionId, 启用: $isEnabled")
        audioReceivingManager.handleMediaAudioControl(connectionId, isEnabled)
    }

    /**
     * 处理麦克风音频控制消息
     */
    private fun handleMicAudioControl(controlMessage: ControlMessage) {
        val connectionId = controlMessage.connectionId
        val isEnabled = controlMessage.getBooleanData("enabled") ?: false

        AppLog.service("收到麦克风音频控制消息: $connectionId, 启用: $isEnabled")
        audioReceivingManager.handleMicAudioControl(connectionId, isEnabled)
    }

    /**
     * 处理视频流停止消息
     */
    private fun handleVideoStreamStop(controlMessage: ControlMessage) {
        val connectionId = controlMessage.connectionId

        // 转发给视频接收管理器处理
        videoReceivingManager.handleVideoStreamStop(connectionId)
    }

    /**
     * 处理断开连接消息
     */
    private fun handleDisconnect(controlMessage: ControlMessage) {
        val connectionId = controlMessage.connectionId
        AppLog.service("断开连接: $connectionId")

        // 检查是否已处理过此断开连接
        if (!callback.addProcessedDisconnection(connectionId)) {
            AppLog.service("连接 $connectionId 的断开事件已处理，跳过重复处理")
            return
        }

        // 通过connectionId查找连接并清理对应的音频组件
        val connection = stateManager.findConnectionById(connectionId)
        if (connection != null) {
            audioReceivingManager.cleanupAudioComponents(connection.connectionId)
        }

        // 处理视频连接断开
        AppLog.service("【断开处理】通过视频接收管理器处理断开逻辑: $connectionId")
        videoReceivingManager.handleConnectionDisconnect(connectionId)

        // 从StateManager中移除连接
        if (connection != null) {
            stateManager.removeConnection(connection)
            AppLog.service("【断开处理】已从StateManager移除连接: ${connection.getDisplayText()}")
        } else {
            AppLog.service("【断开处理】StateManager中未找到要移除的连接: $connectionId")
        }

        AppLog.service("【断开处理】连接断开处理完成: $connectionId")
    }

    /**
     * 处理功能控制消息
     */
    private fun handleFunctionControl(controlMessage: ControlMessage) {
        val senderConnectionId = controlMessage.connectionId
        val functionType = controlMessage.getStringData("function_type") ?: ""
        val isEnabled = controlMessage.getBooleanData("enabled") ?: false

        AppLog.service("收到功能控制消息: 发送端connectionId=$senderConnectionId, 功能: $functionType, 启用: $isEnabled")
        AppLog.service("处理功能控制: $senderConnectionId (功能: $functionType)")

        when (functionType) {
            "video" -> {
                // 转发给视频接收管理器处理
                videoReceivingManager.handleVideoFunctionControl(senderConnectionId, isEnabled)
            }
            "media_audio" -> {
                audioReceivingManager.handleMediaAudioControl(senderConnectionId, isEnabled)
            }
            "mic_audio" -> {
                audioReceivingManager.handleMicAudioControl(senderConnectionId, isEnabled)
            }
            else -> {
                AppLog.w("未知的功能类型: $functionType")
            }
        }
    }

    /**
     * 处理分辨率变化消息
     */
    private fun handleResolutionChange(controlMessage: ControlMessage) {
        val connectionId = controlMessage.connectionId
        val newWidth = controlMessage.getIntData("width") ?: 0
        val newHeight = controlMessage.getIntData("height") ?: 0

        if (newWidth > 0 && newHeight > 0) {
            // 转发给视频接收管理器处理
            videoReceivingManager.handleResolutionChange(connectionId, newWidth, newHeight)
        } else {
            AppLog.w("无效的分辨率变化数据: ${newWidth}x${newHeight}")
        }
    }

    /**
     * 处理远程控制请求消息
     */
    private fun handleRemoteControlRequest(controlMessage: ControlMessage) {
        val connectionId = controlMessage.connectionId
        val deviceName = controlMessage.getStringData("device_name")
        AppLog.service("收到远程控制请求: $connectionId, 设备: $deviceName")

        // 标记这是遥控连接，不进入投屏状态管理
        callback.markAsRemoteControlConnection(connectionId)

        // 远程控制请求已在 onConnectionRequest 回调中处理
        // 这里只需要记录日志，实际处理逻辑在 handleConnectionRequest 中
    }
}
