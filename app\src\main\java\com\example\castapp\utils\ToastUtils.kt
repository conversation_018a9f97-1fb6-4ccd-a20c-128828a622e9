package com.example.castapp.utils

import android.content.Context
import android.widget.Toast

/**
 * Toast工具类
 * 提供简洁的Toast消息显示功能，替代ToastManager
 */
object ToastUtils {
    
    /**
     * 显示短时间Toast消息
     * @param context 上下文
     * @param message 消息内容
     */
    fun showToast(context: Context, message: String) {
        showToast(context, message, Toast.LENGTH_SHORT)
    }
    
    /**
     * 显示Toast消息
     * @param context 上下文
     * @param message 消息内容
     * @param duration 显示时长
     */
    fun showToast(context: Context, message: String, duration: Int) {
        try {
            Toast.makeText(context, message, duration).show()
            AppLog.d("显示Toast: $message")
        } catch (e: Exception) {
            AppLog.e("显示Toast失败: $message", e)
        }
    }
}
