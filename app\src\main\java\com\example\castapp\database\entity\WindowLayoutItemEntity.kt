package com.example.castapp.database.entity

import androidx.core.graphics.toColorInt
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ForeignKey
import androidx.room.Index
import com.example.castapp.model.CastWindowInfo

/**
 * 窗口布局项实体类
 * 用于存储布局中每个窗口的详细参数信息
 */
@Entity(
    tableName = "window_layout_items",
    foreignKeys = [
        ForeignKey(
            entity = WindowLayoutEntity::class,
            parentColumns = ["id"],
            childColumns = ["layoutId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index(value = ["layoutId"])]
)
data class WindowLayoutItemEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    /**
     * 所属布局的ID
     */
    val layoutId: Long,
    
    /**
     * 窗口在布局中的顺序（用于恢复时的层级顺序）
     */
    val orderIndex: Int,
    
    /**
     * 设备名称
     */
    val deviceName: String?,
    
    /**
     * 设备ID（连接ID）
     */
    val deviceId: String,
    
    /**
     * IP地址
     */
    val ipAddress: String,
    
    /**
     * 端口号
     */
    val port: Int,
    
    /**
     * 窗口X坐标
     */
    val positionX: Float,
    
    /**
     * 窗口Y坐标
     */
    val positionY: Float,
    
    /**
     * 缩放倍数
     */
    val scaleFactor: Float,
    
    /**
     * 旋转角度
     */
    val rotationAngle: Float,
    
    /**
     * 是否正在裁剪
     */
    val isCropping: Boolean,
    
    /**
     * 拖动功能是否启用
     */
    val isDragEnabled: Boolean,
    
    /**
     * 缩放功能是否启用
     */
    val isScaleEnabled: Boolean,
    
    /**
     * 旋转功能是否启用
     */
    val isRotationEnabled: Boolean,
    
    /**
     * 窗口是否可见
     */
    val isVisible: Boolean,
    
    /**
     * 镜像功能是否启用
     */
    val isMirrored: Boolean,
    
    /**
     * 圆角半径
     */
    val cornerRadius: Float,
    
    /**
     * 透明度
     */
    val alpha: Float,
    
    /**
     * 精准调控功能是否启用
     */
    val isControlEnabled: Boolean,

    /**
     * 📝 文本编辑功能是否启用（仅文本窗口）
     */
    val isEditEnabled: Boolean,



    /**
     * 边框显示是否启用
     */
    val isBorderEnabled: Boolean = false,

    /**
     * 边框颜色
     */
    val borderColor: Int = "#6B6B6B".toColorInt(),

    /**
     * 边框宽度（dp）
     */
    val borderWidth: Float = 2f,

    /**
     * 裁剪区域参数（JSON格式存储，包含left, top, right, bottom）
     */
    val cropRect: String? = null,

    /**
     * 基础窗口宽度（未缩放）
     */
    val baseWindowWidth: Int = 0,

    /**
     * 基础窗口高度（未缩放）
     */
    val baseWindowHeight: Int = 0,

    /**
     * 🏷️ 备注信息
     */
    val note: String? = null,

    /**
     * 📝 文本格式信息 - 文本内容
     */
    val textContent: String? = null,

    /**
     * 📝 文本格式信息 - 是否加粗
     */
    val textIsBold: Boolean = false,

    /**
     * 📝 文本格式信息 - 是否倾斜
     */
    val textIsItalic: Boolean = false,

    /**
     * 📝 文本格式信息 - 字号大小
     */
    val textFontSize: Int = 13,

    /**
     * 📝 文本格式信息 - 字体名称
     */
    val textFontName: String? = null,

    /**
     * 📝 文本格式信息 - 字体族
     */
    val textFontFamily: String? = null,

    /**
     * 📝 文本格式信息 - 行间距值（dp）
     */
    val textLineSpacing: Float = 0.0f,

    /**
     * 📝 文本格式信息 - 文本对齐方式
     */
    val textAlignment: Int = android.view.Gravity.CENTER,

    /**
     * 📝 富文本格式数据 - JSON格式的完整格式信息
     */
    val richTextData: String? = null,

    /**
     * 🎨 窗口背景颜色 - 是否启用
     */
    val windowColorEnabled: Boolean = false,

    /**
     * 🎨 窗口背景颜色 - 颜色值
     */
    val windowBackgroundColor: Int = 0xFFFFFFFF.toInt(),

    /**
     * 📁 媒体文件URI（用于重新创建媒体窗口）
     */
    val mediaFileUri: String? = null,

    /**
     * 📁 媒体文件名（用于重新创建媒体窗口）
     */
    val mediaFileName: String? = null,

    /**
     * 📁 媒体内容类型（video/image，用于重新创建媒体窗口）
     */
    val mediaContentType: String? = null,

    /**
     * 🎬 视频播放开关状态（仅对视频窗口有效）
     */
    val videoPlayEnabled: Boolean = true,

    /**
     * 🎬 视频播放次数（-1表示无限循环，0表示不循环，>0表示循环指定次数）
     */
    val videoLoopCount: Int = -1,

    /**
     * 🎬 视频播放音量（0-100的百分比值）
     */
    val videoVolume: Int = 80,

    /**
     * 🎯 横屏模式检测是否启用
     */
    val isLandscapeModeEnabled: Boolean = false
) {
    companion object {
        /**
         * 从CastWindowInfo创建WindowLayoutItemEntity
         * @param layoutId 布局ID
         * @param orderIndex 在布局中的顺序索引（用于层级恢复）
         * @param windowInfo 窗口信息（包含层级参数）
         * @param context 上下文，用于获取文本格式信息（可选）
         */
        fun fromCastWindowInfo(
            layoutId: Long,
            orderIndex: Int,
            windowInfo: CastWindowInfo,
            context: android.content.Context? = null
        ): WindowLayoutItemEntity {
            // 将裁剪区域转换为JSON字符串
            val cropRectJson = windowInfo.cropRectRatio?.let { rect ->
                "{\"left\":${rect.left},\"top\":${rect.top},\"right\":${rect.right},\"bottom\":${rect.bottom}}"
            }

            // 📝 获取文本格式信息（仅对文本窗口有效）
            var textContent: String? = null
            var textIsBold = false
            var textIsItalic = false
            var textFontSize = 13
            var textFontName: String? = null
            var textFontFamily: String? = null
            var textLineSpacing = 0.0f
            var textAlignment = android.view.Gravity.CENTER
            var richTextData: String? = null

            // 📁 获取媒体文件信息（仅对媒体窗口有效）
            var mediaFileUri: String? = null
            var mediaFileName: String? = null
            var mediaContentType: String? = null

            // 🎬 初始化视频播放状态变量
            var videoPlayEnabled = true
            var videoLoopCount = -1
            var videoVolume = 80

            // 检查是否为媒体窗口
            if ((windowInfo.connectionId.startsWith("video_") || windowInfo.connectionId.startsWith("image_")) && context != null) {
                try {
                    // 📁 从MediaFileManager获取媒体文件信息
                    val mediaFileManager = com.example.castapp.utils.MediaFileManager(context)
                    val mediaFileInfo = mediaFileManager.getMediaFileInfo(windowInfo.connectionId)
                    if (mediaFileInfo != null) {
                        mediaFileUri = mediaFileInfo.uri
                        mediaFileName = mediaFileInfo.fileName
                        mediaContentType = mediaFileInfo.contentType
                        com.example.castapp.utils.AppLog.d("【数据库保存】媒体文件信息已获取: ID=${windowInfo.connectionId}, 文件=$mediaFileName, URI=$mediaFileUri")
                    } else {
                        com.example.castapp.utils.AppLog.d("【数据库保存】未找到媒体文件信息: ID=${windowInfo.connectionId}")
                    }
                } catch (e: Exception) {
                    com.example.castapp.utils.AppLog.e("【数据库保存】获取媒体文件信息失败: ID=${windowInfo.connectionId}", e)
                }

                // 🎬 如果是视频窗口，从SharedPreferences读取播放状态
                if (windowInfo.connectionId.startsWith("video_")) {
                    try {
                        val prefs = context.getSharedPreferences("video_settings", android.content.Context.MODE_PRIVATE)
                        videoPlayEnabled = prefs.getBoolean("${windowInfo.connectionId}_play_enabled", true)
                        videoLoopCount = prefs.getInt("${windowInfo.connectionId}_loop_count", -1)
                        videoVolume = prefs.getInt("${windowInfo.connectionId}_volume", 80)
                        com.example.castapp.utils.AppLog.d("【数据库保存】视频播放状态已获取: ID=${windowInfo.connectionId}, 播放=$videoPlayEnabled, 次数=$videoLoopCount, 音量=$videoVolume")
                    } catch (e: Exception) {
                        com.example.castapp.utils.AppLog.e("【数据库保存】获取视频播放状态失败: ID=${windowInfo.connectionId}", e)
                    }
                }
            }

            // 检查是否为文本窗口
            if (windowInfo.connectionId.startsWith("text_") && context != null) {
                try {
                    // 📝 从TextFormatManager获取文本格式信息
                    val textFormatManager = com.example.castapp.utils.TextFormatManager(context)

                    // 优先获取富文本格式
                    val richTextSpannable = textFormatManager.getRichTextFormat(windowInfo.connectionId)
                    if (richTextSpannable != null) {
                        textContent = richTextSpannable.toString()
                        richTextData = context.getSharedPreferences("text_format_preferences", android.content.Context.MODE_PRIVATE)
                            .getString("rich_text_data_${windowInfo.connectionId}", null)

                        // 📝 关键修复：即使有富文本格式，也要获取TextView级别的属性（行间距、对齐方式）
                        val sharedPrefs = context.getSharedPreferences("text_format_preferences", android.content.Context.MODE_PRIVATE)
                        textLineSpacing = sharedPrefs.getFloat("line_spacing_${windowInfo.connectionId}", 0.0f)
                        textAlignment = sharedPrefs.getInt("text_alignment_${windowInfo.connectionId}", android.view.Gravity.CENTER)

                        com.example.castapp.utils.AppLog.d("【数据库保存】富文本格式已获取: ID=${windowInfo.connectionId}, 内容=$textContent, 数据大小=${richTextData?.length ?: 0}")
                        com.example.castapp.utils.AppLog.d("【数据库保存】富文本模式下TextView属性已获取: 行间距=${textLineSpacing}dp, 对齐=$textAlignment")

                        if (textLineSpacing > 0.0f) {
                            com.example.castapp.utils.AppLog.d("【数据库保存】✅ 富文本模式下行间距信息已获取: ${textLineSpacing}dp")
                        } else {
                            com.example.castapp.utils.AppLog.w("【数据库保存】⚠️ 富文本模式下行间距为0或未设置: ${textLineSpacing}dp")
                        }
                    } else {
                        // 尝试获取扩展格式信息
                        val extendedFormatInfo = textFormatManager.getExtendedTextFormat(windowInfo.connectionId)
                        if (extendedFormatInfo != null) {
                            textContent = extendedFormatInfo.textContent
                            textIsBold = extendedFormatInfo.isBold
                            textIsItalic = extendedFormatInfo.isItalic
                            textFontSize = extendedFormatInfo.fontSize
                            textFontName = extendedFormatInfo.fontName
                            textFontFamily = extendedFormatInfo.fontFamily
                            textLineSpacing = extendedFormatInfo.lineSpacing
                            textAlignment = extendedFormatInfo.textAlignment
                            com.example.castapp.utils.AppLog.d("【数据库保存】扩展文本格式已获取: ID=${windowInfo.connectionId}, 字体=$textFontName, 行间距=${textLineSpacing}dp, 对齐=$textAlignment")

                            // 📝 调试：详细记录行间距信息
                            if (textLineSpacing > 0.0f) {
                                com.example.castapp.utils.AppLog.d("【数据库保存】✅ 行间距信息已获取并将保存: ${textLineSpacing}dp")
                            } else {
                                com.example.castapp.utils.AppLog.w("【数据库保存】⚠️ 行间距为0或未设置: ${textLineSpacing}dp")
                            }
                        } else {
                            // 后备方案：获取基本格式信息
                            val formatInfo = textFormatManager.getTextFormat(windowInfo.connectionId)
                            if (formatInfo != null) {
                                textContent = formatInfo.textContent
                                textIsBold = formatInfo.isBold
                                textIsItalic = formatInfo.isItalic
                                textFontSize = formatInfo.fontSize
                                com.example.castapp.utils.AppLog.d("【数据库保存】基本文本格式已获取: ID=${windowInfo.connectionId}, 内容=$textContent, 加粗=$textIsBold, 倾斜=$textIsItalic, 字号=${textFontSize}sp")
                            } else {
                                com.example.castapp.utils.AppLog.d("【数据库保存】未找到文本格式信息: ID=${windowInfo.connectionId}")
                            }
                        }
                    }
                } catch (e: Exception) {
                    com.example.castapp.utils.AppLog.e("【数据库保存】获取文本格式失败: ID=${windowInfo.connectionId}", e)
                }
            }

            // 调试日志
            com.example.castapp.utils.AppLog.d("【数据库保存】设备 ${windowInfo.connectionId}: 层级=${windowInfo.zOrder}, 顺序索引=$orderIndex, 裁剪JSON=$cropRectJson")

            return WindowLayoutItemEntity(
                layoutId = layoutId,
                orderIndex = orderIndex,
                deviceName = windowInfo.deviceName,
                deviceId = windowInfo.connectionId,
                ipAddress = windowInfo.ipAddress,
                port = windowInfo.port,
                positionX = windowInfo.positionX,
                positionY = windowInfo.positionY,
                scaleFactor = windowInfo.scaleFactor,
                rotationAngle = windowInfo.rotationAngle,
                isCropping = windowInfo.isCropping,
                isDragEnabled = windowInfo.isDragEnabled,
                isScaleEnabled = windowInfo.isScaleEnabled,
                isRotationEnabled = windowInfo.isRotationEnabled,
                isVisible = windowInfo.isVisible,
                isMirrored = windowInfo.isMirrored,
                cornerRadius = windowInfo.cornerRadius,
                alpha = windowInfo.alpha,
                isControlEnabled = windowInfo.isControlEnabled,
                isEditEnabled = windowInfo.isEditEnabled,

                isBorderEnabled = windowInfo.isBorderEnabled,
                borderColor = windowInfo.borderColor,
                borderWidth = windowInfo.borderWidth,
                cropRect = cropRectJson,
                baseWindowWidth = windowInfo.baseWindowWidth,
                baseWindowHeight = windowInfo.baseWindowHeight,
                note = windowInfo.note, // 🏷️ 保存备注信息到数据库
                textContent = textContent, // 📝 保存文本内容
                textIsBold = textIsBold, // 📝 保存加粗状态
                textIsItalic = textIsItalic, // 📝 保存倾斜状态
                textFontSize = textFontSize, // 📝 保存字号大小
                textFontName = textFontName, // 📝 保存字体名称
                textFontFamily = textFontFamily, // 📝 保存字体族
                textLineSpacing = textLineSpacing, // 📝 保存行间距
                textAlignment = textAlignment, // 📝 保存文本对齐方式
                richTextData = richTextData, // 📝 保存富文本数据
                windowColorEnabled = windowInfo.windowColorEnabled, // 🎨 保存窗口颜色启用状态
                windowBackgroundColor = windowInfo.windowBackgroundColor, // 🎨 保存窗口背景颜色
                mediaFileUri = mediaFileUri, // 📁 保存媒体文件URI
                mediaFileName = mediaFileName, // 📁 保存媒体文件名
                mediaContentType = mediaContentType, // 📁 保存媒体内容类型
                videoPlayEnabled = videoPlayEnabled, // 🎬 保存视频播放开关状态
                videoLoopCount = videoLoopCount, // 🎬 保存视频播放次数
                videoVolume = videoVolume, // 🎬 保存视频播放音量
                isLandscapeModeEnabled = windowInfo.isLandscapeModeEnabled // 🎯 保存横屏开关状态
            )
        }
    }
    
    /**
     * 获取显示用的设备信息
     */
    fun getDisplayDeviceInfo(): String {
        return if (!deviceName.isNullOrBlank()) {
            deviceName
        } else {
            "$ipAddress:$port"
        }
    }

    /**
     * 🏷️ 获取备注显示文本
     */
    fun getNoteDisplayText(): String {
        return if (!note.isNullOrBlank() && note != "无") {
            "备注: $note"
        } else {
            "备注: 无"
        }
    }

    /**
     * 获取简化的连接ID（显示规则）
     * - 摄像头窗口：显示完整ID
     * - 媒体窗口：显示文件名前8位
     * - 文本窗口：显示UUID后8位
     * - 投屏窗口：显示后8位
     */
    fun getShortConnectionId(): String {
        return when {
            deviceId == "front_camera" -> "front_camera"
            deviceId == "rear_camera" -> "rear_camera"
            deviceId.startsWith("video_") || deviceId.startsWith("image_") -> {
                // 媒体窗口：提取文件名部分并显示前8位
                val parts = deviceId.split("_", limit = 2)
                if (parts.size >= 2) {
                    val fileName = parts[1]
                    if (fileName.length > 8) fileName.take(8) else fileName
                } else {
                    deviceId.takeLast(8)
                }
            }
            deviceId.startsWith("text_") -> {
                // 文本窗口：显示UUID后8位
                deviceId.takeLast(8)
            }
            else -> deviceId.takeLast(8)
        }
    }
    
    /**
     * 获取裁剪状态显示文本
     */
    fun getCropDisplayText(): String {
        // 调试日志
        com.example.castapp.utils.AppLog.d("【显示裁剪】设备 $deviceId: cropRect=$cropRect")

        return if (!cropRect.isNullOrBlank()) {
            try {
                // 解析裁剪区域并显示
                val left = cropRect.substringAfter("\"left\":").substringBefore(",").toFloat()
                val top = cropRect.substringAfter("\"top\":").substringBefore(",").toFloat()
                val right = cropRect.substringAfter("\"right\":").substringBefore(",").toFloat()
                val bottom = cropRect.substringAfter("\"bottom\":").substringBefore("}").toFloat()

                val leftPercent = (left * 100).toInt()
                val topPercent = (top * 100).toInt()
                val rightPercent = (right * 100).toInt()
                val bottomPercent = (bottom * 100).toInt()

                "裁剪区域: ($leftPercent%,$topPercent%) - ($rightPercent%,$bottomPercent%)"
            } catch (e: Exception) {
                com.example.castapp.utils.AppLog.e("【显示裁剪】解析裁剪区域失败: $cropRect", e)
                "裁剪区域: 启用"
            }
        } else {
            "裁剪区域: 无"
        }
    }
    
    /**
     * 获取可见性显示文本
     */
    fun getVisibilityDisplayText(): String {
        return if (isVisible) "显示: 开启" else "显示: 关闭"
    }
    
    /**
     * 获取镜像状态显示文本
     */
    fun getMirrorDisplayText(): String {
        return if (isMirrored) "镜像: 开启" else "镜像: 关闭"
    }

    /**
     * 🎯 获取横屏模式状态显示文本
     */
    fun getLandscapeModeDisplayText(): String {
        return if (isLandscapeModeEnabled) "横屏: 开启" else "横屏: 关闭"
    }
    
    /**
     * 获取圆角显示文本
     */
    fun getCornerRadiusDisplayText(): String {
        return "圆角: ${cornerRadius.toInt()}dp"
    }
    
    /**
     * 获取透明度显示文本
     */
    fun getAlphaDisplayText(): String {
        return "透明度: ${(alpha * 100).toInt()}%"
    }



    /**
     * 获取边框状态显示文本
     */
    fun getBorderStatusDisplayText(): String {
        return if (isBorderEnabled) "边框: 开启" else "边框: 关闭"
    }

    /**
     * 获取边框宽度显示文本
     */
    fun getBorderWidthDisplayText(): String {
        return "宽度: ${borderWidth.toInt()}dp"
    }

    /**
     * 获取边框颜色显示文本
     */
    fun getBorderColorDisplayText(): String {
        val colorHex = String.format("%08X", borderColor)
        return "颜色: $colorHex"
    }

    /**
     * 🎬 获取视频播放状态显示文本
     * 格式：播放:开启、次数:2、音量:36%
     */
    fun getVideoPlayStatusDisplayText(): String {
        // 只有视频窗口才显示播放状态
        if (!deviceId.startsWith("video_")) {
            return ""
        }

        val playStatus = if (videoPlayEnabled) "开启" else "关闭"
        val loopText = if (videoLoopCount == -1) "∞" else videoLoopCount.toString()
        return "播放:$playStatus、次数:$loopText、音量:${videoVolume}%"
    }
}
