package com.example.castapp.utils

import com.example.castapp.model.CastWindowInfo
import com.example.castapp.model.WindowVisualizationData

/**
 * 🪟 投屏窗口容器缩放计算工具类
 * 负责计算远程接收端控制窗口中投屏窗口容器的可视化参数
 */
object WindowScaleCalculator {

    /**
     * 批量计算投屏窗口容器的可视化数据
     *
     * @param windowInfoList 投屏窗口信息列表
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @param textContentMap 文本窗口内容映射表（可选）
     * @return 可视化数据列表
     */
    fun calculateWindowVisualizationData(
        windowInfoList: List<CastWindowInfo>,
        remoteControlScale: Double,
        textContentMap: Map<String, Pair<String, Map<String, Any>>>? = null
    ): List<WindowVisualizationData> {
        AppLog.d("【可视化计算】开始计算 ${windowInfoList.size} 个窗口的可视化数据")
        
        val visualizationDataList = windowInfoList.mapIndexed { index, windowInfo ->
            // 📝 获取文本窗口的内容和格式信息
            val textContentData = textContentMap?.get(windowInfo.connectionId)
            val textContent = textContentData?.first
            val textFormatData = textContentData?.second

            val visualizationData = WindowVisualizationData.fromCastWindowInfo(
                windowInfo = windowInfo,
                remoteControlScale = remoteControlScale,
                textContent = textContent,
                textFormatData = textFormatData
            )
            
            AppLog.d("【可视化计算】窗口 $index: ${windowInfo.getDisplayTextWithDevice()}")
            AppLog.d("  原始位置: (${windowInfo.positionX}, ${windowInfo.positionY})")
            AppLog.d("  原始尺寸: ${windowInfo.baseWindowWidth}×${windowInfo.baseWindowHeight}")
            AppLog.d("  窗口缩放: ${windowInfo.scaleFactor}")
            AppLog.d("  可视化位置: (${visualizationData.visualizedX}, ${visualizationData.visualizedY})")
            AppLog.d("  可视化尺寸: ${visualizationData.visualizedWidth}×${visualizationData.visualizedHeight}")
            AppLog.d("  层级: ${windowInfo.zOrder}, 旋转: ${windowInfo.rotationAngle}°")
            
            visualizationData
        }
        
        AppLog.d("【可视化计算】完成，生成 ${visualizationDataList.size} 个可视化数据对象")
        return visualizationDataList
    }
    
    /**
     * 计算窗口容器在远程控制窗口中的实际显示区域
     * 考虑窗口旋转、缩放等变换
     * 
     * @param visualizationData 可视化数据
     * @return 实际显示区域的边界矩形 (left, top, right, bottom)
     */
    fun calculateActualDisplayBounds(visualizationData: WindowVisualizationData): FloatArray {
        val centerX = visualizationData.visualizedX + visualizationData.visualizedWidth / 2
        val centerY = visualizationData.visualizedY + visualizationData.visualizedHeight / 2
        
        val halfWidth = visualizationData.visualizedWidth / 2
        val halfHeight = visualizationData.visualizedHeight / 2
        
        // 如果有旋转，需要计算旋转后的边界
        if (visualizationData.rotationAngle != 0f) {
            val radians = Math.toRadians(visualizationData.rotationAngle.toDouble())
            val cos = kotlin.math.cos(radians).toFloat()
            val sin = kotlin.math.sin(radians).toFloat()
            
            // 计算旋转后的四个角点
            val corners = arrayOf(
                Pair(-halfWidth, -halfHeight),
                Pair(halfWidth, -halfHeight),
                Pair(halfWidth, halfHeight),
                Pair(-halfWidth, halfHeight)
            )
            
            val rotatedCorners = corners.map { (x, y) ->
                val rotatedX = x * cos - y * sin + centerX
                val rotatedY = x * sin + y * cos + centerY
                Pair(rotatedX, rotatedY)
            }
            
            val minX = rotatedCorners.minOf { it.first }
            val maxX = rotatedCorners.maxOf { it.first }
            val minY = rotatedCorners.minOf { it.second }
            val maxY = rotatedCorners.maxOf { it.second }
            
            return floatArrayOf(minX, minY, maxX, maxY)
        } else {
            // 无旋转的情况
            return floatArrayOf(
                visualizationData.visualizedX,
                visualizationData.visualizedY,
                visualizationData.visualizedX + visualizationData.visualizedWidth,
                visualizationData.visualizedY + visualizationData.visualizedHeight
            )
        }
    }
    
    /**
     * 验证可视化数据的有效性
     * 
     * @param visualizationData 可视化数据
     * @param containerWidth 容器宽度
     * @param containerHeight 容器高度
     * @return 是否有效
     */
    fun validateVisualizationData(
        visualizationData: WindowVisualizationData,
        containerWidth: Int,
        containerHeight: Int
    ): Boolean {
        // 检查尺寸是否有效
        if (visualizationData.visualizedWidth <= 0 || visualizationData.visualizedHeight <= 0) {
            AppLog.w("【数据验证】窗口尺寸无效: ${visualizationData.getSizeText()}")
            return false
        }
        
        // 检查是否完全超出容器边界
        val bounds = calculateActualDisplayBounds(visualizationData)
        val isCompletelyOutside = bounds[2] < 0 || bounds[0] > containerWidth ||
                                 bounds[3] < 0 || bounds[1] > containerHeight

        // 添加详细的边界验证日志
        AppLog.d("【边界验证】窗口: ${visualizationData.getDeviceDisplayInfo()}")
        AppLog.d("  容器尺寸: ${containerWidth}×${containerHeight}")
        AppLog.d("  可视化位置: (${visualizationData.visualizedX.toInt()}, ${visualizationData.visualizedY.toInt()})")
        AppLog.d("  可视化尺寸: ${visualizationData.visualizedWidth.toInt()}×${visualizationData.visualizedHeight.toInt()}")
        AppLog.d("  旋转角度: ${visualizationData.rotationAngle}°")
        AppLog.d("  计算边界: left=${bounds[0].toInt()}, top=${bounds[1].toInt()}, right=${bounds[2].toInt()}, bottom=${bounds[3].toInt()}")
        AppLog.d("  边界检查: 完全超出=${isCompletelyOutside}")

        if (isCompletelyOutside) {
            AppLog.w("【数据验证】窗口完全超出容器边界: ${visualizationData.getDeviceDisplayInfo()}")
            return false
        }
        
        return true
    }

    /**
     * 🎯 将遥控端坐标转换为接收端实际屏幕坐标
     * @param remoteX 遥控端X坐标（远程控制窗口坐标系）
     * @param remoteY 遥控端Y坐标（远程控制窗口坐标系）
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @return 接收端实际屏幕坐标 Pair(actualX, actualY)
     */
    fun convertRemoteToActualCoordinates(
        remoteX: Float,
        remoteY: Float,
        remoteControlScale: Double
    ): Pair<Float, Float> {
        // 反向缩放计算：遥控端坐标 / 缩放比例 = 接收端实际坐标
        val actualX = (remoteX / remoteControlScale).toFloat()
        val actualY = (remoteY / remoteControlScale).toFloat()

        AppLog.d("【坐标转换】遥控端坐标: ($remoteX, $remoteY)")
        AppLog.d("  缩放比例: $remoteControlScale")
        AppLog.d("  接收端实际坐标: ($actualX, $actualY)")

        return Pair(actualX, actualY)
    }

    /**
     * 🎯 将接收端实际屏幕坐标转换为遥控端坐标
     * @param actualX 接收端实际X坐标
     * @param actualY 接收端实际Y坐标
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @return 遥控端坐标 Pair(remoteX, remoteY)
     */
    fun convertActualToRemoteCoordinates(
        actualX: Float,
        actualY: Float,
        remoteControlScale: Double
    ): Pair<Float, Float> {
        // 正向缩放计算：接收端实际坐标 * 缩放比例 = 遥控端坐标
        val remoteX = (actualX * remoteControlScale).toFloat()
        val remoteY = (actualY * remoteControlScale).toFloat()

        AppLog.d("【坐标转换】接收端实际坐标: ($actualX, $actualY)")
        AppLog.d("  缩放比例: $remoteControlScale")
        AppLog.d("  遥控端坐标: ($remoteX, $remoteY)")

        return Pair(remoteX, remoteY)
    }

    /**
     * 🎯 将遥控端缩放因子转换为接收端实际缩放因子
     * @param remoteScaleFactor 遥控端缩放因子（相对于可视化容器的缩放）
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @return 接收端实际缩放因子
     */
    fun convertRemoteToActualScaleFactor(
        remoteScaleFactor: Float,
        remoteControlScale: Double
    ): Float {
        // 缩放因子转换：遥控端缩放因子直接对应接收端缩放因子
        // 因为缩放是相对变换，不受坐标系缩放影响
        val actualScaleFactor = remoteScaleFactor

        AppLog.d("【缩放转换】遥控端缩放因子: $remoteScaleFactor")
        AppLog.d("  远程控制缩放比例: $remoteControlScale")
        AppLog.d("  接收端实际缩放因子: $actualScaleFactor")

        return actualScaleFactor
    }

    /**
     * 🎯 将接收端实际缩放因子转换为遥控端缩放因子
     * @param actualScaleFactor 接收端实际缩放因子
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @return 遥控端缩放因子
     */
    fun convertActualToRemoteScaleFactor(
        actualScaleFactor: Float,
        remoteControlScale: Double
    ): Float {
        // 缩放因子转换：接收端缩放因子直接对应遥控端缩放因子
        // 因为缩放是相对变换，不受坐标系缩放影响
        val remoteScaleFactor = actualScaleFactor

        AppLog.d("【缩放转换】接收端实际缩放因子: $actualScaleFactor")
        AppLog.d("  远程控制缩放比例: $remoteControlScale")
        AppLog.d("  遥控端缩放因子: $remoteScaleFactor")

        return remoteScaleFactor
    }
}
