package com.example.castapp.ui

import com.example.castapp.model.Connection
import com.example.castapp.websocket.ControlMessage
import org.junit.Test
import org.junit.Assert.*

/**
 * 窗口移除测试
 * 测试投屏窗口移除逻辑
 */
class WindowRemovalTest {

    @Test
    fun testSimplifiedConnection() {
        // 测试简化的连接创建
        val connection = Connection.create("*************", 8080)

        // 验证connectionId不为空
        assertTrue("connectionId应该不为空", connection.connectionId.isNotBlank())

        // 验证基本属性
        assertEquals("IP地址应该正确", "*************", connection.ipAddress)
        assertEquals("端口应该正确", 8080, connection.port)
        assertEquals("显示文本应该正确", "*************:8080", connection.getDisplayText())
    }

    @Test
    fun testConnectionUniqueness() {
        // 测试连接ID的唯一性
        val connection1 = Connection.create("*************", 8080)
        val connection2 = Connection.create("*************", 8080)

        // 即使IP和端口相同，connectionId也应该不同
        assertNotEquals("不同连接的connectionId应该不同",
            connection1.connectionId, connection2.connectionId)
    }

    @Test
    fun testWindowMappingLogic() {
        // 测试窗口映射逻辑
        val connection = Connection.create("*************", 8080)
        val connectionId = connection.connectionId

        // 窗口映射直接使用connectionId
        val windowMappings = mutableMapOf<String, String>()
        windowMappings[connectionId] = "window_instance"

        // 验证窗口映射
        assertTrue("应该能通过connectionId找到窗口", windowMappings.containsKey(connectionId))
        assertEquals("窗口实例应该正确", "window_instance", windowMappings[connectionId])

        // 验证ID不为空
        assertTrue("connectionId应该不为空", connectionId.isNotBlank())
    }

    @Test
    fun testConnectionValidation() {
        // 测试连接对象的基本验证
        val validConnection = Connection.create("*************", 8080)
        assertTrue("有效连接的ID应该不为空", validConnection.connectionId.isNotBlank())
        assertEquals("IP地址应该正确", "*************", validConnection.ipAddress)
        assertEquals("端口应该正确", 8080, validConnection.port)

        // 测试空ID连接
        val invalidConnection = Connection("", "*************", 8080)
        assertTrue("空connectionId应该为空", invalidConnection.connectionId.isBlank())
    }

    @Test
    fun testConnectionIdGeneration() {
        // 测试connectionId生成的唯一性
        val connectionIds = mutableSetOf<String>()
        repeat(100) {
            val connectionId = Connection.generateConnectionId()
            assertTrue("生成的connectionId应该不为空", connectionId.isNotBlank())
            assertTrue("生成的connectionId应该是唯一的", connectionIds.add(connectionId))
        }
    }

    @Test
    fun testWindowRemovalByConnectionId() {
        // 测试通过connectionId移除窗口的逻辑
        val connection = Connection.create("*************", 8080)
        val connectionId = connection.connectionId

        // 模拟窗口映射
        val windowMappings = mutableMapOf<String, String>()
        windowMappings[connectionId] = "window_to_remove"

        // 模拟窗口移除过程
        var windowRemoved = false
        if (windowMappings.containsKey(connectionId)) {
            windowMappings.remove(connectionId)
            windowRemoved = true
        }

        assertTrue("应该成功移除窗口", windowRemoved)
        assertFalse("移除后映射中不应该包含该connectionId",
            windowMappings.containsKey(connectionId))
    }

    @Test
    fun testDisconnectMessageCreation() {
        // 测试断开连接消息的创建
        val connection = Connection.create("*************", 8080)
        val connectionId = connection.connectionId

        // 在统一ID架构下，断开消息使用connectionId
        val disconnectMessage = ControlMessage.createDisconnect(connectionId)
        assertEquals("断开消息类型应该正确", ControlMessage.TYPE_DISCONNECT, disconnectMessage.type)
        assertEquals("断开消息的ID应该是connectionId", connectionId, disconnectMessage.connectionId)

        // 验证ID不为空
        assertTrue("connectionId应该不为空", connectionId.isNotBlank())
    }
}
