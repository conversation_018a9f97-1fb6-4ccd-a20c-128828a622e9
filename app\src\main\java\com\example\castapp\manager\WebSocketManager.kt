package com.example.castapp.manager

import android.annotation.SuppressLint
import android.content.Context
import com.example.castapp.model.Connection
import com.example.castapp.websocket.WebSocketClient
import com.example.castapp.websocket.ControlMessage
import kotlinx.coroutines.*
import java.net.URI
import java.util.concurrent.ConcurrentHashMap
import com.example.castapp.utils.AppLog

/**
 * 统一WebSocket连接管理器 - 彻底重构版本
 * 核心原则：每个Connection只维护一个WebSocket连接，所有功能共享
 * 通过消息类型区分不同功能，而不是创建多个连接
 * 🎯 修复：添加Context支持DPI密度信息获取
 */
class WebSocketManager private constructor() {

    companion object {
        @Volatile
        private var INSTANCE: WebSocketManager? = null

        // 🎯 新增：Context缓存，用于DPI密度信息获取
        @Volatile
        private var applicationContext: Context? = null

        fun getInstance(): WebSocketManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WebSocketManager().also { INSTANCE = it }
            }
        }

        /**
         * 🎯 新增：设置应用上下文
         */
        fun setApplicationContext(context: Context) {
            applicationContext = context.applicationContext
        }

        // 功能类型常量
        const val FUNCTION_VIDEO = "video"
        const val FUNCTION_MEDIA_AUDIO = "media_audio"
        const val FUNCTION_MIC_AUDIO = "mic_audio"
    }

    // 连接ID到WebSocket客户端的映射（一对一关系）
    private val webSocketClients = ConcurrentHashMap<String, WebSocketClient>()

    // 连接ID到活跃功能集合的映射
    private val activeFunctions = ConcurrentHashMap<String, MutableSet<String>>()

    // 🐾 新增：功能启用状态跟踪，防止重复发送功能控制消息
    private val functionEnabledStates = ConcurrentHashMap<String, MutableSet<String>>()

    // 连接信息由StateManager统一管理，移除重复存储

    // 连接状态监听器
    private val connectionStateListeners = ConcurrentHashMap<String, (Boolean) -> Unit>()

    // 🚀 修复：消息接收监听器支持多个监听器
    private val messageListeners = ConcurrentHashMap<String, MutableList<(ControlMessage) -> Unit>>()

    // 协程作用域
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // ConnectionInfo已被Connection模型替代，移除重复定义

    /**
     * 启动连接功能
     * @param connection 连接对象
     * @param functionType 功能类型
     * @return 是否成功启动
     */
    fun startFunction(
        connection: Connection,
        functionType: String
    ): Boolean {
        val connectionId = connection.connectionId

        AppLog.websocket("启动功能: 连接ID=$connectionId, 功能类型=$functionType")

        // 🐾 根源修复：检查功能是否已经启用，避免重复发送
        val enabledFunctions = functionEnabledStates.getOrPut(connectionId) { ConcurrentHashMap.newKeySet() }
        if (enabledFunctions.contains(functionType)) {
            AppLog.websocket("功能已启用，跳过重复发送: $functionType -> $connectionId")
            return true
        }

        // 记录活跃功能类型
        val functions = activeFunctions.getOrPut(connectionId) { ConcurrentHashMap.newKeySet() }
        functions.add(functionType)

        // 🐾 修复：检查WebSocket连接是否真正有效
        val existingClient = webSocketClients[connectionId]
        val isConnectionValid = existingClient?.isOpen == true

        if (!isConnectionValid) {
            // 清理无效的连接
            if (existingClient != null) {
                AppLog.websocket("检测到无效WebSocket连接，清理并重建: $connectionId")
                cleanupConnection(connectionId)
            }
            // 创建新的WebSocket连接
            AppLog.websocket("创建新的WebSocket连接: $connectionId")
            if (createWebSocketConnection(connection, connectionId)) {
                // 连接建立后，发送功能启用消息
                val webSocketClient = webSocketClients[connectionId]
                if (webSocketClient != null) {
                    // 🐾 根源修复：只发送一次功能启用消息
                    sendFunctionControlMessage(webSocketClient, connectionId, functionType, true)
                }
                return true
            } else {
                return false
            }
        } else {
            AppLog.websocket("WebSocket连接有效，复用连接: $connectionId")

            // 发送功能启用消息，通知接收端新增功能
            val webSocketClient = webSocketClients[connectionId]
            if (webSocketClient != null) {
                // 🐾 根源修复：只发送一次功能启用消息
                sendFunctionControlMessage(webSocketClient, connectionId, functionType, true)
            }
            return true
        }
    }

    /**
     * 停止连接功能
     * @param connection 连接对象
     * @param functionType 功能类型
     */
    fun stopFunction(connection: Connection, functionType: String) {
        val connectionId = connection.connectionId

        AppLog.websocket("停止功能: 连接ID=$connectionId, 功能类型=$functionType")

        // 从活跃功能集合中移除
        val functions = activeFunctions[connectionId]
        functions?.remove(functionType)

        // 🐾 根源修复：从功能启用状态中移除
        val enabledFunctions = functionEnabledStates[connectionId]
        enabledFunctions?.remove(functionType)

        // 获取WebSocket连接
        val webSocketClient = webSocketClients[connectionId]
        if (webSocketClient != null) {
            // 🐾 根源修复：使用统一的消息发送方法
            sendFunctionControlMessage(webSocketClient, connectionId, functionType, false)

            // 如果停止的是视频功能，发送视频流停止消息
            if (functionType == "video") {
                webSocketClient.sendVideoStreamStop()
                AppLog.websocket("已发送视频流停止消息: 连接ID=$connectionId")
            }
        }

        // 【正确的连接管理逻辑】
        // 只有当所有功能都停止时，才断开WebSocket连接
        if (functions?.isEmpty() == true) {
            AppLog.websocket("连接的所有功能已停止，立即断开WebSocket连接: $connectionId")
            // 🐾 根源修复：清理功能启用状态
            functionEnabledStates.remove(connectionId)
            // 立即同步断开连接，确保心跳停止
            try {
                destroyWebSocketConnectionSync(connectionId)
                AppLog.websocket("WebSocket连接已完全断开: $connectionId")
            } catch (e: Exception) {
                AppLog.e("同步销毁WebSocket连接失败: $connectionId", e)
            }
        } else {
            AppLog.websocket("连接仍有活跃功能，保持WebSocket连接: $connectionId, 剩余功能: ${functions?.toList()}")
        }
    }

    /**
     * 创建纯净WebSocket连接（不携带功能信息）- 简化版
     * 使用Connection模型的统一端口管理
     */
    private fun createWebSocketConnection(connection: Connection, connectionId: String): Boolean {
        try {
            val targetIp = connection.ipAddress
            // val targetPort = connection.port // 未使用的变量已移除
            val webSocketPort = connection.getWebSocketPort() // 使用连接的统一端口方法

            AppLog.websocket("创建纯净WebSocket连接: 连接ID=$connectionId, 地址=$targetIp:$webSocketPort")

            // 连接信息由StateManager统一管理，无需重复存储

            // 创建纯净WebSocket客户端
            val webSocketUri = URI("ws://$targetIp:$webSocketPort")

            // 使用connectionId作为WebSocket层标识符
            // 这确保同一发送端的所有功能（投屏、媒体音频、麦克风）共享一个WebSocket连接
            val webSocketLayerId = connectionId

            AppLog.websocket("WebSocketManager创建连接: connectionId=$connectionId, 目标地址=$targetIp:$webSocketPort")

            val webSocketClient = WebSocketClient(
                webSocketUri,
                webSocketLayerId, // 使用connectionId作为WebSocket层标识符
                applicationContext ?: throw IllegalStateException("ApplicationContext未设置，请先调用setApplicationContext"), // 🎯 新增：传递Context
                onMessageReceived = { controlMessage ->
                    // 🚀 修复：通知所有消息监听器
                    messageListeners[connectionId]?.forEach { listener ->
                        try {
                            listener(controlMessage)
                        } catch (e: Exception) {
                            AppLog.e("消息监听器处理失败: $connectionId", e)
                        }
                    }
                },
                onConnectionStateChanged = { isConnected ->
                    AppLog.websocket("WebSocket连接状态变化: 连接ID=$connectionId, WebSocket层ID=$webSocketLayerId, 连接=$isConnected")
                    // 通知连接状态监听器
                    connectionStateListeners[connectionId]?.invoke(isConnected)

                    // 🚀 新增：处理WebSocket异常断开（如接收端APP被强制结束）
                    if (!isConnected) {
                        AppLog.websocket("检测到WebSocket连接异常断开，开始清理: $connectionId")
                        handleWebSocketAbnormalDisconnection(connectionId)
                    }
                }
            )

            // 尝试连接
            if (webSocketClient.connectToServer()) {
                webSocketClients[connectionId] = webSocketClient
                AppLog.websocket("纯净WebSocket连接创建成功: 连接ID=$connectionId")
                return true
            } else {
                AppLog.w("纯净WebSocket连接失败: 连接ID=$connectionId")
                return false
            }

        } catch (e: Exception) {
            AppLog.e("创建纯净WebSocket连接异常: 连接ID=$connectionId", e)
            return false
        }
    }

    /**
     * 销毁WebSocket连接 - 同步版本，立即断开连接
     */
    private fun destroyWebSocketConnectionSync(connectionId: String) {
        AppLog.websocket("同步销毁WebSocket连接: 连接ID=$connectionId")

        val webSocketClient = webSocketClients.remove(connectionId)
        webSocketClient?.let {
            try {
                // 在断开连接之前发送断开连接消息给接收端
                if (it.isOpen) {
                    AppLog.websocket("发送断开连接消息: 连接ID=$connectionId")
                    it.sendDisconnect()

                    // 给发送端一点时间处理断开消息
                    Thread.sleep(50)
                }

                it.disconnect()
                AppLog.websocket("WebSocket连接已断开: 连接ID=$connectionId")
            } catch (e: Exception) {
                AppLog.e("断开WebSocket连接失败: 连接ID=$connectionId", e)
            }
        }

        // 清理相关数据
        cleanupConnection(connectionId)

        // 🚀 根源优化：在这里统一处理状态更新，避免重复调用
        updateConnectionStateOnDisconnect(connectionId)
    }

    /**
     * 🔥 移除消息去重：允许重复发送功能控制消息
     */
    private fun sendFunctionControlMessage(
        webSocketClient: WebSocketClient,
        connectionId: String,
        functionType: String,
        enabled: Boolean
    ) {
        val enabledFunctions = functionEnabledStates.getOrPut(connectionId) { ConcurrentHashMap.newKeySet() }

        if (enabled) {
            // 🔥 移除去重检查：允许重复启用
            // if (enabledFunctions.contains(functionType)) {
            //     AppLog.websocket("功能已启用，跳过重复发送: $functionType -> $connectionId")
            //     return
            // }
            enabledFunctions.add(functionType)
        } else {
            // 禁用功能：直接移除
            enabledFunctions.remove(functionType)
        }

        val controlMessage = ControlMessage.createFunctionControl(connectionId, functionType, enabled)
        webSocketClient.sendMessage(controlMessage)
        val action = if (enabled) "启用" else "禁用"
        AppLog.websocket("已发送功能${action}消息: $functionType -> $connectionId")
    }

    /**
     * 清理连接相关数据 - 🚀 优化：支持异常断开处理
     */
    private fun cleanupConnection(connectionId: String) {
        // 获取当前连接的活跃功能
        val functions = activeFunctions[connectionId]

        // 如果有功能在运行，需要停止相应的服务
        if (functions != null) {
            AppLog.websocket("WebSocket连接断开，清理活跃功能: $connectionId, 功能: ${functions.toList()}")

            // 停止视频功能（投屏）
            if (functions.contains("video")) {
                AppLog.websocket("WebSocket断开，停止投屏服务: $connectionId")
                stopCastingServiceForConnection(connectionId)
            }

            // 停止媒体音频功能
            if (functions.contains("media_audio")) {
                AppLog.websocket("WebSocket断开，停止媒体音频服务: $connectionId")
                stopAudioServiceForConnection(connectionId, "media")
            }

            // 停止麦克风音频功能
            if (functions.contains("mic_audio")) {
                AppLog.websocket("WebSocket断开，停止麦克风音频服务: $connectionId")
                stopAudioServiceForConnection(connectionId, "mic")
            }
        } else {
            AppLog.websocket("连接 $connectionId 没有活跃功能需要清理")
        }

        // 🐾 修复：清理WebSocket客户端引用
        webSocketClients.remove(connectionId)
        activeFunctions.remove(connectionId)
        // 🐾 根源修复：清理功能启用状态
        functionEnabledStates.remove(connectionId)
        // 连接信息由StateManager统一管理，无需清理
        connectionStateListeners.remove(connectionId)
        messageListeners.remove(connectionId)

        AppLog.websocket("已清理连接数据: 连接ID=$connectionId")
    }

    /**
     * 停止指定连接的投屏服务
     */
    private fun stopCastingServiceForConnection(connectionId: String) {
        try {
            val context = getApplicationContext()
            if (context is android.app.Application) {
                val intent = android.content.Intent(context, com.example.castapp.service.CastingService::class.java).apply {
                    action = com.example.castapp.service.CastingService.ACTION_STOP_CASTING
                    putExtra(com.example.castapp.service.CastingService.EXTRA_CONNECTION_ID, connectionId)
                }
                context.startService(intent)
                AppLog.websocket("已发送停止投屏服务的Intent: $connectionId")
            } else {
                AppLog.w("无法获取应用上下文，无法停止投屏服务: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("停止投屏服务失败: connectionId=$connectionId", e)
        }
    }

    /**
     * 停止指定连接的音频服务
     */
    private fun stopAudioServiceForConnection(connectionId: String, audioType: String) {
        try {
            val context = getApplicationContext()
            if (context is android.app.Application) {

                val intent = android.content.Intent(context, com.example.castapp.service.AudioService::class.java).apply {
                    action = when (audioType) {
                        "media" -> com.example.castapp.service.AudioService.ACTION_STOP_MEDIA_AUDIO
                        "mic" -> com.example.castapp.service.AudioService.ACTION_STOP_MIC_AUDIO
                        else -> return@apply
                    }
                    putExtra(com.example.castapp.service.AudioService.EXTRA_CONNECTION_ID, connectionId)
                }
                context.startService(intent)
                AppLog.websocket("已发送停止${audioType}音频服务的Intent: $connectionId")
            } else {
                AppLog.w("无法获取应用上下文，无法停止音频服务: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("停止音频服务失败: connectionId=$connectionId, audioType=$audioType", e)
        }
    }

    /**
     * 🚀 新增：处理WebSocket异常断开（如接收端APP被强制结束）
     */
    private fun handleWebSocketAbnormalDisconnection(connectionId: String) {
        scope.launch(Dispatchers.Main) {
            try {
                AppLog.websocket("⚠️ 处理WebSocket异常断开: $connectionId")

                // 清理连接相关数据和服务
                cleanupConnection(connectionId)

                // 更新连接状态
                updateConnectionStateOnDisconnect(connectionId)

                AppLog.websocket("⚠️ WebSocket异常断开处理完成: $connectionId")
            } catch (e: Exception) {
                AppLog.e("处理WebSocket异常断开失败: $connectionId", e)
            }
        }
    }

    /**
     * 更新连接状态为断开状态，自动恢复所有开关
     */
    private fun updateConnectionStateOnDisconnect(connectionId: String) {
        scope.launch(Dispatchers.Main) {
            try {
                val context = getApplicationContext()
                if (context is android.app.Application) {
                    // 直接调用StateManager的方法
                    val stateManager = StateManager.getInstance(context)
                    stateManager.handleWebSocketDisconnected(connectionId)
                    AppLog.websocket("WebSocket断开，已通知StateManager更新状态: $connectionId")
                } else {
                    AppLog.w("无法获取Application实例，无法更新连接状态: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("更新连接状态失败: connectionId=$connectionId", e)
            }
        }
    }

    /**
     * 获取应用上下文
     */
    @SuppressLint("PrivateApi")
    private fun getApplicationContext(): android.content.Context? {
        return try {
            // 通过反射获取应用上下文
            val activityThreadClass = Class.forName("android.app.ActivityThread")
            val currentApplicationMethod = activityThreadClass.getMethod("currentApplication")
            currentApplicationMethod.invoke(null) as? android.content.Context
        } catch (e: Exception) {
            AppLog.w("无法通过反射获取应用上下文", e)
            null
        }
    }

    /**
     * 获取WebSocket客户端（统一连接管理）
     */
    fun getWebSocketClient(connectionId: String): WebSocketClient? {
        return webSocketClients[connectionId]
    }




    /**
     * 设置连接状态监听器
     */
    fun setConnectionStateListener(connectionId: String, listener: (Boolean) -> Unit) {
        connectionStateListeners[connectionId] = listener
    }

    /**
     * 🚀 修复：设置消息监听器（支持多个监听器）
     */
    fun setMessageListener(connectionId: String, listener: (ControlMessage) -> Unit) {
        messageListeners.computeIfAbsent(connectionId) { mutableListOf() }.add(listener)
        AppLog.websocket("已添加消息监听器: $connectionId, 当前监听器数量: ${messageListeners[connectionId]?.size}")
    }

    /**
     * 🚀 修复：移除监听器（清空所有消息监听器）
     */
    fun removeListeners(connectionId: String) {
        connectionStateListeners.remove(connectionId)
        val removedCount = messageListeners.remove(connectionId)?.size ?: 0
        AppLog.websocket("已移除连接的所有监听器: $connectionId, 移除的消息监听器数量: $removedCount")
    }



    /**
     * 发送投屏状态
     */
    fun sendCastingState(connectionId: String, isCasting: Boolean, message: String = ""): Boolean {
        val webSocketClient = webSocketClients[connectionId]
        return webSocketClient?.sendCastingState(isCasting, message) ?: false
    }

    /**
     * 检查指定连接的WebSocket是否处于活跃状态
     * @param connectionId 连接ID
     * @return true表示连接存在且活跃，false表示连接不存在或已断开
     */
    fun isConnectionActive(connectionId: String): Boolean {
        val webSocketClient = webSocketClients[connectionId]
        return webSocketClient != null && webSocketClient.isOpen
    }

    /**
     * 🗑️ 强制断开WebSocket连接（用于窗口删除等场景）- 优化版本
     * 不依赖于功能状态判断，直接断开连接，避免重复处理
     */
    fun forceDisconnectConnection(connectionId: String) {
        AppLog.websocket("【强制断开】开始强制断开WebSocket连接: $connectionId")

        try {
            // 🚀 优化：使用专门的删除断开方法，避免状态更新冲突
            destroyWebSocketConnectionForDeletion(connectionId)
            AppLog.websocket("【强制断开】WebSocket连接已强制断开: $connectionId")
        } catch (e: Exception) {
            AppLog.e("【强制断开】强制断开WebSocket连接失败: $connectionId", e)
        }
    }

    /**
     * 🚀 专门用于窗口删除的WebSocket连接销毁方法
     * 避免与正常断开流程产生冲突
     */
    private fun destroyWebSocketConnectionForDeletion(connectionId: String) {
        AppLog.websocket("【删除断开】销毁WebSocket连接: 连接ID=$connectionId")

        val webSocketClient = webSocketClients.remove(connectionId)
        webSocketClient?.let {
            try {
                // 🚀 优化：窗口删除时不发送断开消息，避免接收端重复处理
                // 因为删除操作本身就是主动断开，不需要通知接收端
                AppLog.websocket("【删除断开】跳过发送断开消息，直接断开连接: 连接ID=$connectionId")

                it.disconnect()
                AppLog.websocket("【删除断开】WebSocket连接已断开: 连接ID=$connectionId")
            } catch (e: Exception) {
                AppLog.e("【删除断开】断开WebSocket连接失败: 连接ID=$connectionId", e)
            }
        }

        // 清理相关数据
        cleanupConnection(connectionId)

        // 🚀 优化：窗口删除时不更新StateManager状态，由删除流程统一处理
        AppLog.websocket("【删除断开】跳过StateManager状态更新，由删除流程统一处理: $connectionId")
    }
}
