package com.example.castapp.ui.windowsettings

import android.content.Context
import android.graphics.SurfaceTexture
import android.view.Surface
import android.view.TextureView
import android.widget.FrameLayout
import com.example.castapp.manager.MultiCameraManager
import com.example.castapp.ui.windowsettings.interfaces.SurfaceStateListener
import com.example.castapp.utils.AppLog

/**
 * Surface管理器
 * 负责TextureView和Surface的生命周期管理
 */
class SurfaceManager(
    private val context: Context,
    private val container: FrameLayout
) {
    
    // TextureView实例
    private var textureView: TextureView? = null
    
    // 连接ID
    private var connectionId: String = ""
    
    // Surface状态监听器
    private var surfaceStateListener: SurfaceStateListener? = null
    
    /**
     * 设置Surface状态监听器
     */
    fun setSurfaceStateListener(listener: SurfaceStateListener?) {
        this.surfaceStateListener = listener
    }
    
    /**
     * 为指定连接创建TextureView和Surface
     * @param connectionId 连接ID
     */
    fun setupTextureView(connectionId: String) {
        this.connectionId = connectionId
        
        // 移除旧的TextureView（如果存在）
        textureView?.let { 
            container.removeView(it)
            AppLog.d("移除旧的TextureView: $connectionId")
        }
        
        // 创建新的TextureView
        textureView = TextureView(context).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            
            surfaceTextureListener = createSurfaceTextureListener()
        }
        
        // 添加TextureView到容器
        container.addView(textureView)
        
        AppLog.d("为连接 $connectionId 创建TextureView完成")
    }
    
    /**
     * 创建SurfaceTextureListener
     */
    private fun createSurfaceTextureListener(): TextureView.SurfaceTextureListener {
        return object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                AppLog.d("TextureView Surface可用: connectionId=$connectionId, size=${width}x${height}")
                
                val viewSurface = Surface(surface)
                surfaceStateListener?.onSurfaceAvailable(viewSurface, connectionId)
                
                AppLog.d("已为连接 $connectionId 设置Surface")
            }
            
            override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {
                AppLog.d("TextureView Surface尺寸改变: connectionId=$connectionId, size=${width}x${height}")
            }
            
            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                AppLog.d("TextureView Surface销毁: connectionId=$connectionId")
                surfaceStateListener?.onSurfaceDestroyed(connectionId)
                return true
            }
            
            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
                // 帧更新，通常不需要处理
            }
        }
    }
    
    /**
     * 为摄像头创建TextureView和Surface
     */
    fun setupCameraTextureView(cameraId: String, cameraName: String) {
        this.connectionId = cameraId

        // 移除旧的TextureView（如果存在）
        textureView?.let {
            container.removeView(it)
            AppLog.d("移除旧的TextureView: $cameraId")
        }

        // 创建新的TextureView
        textureView = TextureView(context).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )

            surfaceTextureListener = createCameraSurfaceTextureListener(cameraName)
        }

        // 添加TextureView到容器
        container.addView(textureView)

        AppLog.d("为摄像头 $cameraName (ID: $cameraId) 创建TextureView完成")
    }

    /**
     * 创建摄像头SurfaceTextureListener
     */
    private fun createCameraSurfaceTextureListener(cameraName: String): TextureView.SurfaceTextureListener {
        return object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                AppLog.d("摄像头TextureView Surface可用: $cameraName, size=${width}x${height}")

                // 创建摄像头预览
                val multiCameraManager = MultiCameraManager.getInstance()
                val isFrontCamera = cameraName == "前置摄像头"

                multiCameraManager.startCameraPreview(context, connectionId, surface, isFrontCamera) { success ->
                    if (success) {
                        AppLog.d("摄像头预览启动成功: $cameraName")
                    } else {
                        AppLog.e("摄像头预览启动失败: $cameraName")
                    }
                }
            }

            override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {
                AppLog.d("摄像头TextureView Surface尺寸改变: $cameraName, size=${width}x${height}")
            }

            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                AppLog.d("摄像头TextureView Surface销毁: $cameraName")

                // 停止摄像头预览
                val multiCameraManager = MultiCameraManager.getInstance()
                multiCameraManager.stopCameraPreview(connectionId)

                return true
            }

            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
                // 帧更新，通常不需要处理
            }
        }
    }

    /**
     * 获取当前TextureView
     */
    fun getTextureView(): TextureView? = textureView
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            AppLog.d("开始清理SurfaceManager: connectionId=$connectionId")
            
            // 先通知外部Surface不可用
            if (connectionId.isNotEmpty()) {
                AppLog.d("通知外部清理连接 $connectionId 的Surface")
                surfaceStateListener?.onSurfaceAvailable(null, connectionId)
                
                // 等待一小段时间，确保外部有足够时间处理Surface清理
                try {
                    Thread.sleep(50) // 等待50ms让MediaCodec有时间处理Surface清理
                    AppLog.d("Surface清理通知等待完成: connectionId=$connectionId")
                } catch (_: InterruptedException) {
                    Thread.currentThread().interrupt()
                    AppLog.w("Surface清理等待被中断: connectionId=$connectionId")
                }
            }
            
            // 🚀 优化：清理摄像头预览（如果是摄像头窗口）
            if (connectionId == "front_camera" || connectionId == "rear_camera") {
                try {
                    val multiCameraManager = MultiCameraManager.getInstance()
                    multiCameraManager.stopCameraPreview(connectionId)
                    AppLog.d("已停止摄像头预览: connectionId=$connectionId")
                } catch (e: Exception) {
                    AppLog.e("停止摄像头预览失败: connectionId=$connectionId", e)
                }
            }

            // 清理TextureView
            textureView?.let { textureView ->
                try {
                    AppLog.d("开始清理TextureView: connectionId=$connectionId, isAvailable=${textureView.isAvailable}")

                    // 安全清理SurfaceTextureListener
                    if (textureView.isAvailable) {
                        try {
                            // 清除监听器
                            textureView.surfaceTextureListener = null
                            AppLog.d("已清理SurfaceTextureListener: connectionId=$connectionId")
                        } catch (e: Exception) {
                            AppLog.w("清理SurfaceTextureListener时发生异常: connectionId=$connectionId", e)
                        }
                    }

                    // 从容器中安全移除TextureView
                    try {
                        container.removeView(textureView)
                        AppLog.d("已从容器移除TextureView: connectionId=$connectionId")
                    } catch (e: Exception) {
                        AppLog.w("从容器移除TextureView时发生异常: connectionId=$connectionId", e)
                    }

                } catch (e: Exception) {
                    AppLog.e("清理TextureView时发生异常: connectionId=$connectionId", e)
                }
            }
            
            // 清理引用和状态
            textureView = null
            val oldConnectionId = connectionId
            connectionId = ""
            surfaceStateListener = null
            
            AppLog.d("SurfaceManager清理完成: oldConnectionId=$oldConnectionId")
            
        } catch (e: Exception) {
            AppLog.e("SurfaceManager清理时发生异常", e)
        }
    }
}
