package com.example.castapp.manager

import android.content.Context
import com.example.castapp.service.FloatingStopwatchService
import com.example.castapp.R
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.ToastUtils
import java.lang.ref.WeakReference

/**
 * 悬浮窗管理器
 * 负责管理悬浮窗权限和悬浮窗服务的启动
 */
class FloatingWindowManager private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: FloatingWindowManager? = null
        
        fun getInstance(): FloatingWindowManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FloatingWindowManager().also { INSTANCE = it }
            }
        }
    }
    
    // 当前Context的弱引用
    private var currentContextRef: WeakReference<Context>? = null

    /**
     * 初始化管理器
     */
    fun initialize(context: Context) {
        this.currentContextRef = WeakReference(context.applicationContext)
        AppLog.d("FloatingWindowManager初始化完成")
    }
    
    /**
     * 请求悬浮窗权限并启动秒表
     */
    fun requestOverlayPermissionAndStartStopwatch(permissionHelper: PermissionManager.ActivityPermissionHelper) {
        permissionHelper.requestOverlayPermission(object : PermissionManager.OverlayPermissionCallback {
            override fun onOverlayPermissionGranted() {
                AppLog.d("悬浮窗权限已授予，启动悬浮秒表")
                startFloatingStopwatch()
            }
            
            override fun onOverlayPermissionDenied() {
                AppLog.w("悬浮窗权限被拒绝")
                val context = currentContextRef?.get() ?: return
                ToastUtils.showToast(context, context.getString(R.string.overlay_permission_required))
            }
        })
    }
    
    /**
     * 启动悬浮秒表
     */
    private fun startFloatingStopwatch() {
        val context = currentContextRef?.get() ?: return

        try {
            FloatingStopwatchService.startService(context)
            ToastUtils.showToast(context, context.getString(R.string.stopwatch_started))
            AppLog.d("悬浮秒表启动成功")
        } catch (e: Exception) {
            AppLog.e("启动悬浮秒表失败", e)
            ToastUtils.showToast(context, context.getString(R.string.stopwatch_start_failed))
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        currentContextRef?.clear()
        currentContextRef = null
        AppLog.d("FloatingWindowManager清理完成")
    }
}
