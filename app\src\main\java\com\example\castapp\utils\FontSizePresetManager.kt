package com.example.castapp.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 全局字号预设管理器
 * 负责管理所有文本窗口共享的字号预设列表
 * 提供持久化存储和实时同步功能
 */
class FontSizePresetManager private constructor(context: Context) {
    
    companion object {
        private const val PREF_NAME = "font_size_preset_preferences"
        private const val KEY_CUSTOM_FONT_SIZES = "custom_font_sizes"
        
        // 预设字号（不可删除）
        val PRESET_FONT_SIZES = listOf(10, 11, 12, 13, 14)
        
        // 单例实例
        @Volatile
        private var INSTANCE: FontSizePresetManager? = null
        
        /**
         * 获取单例实例
         * 使用 applicationContext 避免内存泄漏
         */
        fun getInstance(context: Context): FontSizePresetManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FontSizePresetManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    
    private val gson = Gson()
    
    // 字号列表变化监听器
    private val listeners = mutableSetOf<FontSizePresetListener>()
    
    /**
     * 字号预设列表变化监听器接口
     */
    interface FontSizePresetListener {
        /**
         * 字号被添加时调用
         */
        fun onFontSizeAdded(fontSize: Int)
        
        /**
         * 字号被删除时调用
         */
        fun onFontSizeDeleted(fontSize: Int)
        
        /**
         * 字号列表被重置时调用
         */
        fun onFontSizeListReset()
    }
    
    /**
     * 获取完整的字号列表（预设 + 自定义）
     */
    fun getAllFontSizes(): List<Int> {
        val customFontSizes = getCustomFontSizes()
        val allFontSizes = (PRESET_FONT_SIZES + customFontSizes).distinct().sorted()
        
        AppLog.d("【字号预设管理器】获取完整字号列表: $allFontSizes")
        return allFontSizes
    }
    
    /**
     * 获取字号选项字符串列表（用于Spinner显示）
     */
    fun getAllFontSizeOptions(): List<String> {
        return getAllFontSizes().map { "${it}sp" }
    }
    
    /**
     * 获取自定义字号列表
     */
    private fun getCustomFontSizes(): List<Int> {
        return try {
            val json = sharedPreferences.getString(KEY_CUSTOM_FONT_SIZES, null)
            if (json != null) {
                val type = object : TypeToken<List<Int>>() {}.type
                gson.fromJson(json, type) ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            AppLog.e("【字号预设管理器】获取自定义字号失败", e)
            emptyList()
        }
    }
    
    /**
     * 保存自定义字号列表
     */
    private fun saveCustomFontSizes(customFontSizes: List<Int>) {
        try {
            val json = gson.toJson(customFontSizes)
            sharedPreferences.edit {
                putString(KEY_CUSTOM_FONT_SIZES, json)
            }

            AppLog.d("【字号预设管理器】自定义字号已保存: $customFontSizes")
        } catch (e: Exception) {
            AppLog.e("【字号预设管理器】保存自定义字号失败", e)
        }
    }
    
    /**
     * 添加自定义字号
     */
    fun addCustomFontSize(fontSize: Int): Boolean {
        try {
            // 检查是否已存在
            if (getAllFontSizes().contains(fontSize)) {
                AppLog.d("【字号预设管理器】字号${fontSize}sp已存在")
                return false
            }
            
            // 检查字号范围
            if (fontSize < 4 || fontSize > 90) {
                AppLog.w("【字号预设管理器】字号${fontSize}sp超出有效范围(4-90)")
                return false
            }
            
            // 添加到自定义列表
            val customFontSizes = getCustomFontSizes().toMutableList()
            customFontSizes.add(fontSize)
            customFontSizes.sort()
            
            // 保存到存储
            saveCustomFontSizes(customFontSizes)
            
            // 通知监听器
            notifyFontSizeAdded(fontSize)
            
            AppLog.d("【字号预设管理器】已添加自定义字号: ${fontSize}sp")
            return true
            
        } catch (e: Exception) {
            AppLog.e("【字号预设管理器】添加自定义字号失败", e)
            return false
        }
    }
    
    /**
     * 删除自定义字号
     */
    fun deleteCustomFontSize(fontSize: Int): Boolean {
        try {
            // 检查是否为预设字号
            if (PRESET_FONT_SIZES.contains(fontSize)) {
                AppLog.w("【字号预设管理器】不能删除预设字号: ${fontSize}sp")
                return false
            }
            
            // 从自定义列表中移除
            val customFontSizes = getCustomFontSizes().toMutableList()
            val removed = customFontSizes.remove(fontSize)
            
            if (removed) {
                // 保存到存储
                saveCustomFontSizes(customFontSizes)
                
                // 通知监听器
                notifyFontSizeDeleted(fontSize)
                
                AppLog.d("【字号预设管理器】已删除自定义字号: ${fontSize}sp")
                return true
            } else {
                AppLog.w("【字号预设管理器】要删除的字号不存在: ${fontSize}sp")
                return false
            }
            
        } catch (e: Exception) {
            AppLog.e("【字号预设管理器】删除自定义字号失败", e)
            return false
        }
    }
    
    /**
     * 重置为默认字号设置（清除所有自定义字号）
     */
    fun resetToDefault() {
        try {
            // 清除自定义字号
            saveCustomFontSizes(emptyList())
            
            // 通知监听器
            notifyFontSizeListReset()
            
            AppLog.d("【字号预设管理器】已重置为默认字号设置")
            
        } catch (e: Exception) {
            AppLog.e("【字号预设管理器】重置默认设置失败", e)
        }
    }
    
    /**
     * 检查字号是否为预设字号
     */
    fun isPresetFontSize(fontSize: Int): Boolean {
        return PRESET_FONT_SIZES.contains(fontSize)
    }
    
    /**
     * 添加字号列表变化监听器
     */
    fun addListener(listener: FontSizePresetListener) {
        listeners.add(listener)
        AppLog.d("【字号预设管理器】已添加监听器，当前监听器数量: ${listeners.size}")
    }
    
    /**
     * 移除字号列表变化监听器
     */
    fun removeListener(listener: FontSizePresetListener) {
        listeners.remove(listener)
        AppLog.d("【字号预设管理器】已移除监听器，当前监听器数量: ${listeners.size}")
    }
    
    /**
     * 通知字号被添加
     */
    private fun notifyFontSizeAdded(fontSize: Int) {
        listeners.forEach { listener ->
            try {
                listener.onFontSizeAdded(fontSize)
            } catch (e: Exception) {
                AppLog.e("【字号预设管理器】通知监听器字号添加失败", e)
            }
        }
    }
    
    /**
     * 通知字号被删除
     */
    private fun notifyFontSizeDeleted(fontSize: Int) {
        listeners.forEach { listener ->
            try {
                listener.onFontSizeDeleted(fontSize)
            } catch (e: Exception) {
                AppLog.e("【字号预设管理器】通知监听器字号删除失败", e)
            }
        }
    }
    
    /**
     * 通知字号列表被重置
     */
    private fun notifyFontSizeListReset() {
        listeners.forEach { listener ->
            try {
                listener.onFontSizeListReset()
            } catch (e: Exception) {
                AppLog.e("【字号预设管理器】通知监听器列表重置失败", e)
            }
        }
    }
    
    /**
     * 查找最接近的预设字号
     */
    fun findClosestPresetFontSize(targetFontSize: Int): Int {
        return PRESET_FONT_SIZES.minByOrNull { kotlin.math.abs(it - targetFontSize) } ?: 13
    }
}
