package com.example.castapp.network

import java.net.InetAddress
import java.net.NetworkInterface

/**
 * 网络工具类
 */
object NetworkUtils {

    /**
     * 获取本机IP地址
     */
    fun getLocalIpAddress(): String? {
        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                val addresses = networkInterface.inetAddresses
                while (addresses.hasMoreElements()) {
                    val address = addresses.nextElement()
                    if (!address.isLoopbackAddress && address.hostAddress?.indexOf(':') == -1) {
                        return address.hostAddress
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    /**
     * 验证IP地址格式
     */
    fun isValidIpAddress(ip: String): <PERSON><PERSON>an {
        return try {
            InetAddress.getByName(ip)
            true
        } catch (_: Exception) {
            false
        }
    }

    /**
     * 验证端口号
     */
    fun isValidPort(port: Int): <PERSON><PERSON><PERSON> {
        return port in 1024..65535
    }
}
