package com.example.castapp.ui.view

import android.content.Context
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.Spinner
import android.widget.TextView
import com.example.castapp.R
import com.example.castapp.ui.dialog.ColorPickerDialog
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.FontSizePresetManager
import com.example.castapp.utils.FontPresetManager
import com.example.castapp.utils.LetterSpacingPresetManager
import com.example.castapp.utils.LineSpacingPresetManager
import com.example.castapp.utils.ToastUtils

/**
 * 文本编辑控制面板
 * 提供文本内容编辑和格式化功能，支持拖动
 */
class TextEditPanel(
    private val context: Context,
    private val parentContainer: ViewGroup
) : FontSizePresetManager.FontSizePresetListener, LetterSpacingPresetManager.LetterSpacingPresetListener, LineSpacingPresetManager.LineSpacingPresetListener {

    // UI组件
    private lateinit var panelView: LinearLayout
    private lateinit var titleBar: LinearLayout
    private lateinit var dragHandle: ImageView
    private lateinit var tvPanelTitle: TextView
    private lateinit var btnClosePanel: ImageView
    private lateinit var btnBold: LinearLayout
    private lateinit var btnItalic: LinearLayout
    private lateinit var fontSizeContainer: LinearLayout
    private lateinit var spinnerFontSize: Spinner
    private lateinit var letterSpacingContainer: LinearLayout
    private lateinit var spinnerLetterSpacing: Spinner
    private lateinit var lineSpacingContainer: LinearLayout
    private lateinit var spinnerLineSpacing: Spinner
    private lateinit var fontFamilyContainer: LinearLayout
    private lateinit var spinnerFontFamily: Spinner
    private lateinit var textAlignmentContainer: LinearLayout
    private lateinit var spinnerTextAlignment: Spinner
    private lateinit var btnColor: LinearLayout
    private lateinit var btnStroke: LinearLayout
    private lateinit var btnWindowColor: LinearLayout
    private lateinit var btnClearFormat: LinearLayout
    private lateinit var btnDragWindow: LinearLayout


    // 拖拽相关
    private var isDragging = false
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    private var initialX = 0f
    private var initialY = 0f

    // 格式状态
    private var isBoldEnabled = false
    private var isItalicEnabled = false
    private var currentFontSize = 13 // 默认字号13sp
    private var currentTextColor: Int? = null // 当前文字颜色，null表示默认颜色
    private var currentStrokeEnabled = false // 当前描边开关状态
    private var currentStrokeWidth = 4.0f // 当前描边宽度
    private var currentStrokeColor = 0xFF000000.toInt() // 当前描边颜色
    private var currentWindowColorEnabled = false // 当前窗口颜色开关状态
    private var currentWindowColor = 0xFFFFFFFF.toInt() // 当前窗口颜色

    // 全局字号预设管理器
    private val fontSizePresetManager = FontSizePresetManager.getInstance(context)

    // 字号选项（从全局管理器获取）
    private val fontSizeOptions = mutableListOf<String>()
    private val fontSizeValues = mutableListOf<Int>()

    // 字体相关
    private val fontFamilyItems = mutableListOf<FontPresetManager.FontItem>()
    private val fontFamilyOptions = mutableListOf<String>()
    private var currentFontFamily: FontPresetManager.FontItem? = null // 当前字体

    // 字间距相关
    private val letterSpacingPresetManager = LetterSpacingPresetManager.getInstance(context)
    private val letterSpacingOptions = mutableListOf<String>()
    private val letterSpacingValues = mutableListOf<Float>()
    private var currentLetterSpacing = 0.0f

    // 行间距相关
    private val lineSpacingPresetManager = LineSpacingPresetManager
    private val lineSpacingOptions = mutableListOf<String>()
    private val lineSpacingValues = mutableListOf<Float>()
    private var currentLineSpacing = 0.0f

    // 对齐相关
    private val textAlignmentOptions = mutableListOf<String>()
    private val textAlignmentValues = mutableListOf<Int>()
    private val textAlignmentIcons = mutableListOf<Int>()
    private var currentTextAlignment = android.view.Gravity.CENTER_HORIZONTAL or android.view.Gravity.CENTER_VERTICAL // 默认居中对齐

    // Spinner适配器
    private lateinit var fontSizeAdapter: FontSizeAdapter
    private lateinit var fontFamilyAdapter: FontFamilyAdapter
    private lateinit var textAlignmentAdapter: TextAlignmentAdapter
    private lateinit var letterSpacingAdapter: LetterSpacingAdapter
    private lateinit var lineSpacingAdapter: LineSpacingAdapter

    // 回调接口
    private var onFormatChangeListener: ((Boolean, Boolean) -> Unit)? = null
    private var onFontSizeChangeListener: ((Int) -> Unit)? = null
    private var onFontFamilyChangeListener: ((FontPresetManager.FontItem) -> Unit)? = null
    private var onLetterSpacingChangeListener: ((Float) -> Unit)? = null
    private var onLineSpacingChangeListener: ((Float) -> Unit)? = null
    private var onTextAlignmentChangeListener: ((Int) -> Unit)? = null
    private var onColorChangeListener: ((Int) -> Unit)? = null
    private var onStrokeChangeListener: ((Boolean, Float, Int) -> Unit)? = null
    private var onWindowColorChangeListener: ((Boolean, Int) -> Unit)? = null
    private var onClearFormatListener: (() -> Unit)? = null
    private var onCloseListener: (() -> Unit)? = null

    // 文本窗口拖动回调接口
    private var onTextWindowDragListener: ((deltaX: Float, deltaY: Float) -> Unit)? = null
    private var onTextWindowDragEndListener: (() -> Unit)? = null

    init {
        // 初始化字体管理器
        FontPresetManager.initialize(context)

        // 初始化行间距预设管理器
        lineSpacingPresetManager.initialize(context)

        // 注册字号预设管理器监听器
        fontSizePresetManager.addListener(this)

        // 注册字间距预设管理器监听器
        letterSpacingPresetManager.addListener(this)

        // 注册行间距预设管理器监听器
        lineSpacingPresetManager.addListener(this)

        // 从全局管理器加载字号列表
        loadFontSizeListFromManager()

        // 从全局管理器加载字间距列表
        loadLetterSpacingListFromManager()

        // 从全局管理器加载行间距列表
        loadLineSpacingListFromManager()

        // 加载字体列表
        loadFontFamilyList()

        // 加载对齐选项列表
        loadTextAlignmentList()

        initializePanel()
        setupFontSizeSpinner()
        setupLetterSpacingSpinner()
        setupLineSpacingSpinner()
        setupFontFamilySpinner()
        setupTextAlignmentSpinner()
        setupDragFunctionality()
        setupClickListeners()
        AppLog.d("【文本编辑面板】初始化完成")
    }

    /**
     * 从全局管理器加载字号列表
     */
    private fun loadFontSizeListFromManager() {
        try {
            // 清空现有列表
            fontSizeValues.clear()
            fontSizeOptions.clear()

            // 从全局管理器获取字号列表
            val allFontSizes = fontSizePresetManager.getAllFontSizes()
            val allFontSizeOptions = fontSizePresetManager.getAllFontSizeOptions()

            // 添加到本地列表
            fontSizeValues.addAll(allFontSizes)
            fontSizeOptions.addAll(allFontSizeOptions)

            AppLog.d("【文本编辑面板】从全局管理器加载字号列表: $allFontSizes")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】从全局管理器加载字号列表失败", e)
        }
    }

    /**
     * 从全局管理器加载字间距列表
     */
    private fun loadLetterSpacingListFromManager() {
        try {
            // 清空现有列表
            letterSpacingValues.clear()
            letterSpacingOptions.clear()

            // 从全局管理器获取字间距列表
            val allLetterSpacings = letterSpacingPresetManager.getAllLetterSpacings()
            val allLetterSpacingOptions = letterSpacingPresetManager.getAllLetterSpacingOptions()

            // 添加到本地列表
            letterSpacingValues.addAll(allLetterSpacings)
            letterSpacingOptions.addAll(allLetterSpacingOptions)

            AppLog.d("【文本编辑面板】从全局管理器加载字间距列表: $allLetterSpacings")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】从全局管理器加载字间距列表失败", e)
        }
    }

    /**
     * 从全局管理器加载行间距列表
     */
    private fun loadLineSpacingListFromManager() {
        try {
            // 清空现有列表
            lineSpacingValues.clear()
            lineSpacingOptions.clear()

            // 从全局管理器获取行间距列表
            val allLineSpacings = lineSpacingPresetManager.getAllLineSpacings()
            val allLineSpacingOptions = lineSpacingPresetManager.getAllLineSpacingOptions()

            // 添加到本地列表
            lineSpacingValues.addAll(allLineSpacings)
            lineSpacingOptions.addAll(allLineSpacingOptions)

            AppLog.d("【文本编辑面板】从全局管理器加载行间距列表: $allLineSpacings")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】从全局管理器加载行间距列表失败", e)
        }
    }

    /**
     * 加载字体列表
     */
    private fun loadFontFamilyList() {
        try {
            // 清空现有列表
            fontFamilyItems.clear()
            fontFamilyOptions.clear()

            // 从字体管理器获取字体列表
            val allFonts = FontPresetManager.getAllFonts()
            fontFamilyItems.addAll(allFonts)
            fontFamilyOptions.addAll(allFonts.map { it.name })

            // 设置默认字体
            if (currentFontFamily == null && fontFamilyItems.isNotEmpty()) {
                currentFontFamily = fontFamilyItems.first() // 默认选择第一个字体（"Roboto"）
            }

            AppLog.d("【文本编辑面板】加载字体列表: ${fontFamilyOptions}")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】加载字体列表失败", e)
        }
    }

    /**
     * 加载对齐选项列表
     */
    private fun loadTextAlignmentList() {
        try {
            // 清空现有列表
            textAlignmentOptions.clear()
            textAlignmentValues.clear()
            textAlignmentIcons.clear()

            // 添加对齐选项（统一使用CENTER_VERTICAL保持一致性）
            textAlignmentOptions.add("居中")
            textAlignmentValues.add(android.view.Gravity.CENTER_HORIZONTAL or android.view.Gravity.CENTER_VERTICAL)
            textAlignmentIcons.add(R.drawable.ic_format_align_center)

            textAlignmentOptions.add("偏左")
            textAlignmentValues.add(android.view.Gravity.START or android.view.Gravity.CENTER_VERTICAL)
            textAlignmentIcons.add(R.drawable.ic_format_align_left)

            textAlignmentOptions.add("偏右")
            textAlignmentValues.add(android.view.Gravity.END or android.view.Gravity.CENTER_VERTICAL)
            textAlignmentIcons.add(R.drawable.ic_format_align_right)

            AppLog.d("【文本编辑面板】加载对齐选项列表: ${textAlignmentOptions}")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】加载对齐选项列表失败", e)
        }
    }

    /**
     * 初始化面板视图
     */
    private fun initializePanel() {
        // 加载布局
        val inflater = LayoutInflater.from(context)
        panelView = inflater.inflate(R.layout.layout_text_edit_panel, null) as LinearLayout

        // 获取UI组件引用
        titleBar = panelView.findViewById(R.id.title_bar)
        dragHandle = panelView.findViewById(R.id.drag_handle)
        tvPanelTitle = panelView.findViewById(R.id.tv_panel_title)
        btnClosePanel = panelView.findViewById(R.id.btn_close_panel)
        btnBold = panelView.findViewById(R.id.btn_bold)
        btnItalic = panelView.findViewById(R.id.btn_italic)
        fontSizeContainer = panelView.findViewById(R.id.font_size_container)
        spinnerFontSize = panelView.findViewById(R.id.spinner_font_size)
        letterSpacingContainer = panelView.findViewById(R.id.letter_spacing_container)
        spinnerLetterSpacing = panelView.findViewById(R.id.spinner_letter_spacing)
        lineSpacingContainer = panelView.findViewById(R.id.line_spacing_container)
        spinnerLineSpacing = panelView.findViewById(R.id.spinner_line_spacing)
        fontFamilyContainer = panelView.findViewById(R.id.font_family_container)
        spinnerFontFamily = panelView.findViewById(R.id.spinner_font_family)
        textAlignmentContainer = panelView.findViewById(R.id.text_alignment_container)
        spinnerTextAlignment = panelView.findViewById(R.id.spinner_text_alignment)
        btnColor = panelView.findViewById(R.id.btn_color)
        btnStroke = panelView.findViewById(R.id.btn_stroke)
        btnWindowColor = panelView.findViewById(R.id.btn_window_color)
        btnClearFormat = panelView.findViewById(R.id.btn_clear_format)
        btnDragWindow = panelView.findViewById(R.id.btn_drag_window)

        // 设置布局参数
        val layoutParams = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT
        )
        layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT)
        panelView.layoutParams = layoutParams

        AppLog.d("【文本编辑面板】视图初始化完成")
    }

    /**
     * 设置字号下拉框
     */
    private fun setupFontSizeSpinner() {
        // 暂时使用标准适配器，确保基本功能正常
        fontSizeAdapter = FontSizeAdapter(context, R.layout.spinner_font_size_item, fontSizeOptions)
        fontSizeAdapter.setDropDownViewResource(R.layout.spinner_font_size_dropdown_item)
        spinnerFontSize.adapter = fontSizeAdapter

        // 设置默认选中项（13sp）
        updateSpinnerSelection(currentFontSize)

        // 设置选择监听器
        spinnerFontSize.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                AppLog.d("【文本编辑面板】Spinner选择事件触发: position=$position")

                if (position >= 0 && position < fontSizeValues.size) {
                    val newFontSize = fontSizeValues[position]
                    AppLog.d("【文本编辑面板】选择的字号: ${newFontSize}sp, 当前字号: ${currentFontSize}sp")

                    if (newFontSize != currentFontSize) {
                        currentFontSize = newFontSize
                        onFontSizeChangeListener?.invoke(newFontSize)
                        AppLog.d("【文本编辑面板】字号已更改: ${newFontSize}sp")
                    } else {
                        AppLog.d("【文本编辑面板】字号未变化，跳过更新")
                    }
                } else {
                    AppLog.w("【文本编辑面板】无效的position: $position, 数据大小: ${fontSizeValues.size}")
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
                AppLog.d("【文本编辑面板】Spinner无选择")
            }
        }

        AppLog.d("【文本编辑面板】字号下拉框设置完成")
    }

    /**
     * 设置字间距下拉框
     */
    private fun setupLetterSpacingSpinner() {
        // 创建字间距适配器
        letterSpacingAdapter = LetterSpacingAdapter(context, R.layout.spinner_letter_spacing_item, letterSpacingOptions)
        letterSpacingAdapter.setDropDownViewResource(R.layout.spinner_letter_spacing_dropdown_item)
        spinnerLetterSpacing.adapter = letterSpacingAdapter

        // 设置默认选中项（0.0em）
        updateLetterSpacingSpinnerSelection(currentLetterSpacing)

        // 设置选择监听器
        spinnerLetterSpacing.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                AppLog.d("【文本编辑面板】字间距Spinner选择事件触发: position=$position")

                if (position >= 0 && position < letterSpacingValues.size) {
                    val newLetterSpacing = letterSpacingValues[position]
                    AppLog.d("【文本编辑面板】选择的字间距: ${newLetterSpacing}em, 当前字间距: ${currentLetterSpacing}em")

                    if (newLetterSpacing != currentLetterSpacing) {
                        currentLetterSpacing = newLetterSpacing
                        onLetterSpacingChangeListener?.invoke(newLetterSpacing)
                        AppLog.d("【文本编辑面板】字间距已更改: ${newLetterSpacing}em")
                    } else {
                        AppLog.d("【文本编辑面板】字间距未变化，跳过更新")
                    }
                } else {
                    AppLog.w("【文本编辑面板】无效的position: $position, 数据大小: ${letterSpacingValues.size}")
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
                AppLog.d("【文本编辑面板】字间距Spinner无选择")
            }
        }

        AppLog.d("【文本编辑面板】字间距下拉框设置完成")
    }

    /**
     * 设置行间距下拉框
     */
    private fun setupLineSpacingSpinner() {
        // 创建行间距适配器
        lineSpacingAdapter = LineSpacingAdapter(context, R.layout.spinner_line_spacing_item, lineSpacingOptions)
        lineSpacingAdapter.setDropDownViewResource(R.layout.spinner_line_spacing_dropdown_item)
        spinnerLineSpacing.adapter = lineSpacingAdapter

        // 设置默认选中项（0.0dp）
        updateLineSpacingSpinnerSelection(currentLineSpacing)

        // 设置选择监听器
        spinnerLineSpacing.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                AppLog.d("【文本编辑面板】行间距Spinner选择事件触发: position=$position")

                if (position >= 0 && position < lineSpacingValues.size) {
                    val newLineSpacing = lineSpacingValues[position]
                    AppLog.d("【文本编辑面板】选择的行间距: ${newLineSpacing}dp, 当前行间距: ${currentLineSpacing}dp")

                    if (newLineSpacing != currentLineSpacing) {
                        currentLineSpacing = newLineSpacing
                        onLineSpacingChangeListener?.invoke(newLineSpacing)
                        AppLog.d("【文本编辑面板】行间距已更改: ${newLineSpacing}dp")
                    } else {
                        AppLog.d("【文本编辑面板】行间距未变化，跳过更新")
                    }
                } else {
                    AppLog.w("【文本编辑面板】无效的position: $position, 数据大小: ${lineSpacingValues.size}")
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
                AppLog.d("【文本编辑面板】行间距Spinner无选择")
            }
        }

        AppLog.d("【文本编辑面板】行间距下拉框设置完成")
    }

    /**
     * 设置字体下拉框
     */
    private fun setupFontFamilySpinner() {
        // 创建字体适配器
        fontFamilyAdapter = FontFamilyAdapter(context, R.layout.spinner_font_size_item, fontFamilyOptions)
        fontFamilyAdapter.setDropDownViewResource(R.layout.spinner_font_size_dropdown_item)
        spinnerFontFamily.adapter = fontFamilyAdapter

        // 设置默认选中项
        updateFontFamilySelection(currentFontFamily)

        // 设置选择监听器
        spinnerFontFamily.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                AppLog.d("【文本编辑面板】字体Spinner选择事件触发: position=$position")

                if (position >= 0 && position < fontFamilyItems.size) {
                    val newFontFamily = fontFamilyItems[position]
                    AppLog.d("【文本编辑面板】选择的字体: ${newFontFamily.name}, 当前字体: ${currentFontFamily?.name}")

                    if (newFontFamily != currentFontFamily) {
                        currentFontFamily = newFontFamily
                        onFontFamilyChangeListener?.invoke(newFontFamily)
                        AppLog.d("【文本编辑面板】字体已更改: ${newFontFamily.name}")
                    } else {
                        AppLog.d("【文本编辑面板】字体未变化，跳过更新")
                    }
                } else {
                    AppLog.w("【文本编辑面板】无效的position: $position, 数据大小: ${fontFamilyItems.size}")
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
                AppLog.d("【文本编辑面板】字体Spinner未选择任何项")
            }
        }

        AppLog.d("【文本编辑面板】字体下拉框设置完成")
    }

    /**
     * 设置对齐下拉框
     */
    private fun setupTextAlignmentSpinner() {
        // 创建对齐适配器
        textAlignmentAdapter = TextAlignmentAdapter(context, R.layout.spinner_text_alignment_item, textAlignmentOptions, textAlignmentIcons)
        textAlignmentAdapter.setDropDownViewResource(R.layout.spinner_text_alignment_dropdown_item)
        spinnerTextAlignment.adapter = textAlignmentAdapter

        // 设置默认选中项（居中对齐）
        updateTextAlignmentSelection(currentTextAlignment)

        // 设置选择监听器
        spinnerTextAlignment.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                AppLog.d("【文本编辑面板】对齐Spinner选择事件触发: position=$position")

                if (position >= 0 && position < textAlignmentValues.size) {
                    val newAlignment = textAlignmentValues[position]
                    AppLog.d("【文本编辑面板】选择的对齐方式: ${textAlignmentOptions[position]}, 值: $newAlignment")

                    if (newAlignment != currentTextAlignment) {
                        currentTextAlignment = newAlignment
                        onTextAlignmentChangeListener?.invoke(newAlignment)
                        AppLog.d("【文本编辑面板】对齐方式已更改: ${textAlignmentOptions[position]}")
                    } else {
                        AppLog.d("【文本编辑面板】对齐方式未变化，跳过更新")
                    }
                } else {
                    AppLog.w("【文本编辑面板】无效的position: $position, 数据大小: ${textAlignmentValues.size}")
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
                AppLog.d("【文本编辑面板】对齐Spinner未选择任何项")
            }
        }

        AppLog.d("【文本编辑面板】对齐下拉框设置完成")
    }

    /**
     * 设置拖拽功能
     */
    private fun setupDragFunctionality() {
        // 创建通用的编辑面板拖动处理逻辑
        val panelDragTouchListener = View.OnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    isDragging = true
                    lastTouchX = event.rawX
                    lastTouchY = event.rawY
                    initialX = panelView.x
                    initialY = panelView.y
                    AppLog.v("【文本编辑面板】开始拖拽编辑面板")
                    true
                }

                MotionEvent.ACTION_MOVE -> {
                    if (isDragging) {
                        val deltaX = event.rawX - lastTouchX
                        val deltaY = event.rawY - lastTouchY

                        val newX = initialX + deltaX
                        val newY = initialY + deltaY

                        // 边界检查
                        val maxX = parentContainer.width - panelView.width
                        val maxY = parentContainer.height - panelView.height

                        panelView.x = newX.coerceIn(0f, maxX.toFloat())
                        panelView.y = newY.coerceIn(0f, maxY.toFloat())

                        AppLog.v("【文本编辑面板】拖拽编辑面板中: (${panelView.x}, ${panelView.y})")
                    }
                    true
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    if (isDragging) {
                        isDragging = false
                        AppLog.d("【文本编辑面板】拖拽编辑面板结束: (${panelView.x}, ${panelView.y})")
                    }
                    true
                }

                else -> false
            }
        }

        // 标题栏整体 - 拖动编辑面板
        titleBar.setOnTouchListener(panelDragTouchListener)

        // 标题栏左侧的拖动手柄 - 拖动编辑面板
        dragHandle.setOnTouchListener(panelDragTouchListener)

        // 新的拖动手柄按钮 - 拖动文本窗口位置
        btnDragWindow.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    lastTouchX = event.rawX
                    lastTouchY = event.rawY
                    AppLog.d("【文本编辑面板】拖动手柄开始拖动文本窗口")
                    true
                }

                MotionEvent.ACTION_MOVE -> {
                    val deltaX = event.rawX - lastTouchX
                    val deltaY = event.rawY - lastTouchY

                    // 通知文本窗口拖动监听器
                    onTextWindowDragListener?.invoke(deltaX, deltaY)

                    lastTouchX = event.rawX
                    lastTouchY = event.rawY

                    AppLog.v("【文本编辑面板】拖动手柄移动文本窗口: deltaX=$deltaX, deltaY=$deltaY")
                    true
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    onTextWindowDragEndListener?.invoke()
                    AppLog.d("【文本编辑面板】拖动手柄结束拖动文本窗口")
                    true
                }

                else -> false
            }
        }
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // 关闭按钮
        btnClosePanel.setOnClickListener {
            AppLog.d("【文本编辑面板】用户点击关闭按钮")
            hide()
            onCloseListener?.invoke()
        }

        // 加粗按钮 - 实时应用
        btnBold.setOnClickListener {
            isBoldEnabled = !isBoldEnabled
            updateButtonState(btnBold, isBoldEnabled)
            // 立即应用格式变化
            onFormatChangeListener?.invoke(isBoldEnabled, isItalicEnabled)
            AppLog.d("【文本编辑面板】加粗状态切换并实时应用: $isBoldEnabled")
        }

        // 倾斜按钮 - 实时应用
        btnItalic.setOnClickListener {
            isItalicEnabled = !isItalicEnabled
            updateButtonState(btnItalic, isItalicEnabled)
            // 立即应用格式变化
            onFormatChangeListener?.invoke(isBoldEnabled, isItalicEnabled)
            AppLog.d("【文本编辑面板】倾斜状态切换并实时应用: $isItalicEnabled")
        }

        // 字号容器点击 - 触发Spinner下拉框
        fontSizeContainer.setOnClickListener {
            spinnerFontSize.performClick()
            AppLog.d("【文本编辑面板】字号容器被点击，触发下拉框显示")
        }

        // 字号容器长按 - 显示字号设置对话框
        fontSizeContainer.setOnLongClickListener {
            showFontSizeSettingsDialog()
            AppLog.d("【文本编辑面板】字号容器被长按，显示字号设置对话框")
            true // 消费长按事件
        }

        // 字号Spinner长按 - 显示字号设置对话框
        spinnerFontSize.setOnLongClickListener {
            showFontSizeSettingsDialog()
            AppLog.d("【文本编辑面板】字号Spinner被长按，显示字号设置对话框")
            true // 消费长按事件
        }

        // 字体容器点击 - 触发Spinner下拉框
        fontFamilyContainer.setOnClickListener {
            spinnerFontFamily.performClick()
            AppLog.d("【文本编辑面板】字体容器被点击，触发下拉框显示")
        }

        // 字体容器长按 - 显示字体设置对话框
        fontFamilyContainer.setOnLongClickListener {
            showFontSettingsDialog()
            AppLog.d("【文本编辑面板】字体容器被长按，显示字体设置对话框")
            true // 消费长按事件
        }

        // 字体Spinner长按 - 显示字体设置对话框
        spinnerFontFamily.setOnLongClickListener {
            showFontSettingsDialog()
            AppLog.d("【文本编辑面板】字体Spinner被长按，显示字体设置对话框")
            true // 消费长按事件
        }

        // 字间距容器长按 - 显示字间距设置对话框
        letterSpacingContainer.setOnLongClickListener {
            showLetterSpacingSettingsDialog()
            AppLog.d("【文本编辑面板】字间距容器被长按，显示字间距设置对话框")
            true // 消费长按事件
        }

        // 字间距Spinner长按 - 显示字间距设置对话框
        spinnerLetterSpacing.setOnLongClickListener {
            showLetterSpacingSettingsDialog()
            AppLog.d("【文本编辑面板】字间距Spinner被长按，显示字间距设置对话框")
            true // 消费长按事件
        }

        // 行间距容器长按 - 显示行间距设置对话框
        lineSpacingContainer.setOnLongClickListener {
            showLineSpacingSettingsDialog()
            AppLog.d("【文本编辑面板】行间距容器被长按，显示行间距设置对话框")
            true // 消费长按事件
        }

        // 行间距Spinner长按 - 显示行间距设置对话框
        spinnerLineSpacing.setOnLongClickListener {
            showLineSpacingSettingsDialog()
            AppLog.d("【文本编辑面板】行间距Spinner被长按，显示行间距设置对话框")
            true // 消费长按事件
        }

        // 对齐容器点击 - 触发Spinner下拉框
        textAlignmentContainer.setOnClickListener {
            spinnerTextAlignment.performClick()
            AppLog.d("【文本编辑面板】对齐容器被点击，触发下拉框显示")
        }

        // 颜色按钮 - 显示颜色选择器
        btnColor.setOnClickListener {
            showColorPickerDialog()
            AppLog.d("【文本编辑面板】颜色按钮被点击，显示颜色选择器")
        }

        // 描边按钮 - 显示描边颜色选择器
        btnStroke.setOnClickListener {
            showStrokeColorPickerDialog()
            AppLog.d("【文本编辑面板】描边按钮被点击，显示描边颜色选择器")
        }

        // 窗色按钮 - 显示窗口颜色选择器
        btnWindowColor.setOnClickListener {
            showWindowColorPickerDialog()
            AppLog.d("【文本编辑面板】窗色按钮被点击，显示窗口颜色选择器")
        }

        // 清除格式按钮 - 实时应用
        btnClearFormat.setOnClickListener {
            isBoldEnabled = false
            isItalicEnabled = false
            currentFontFamily = fontFamilyItems.firstOrNull() // 重置为默认字体
            currentLetterSpacing = 0.0f // 重置为默认字间距
            // 🎯 修复：不重置行间距，保持用户设置的行间距
            // currentLineSpacing = 0.0f // 重置为默认行间距
            currentTextAlignment = android.view.Gravity.CENTER_HORIZONTAL or android.view.Gravity.CENTER_VERTICAL // 重置为默认对齐方式（居中）
            updateButtonState(btnBold, false)
            updateButtonState(btnItalic, false)
            updateFontFamilySelection(currentFontFamily)
            updateLetterSpacingSpinnerSelection(currentLetterSpacing)
            // 🎯 修复：不更新行间距下拉框，保持当前行间距设置
            // updateLineSpacingSpinnerSelection(currentLineSpacing)
            updateTextAlignmentSelection(currentTextAlignment)
            // 调用专门的清除格式回调
            onClearFormatListener?.invoke()
            AppLog.d("【文本编辑面板】格式已清除并实时应用")
        }

    }

    /**
     * 更新按钮状态
     */
    private fun updateButtonState(buttonLayout: LinearLayout, isEnabled: Boolean) {
        if (isEnabled) {
            buttonLayout.setBackgroundResource(R.drawable.button_apply_background)
            // 更新图标和文字颜色
            val imageView = buttonLayout.getChildAt(0) as? ImageView
            val textView = buttonLayout.getChildAt(1) as? TextView
            imageView?.setColorFilter(0xFFFFFFFF.toInt())
            textView?.setTextColor(0xFFFFFFFF.toInt())
        } else {
            buttonLayout.setBackgroundResource(R.drawable.button_background)
            // 更新图标和文字颜色
            val imageView = buttonLayout.getChildAt(0) as? ImageView
            val textView = buttonLayout.getChildAt(1) as? TextView
            imageView?.setColorFilter(0xFF333333.toInt())
            textView?.setTextColor(0xFF333333.toInt())
        }
    }

    /**
     * 显示编辑面板
     */
    fun show(@Suppress("UNUSED_PARAMETER") currentText: String = "默认文字", bold: Boolean = false, italic: Boolean = false, fontSize: Int = 13, fontFamily: FontPresetManager.FontItem? = null, letterSpacing: Float = 0.0f, lineSpacing: Float = 0.0f, textAlignment: Int = android.view.Gravity.CENTER_HORIZONTAL or android.view.Gravity.CENTER_VERTICAL) {
        // 设置格式状态
        isBoldEnabled = bold
        isItalicEnabled = italic
        currentFontSize = fontSize
        currentFontFamily = fontFamily ?: fontFamilyItems.firstOrNull()
        currentLetterSpacing = letterSpacing
        currentLineSpacing = lineSpacing
        currentTextAlignment = textAlignment

        updateButtonState(btnBold, isBoldEnabled)
        updateButtonState(btnItalic, isItalicEnabled)

        // 设置字号下拉框选择（动态添加自定义字号）
        updateSpinnerSelection(fontSize)

        // 设置字体下拉框选择
        updateFontFamilySelection(currentFontFamily)

        // 设置字间距下拉框选择
        updateLetterSpacingSpinnerSelection(letterSpacing)

        // 设置行间距下拉框选择
        updateLineSpacingSpinnerSelection(lineSpacing)

        // 设置对齐下拉框选择
        updateTextAlignmentSelection(textAlignment)

        // 添加到父容器
        if (panelView.parent == null) {
            parentContainer.addView(panelView)
        }

        panelView.visibility = View.VISIBLE

        AppLog.d("【文本编辑面板】面板已显示，字号: ${fontSize}sp, 字体: ${currentFontFamily?.name}, 行间距: ${lineSpacing}dp")
    }

    /**
     * 隐藏编辑面板
     */
    fun hide() {
        panelView.visibility = View.GONE
        if (panelView.parent != null) {
            parentContainer.removeView(panelView)
        }
        AppLog.d("【文本编辑面板】面板已隐藏")
    }

    /**
     * 设置格式变化监听器（实时应用）
     */
    fun setOnFormatChangeListener(listener: (Boolean, Boolean) -> Unit) {
        this.onFormatChangeListener = listener
    }

    /**
     * 设置字号变化监听器
     */
    fun setOnFontSizeChangeListener(listener: (Int) -> Unit) {
        this.onFontSizeChangeListener = listener
    }

    /**
     * 设置字体变化监听器
     */
    fun setOnFontFamilyChangeListener(listener: (FontPresetManager.FontItem) -> Unit) {
        this.onFontFamilyChangeListener = listener
    }

    /**
     * 设置字间距变化监听器
     */
    fun setOnLetterSpacingChangeListener(listener: (Float) -> Unit) {
        this.onLetterSpacingChangeListener = listener
    }

    /**
     * 设置行间距变化监听器
     */
    fun setOnLineSpacingChangeListener(listener: (Float) -> Unit) {
        this.onLineSpacingChangeListener = listener
    }

    /**
     * 设置对齐变化监听器
     */
    fun setOnTextAlignmentChangeListener(listener: (Int) -> Unit) {
        this.onTextAlignmentChangeListener = listener
    }

    /**
     * 设置清除格式监听器
     */
    fun setOnClearFormatListener(listener: () -> Unit) {
        this.onClearFormatListener = listener
    }

    /**
     * 设置关闭监听器
     */
    fun setOnCloseListener(listener: () -> Unit) {
        this.onCloseListener = listener
    }

    /**
     * 根据选中文字的格式状态更新按钮状态（包含字号、颜色、描边、字体、字间距和行间距）
     */
    fun updateButtonStatesFromSelection(bold: Boolean, italic: Boolean, fontSize: Int, textColor: Int? = null, strokeState: Triple<Boolean, Float, Int>? = null, fontFamily: FontPresetManager.FontItem? = null, letterSpacing: Float? = null, lineSpacing: Float? = null, textAlignment: Int? = null) {
        isBoldEnabled = bold
        isItalicEnabled = italic
        currentFontSize = fontSize
        currentTextColor = textColor
        // 🎯 修复：直接使用传入的字体，如果为null则保持当前字体不变
        if (fontFamily != null) {
            currentFontFamily = fontFamily
        }

        // 更新字间距状态
        if (letterSpacing != null) {
            currentLetterSpacing = letterSpacing
        }

        // 更新行间距状态
        if (lineSpacing != null) {
            currentLineSpacing = lineSpacing
        }

        // 更新对齐状态
        if (textAlignment != null) {
            currentTextAlignment = textAlignment
        }

        // 更新描边状态
        if (strokeState != null) {
            currentStrokeEnabled = strokeState.first
            currentStrokeWidth = strokeState.second
            currentStrokeColor = strokeState.third
        }

        updateButtonState(btnBold, isBoldEnabled)
        updateButtonState(btnItalic, isItalicEnabled)

        // 更新字号下拉框选择（动态添加自定义字号）
        updateSpinnerSelection(fontSize)

        // 更新字体下拉框选择
        updateFontFamilySelection(currentFontFamily)

        // 更新字间距下拉框选择
        if (letterSpacing != null) {
            updateLetterSpacingSpinnerSelection(letterSpacing)
        }

        // 更新行间距下拉框选择
        if (lineSpacing != null) {
            updateLineSpacingSpinnerSelection(lineSpacing)
        }

        // 更新对齐下拉框选择
        if (textAlignment != null) {
            updateTextAlignmentSelection(textAlignment)
        }

        // 更新颜色按钮状态
        if (textColor != null) {
            updateColorButtonState(textColor)
        } else {
            resetColorButtonState()
        }

        // 更新描边按钮状态
        if (strokeState != null) {
            updateStrokeButtonState(strokeState.first, strokeState.third)
        } else {
            resetStrokeButtonState()
        }

        AppLog.d("【文本编辑面板】按钮状态已根据选中文字更新: 加粗=$bold, 倾斜=$italic, 字号=${fontSize}sp, 字体=${currentFontFamily?.name}, 颜色=${textColor?.let { String.format("#%08X", it) } ?: "默认"}, 描边=${strokeState?.let { "(${it.first}, ${it.second}px, ${String.format("#%08X", it.third)})" } ?: "无"}")
    }

    /**
     * 设置文本窗口拖动监听器
     */
    fun setOnTextWindowDragListener(listener: (deltaX: Float, deltaY: Float) -> Unit) {
        this.onTextWindowDragListener = listener
    }

    /**
     * 设置文本窗口拖动结束监听器
     */
    fun setOnTextWindowDragEndListener(listener: () -> Unit) {
        this.onTextWindowDragEndListener = listener
    }

    /**
     * 检查面板是否显示
     */
    fun isShowing(): Boolean {
        return panelView.visibility == View.VISIBLE && panelView.parent != null
    }

    /**
     * 显示字号设置对话框
     */
    private fun showFontSizeSettingsDialog() {
        try {
            val dialog = com.example.castapp.ui.dialog.FontSizeSettingsDialog(
                context = context,
                currentFontSize = currentFontSize,
                existingFontSizes = fontSizeValues.toList(), // 传入现有字号列表
                onFontSizeSelected = { selectedFontSize ->
                    // 字号选择回调
                    handleFontSizeSelection(selectedFontSize)
                },
                onFontSizeAdded = { addedFontSize ->
                    // 字号添加回调 - 通过全局管理器添加
                    fontSizePresetManager.addCustomFontSize(addedFontSize)
                    AppLog.d("【文本编辑面板】字号已通过全局管理器添加: ${addedFontSize}sp")
                },
                onFontSizeDeleted = { deletedFontSize ->
                    // 字号删除回调 - 通过全局管理器删除
                    fontSizePresetManager.deleteCustomFontSize(deletedFontSize)
                    AppLog.d("【文本编辑面板】字号已通过全局管理器删除: ${deletedFontSize}sp")
                },
                onResetToDefault = {
                    // 恢复默认回调 - 通过全局管理器重置
                    fontSizePresetManager.resetToDefault()
                    AppLog.d("【文本编辑面板】已通过全局管理器恢复默认字号设置")
                }
            )

            dialog.show()

            AppLog.d("【文本编辑面板】字号设置对话框已显示，当前字号: ${currentFontSize}sp")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】显示字号设置对话框失败", e)
            ToastUtils.showToast(context, "显示字号设置对话框失败")
        }
    }

    /**
     * 显示字间距设置对话框
     */
    private fun showLetterSpacingSettingsDialog() {
        try {
            val dialog = com.example.castapp.ui.dialog.LetterSpacingSettingsDialog(
                context = context,
                currentLetterSpacing = currentLetterSpacing,
                existingLetterSpacings = letterSpacingValues.toList(), // 传入现有字间距列表
                onLetterSpacingSelected = { selectedLetterSpacing ->
                    // 字间距选择回调
                    handleLetterSpacingSelection(selectedLetterSpacing)
                },
                onLetterSpacingAdded = { addedLetterSpacing ->
                    // 字间距添加回调 - 通过全局管理器添加
                    letterSpacingPresetManager.addCustomLetterSpacing(addedLetterSpacing)
                    AppLog.d("【文本编辑面板】字间距已通过全局管理器添加: ${addedLetterSpacing}em")
                },
                onLetterSpacingDeleted = { deletedLetterSpacing ->
                    // 字间距删除回调 - 通过全局管理器删除
                    letterSpacingPresetManager.deleteCustomLetterSpacing(deletedLetterSpacing)
                    AppLog.d("【文本编辑面板】字间距已通过全局管理器删除: ${deletedLetterSpacing}em")
                },
                onResetToDefault = {
                    // 恢复默认回调 - 通过全局管理器重置
                    letterSpacingPresetManager.resetToDefault()
                    AppLog.d("【文本编辑面板】已通过全局管理器恢复默认字间距设置")
                }
            )

            dialog.show()

            AppLog.d("【文本编辑面板】字间距设置对话框已显示，当前字间距: ${currentLetterSpacing}em")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】显示字间距设置对话框失败", e)
            ToastUtils.showToast(context, "显示字间距设置对话框失败")
        }
    }

    /**
     * 处理字间距选择
     */
    private fun handleLetterSpacingSelection(letterSpacing: Float) {
        try {
            // 如果字间距没有变化，直接返回
            if (letterSpacing == currentLetterSpacing) {
                AppLog.d("【文本编辑面板】字间距未发生变化: ${letterSpacing}em")
                return
            }

            // 应用新字间距
            currentLetterSpacing = letterSpacing
            onLetterSpacingChangeListener?.invoke(letterSpacing)

            // 更新Spinner选择（动态添加自定义字间距）
            updateLetterSpacingSpinnerSelection(letterSpacing)

            ToastUtils.showToast(context, "字间距已设置为${letterSpacing}em")
            AppLog.d("【文本编辑面板】字间距已通过设置对话框更改: ${letterSpacing}em")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】处理字间距选择失败", e)
            ToastUtils.showToast(context, "设置字间距失败")
        }
    }

    /**
     * 显示行间距设置对话框
     */
    private fun showLineSpacingSettingsDialog() {
        try {
            val dialog = com.example.castapp.ui.dialog.LineSpacingSettingsDialog(
                context = context,
                currentLineSpacing = currentLineSpacing,
                existingLineSpacings = lineSpacingValues.toList(), // 传入现有行间距列表
                onLineSpacingSelected = { selectedLineSpacing ->
                    // 行间距选择回调
                    handleLineSpacingSelection(selectedLineSpacing)
                },
                onLineSpacingAdded = { addedLineSpacing ->
                    // 行间距添加回调 - 通过全局管理器添加
                    lineSpacingPresetManager.addCustomLineSpacing(addedLineSpacing)
                    AppLog.d("【文本编辑面板】行间距已通过全局管理器添加: ${addedLineSpacing}dp")
                },
                onLineSpacingDeleted = { deletedLineSpacing ->
                    // 行间距删除回调 - 通过全局管理器删除
                    lineSpacingPresetManager.deleteCustomLineSpacing(deletedLineSpacing)
                    AppLog.d("【文本编辑面板】行间距已通过全局管理器删除: ${deletedLineSpacing}dp")
                },
                onResetToDefault = {
                    // 恢复默认回调 - 通过全局管理器重置
                    lineSpacingPresetManager.resetToDefault()
                    AppLog.d("【文本编辑面板】已通过全局管理器恢复默认行间距设置")
                }
            )

            dialog.show()

            AppLog.d("【文本编辑面板】行间距设置对话框已显示，当前行间距: ${currentLineSpacing}dp")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】显示行间距设置对话框失败", e)
            ToastUtils.showToast(context, "显示行间距设置对话框失败")
        }
    }

    /**
     * 处理行间距选择
     */
    private fun handleLineSpacingSelection(lineSpacing: Float) {
        try {
            // 如果行间距没有变化，直接返回
            if (lineSpacing == currentLineSpacing) {
                AppLog.d("【文本编辑面板】行间距未发生变化: ${lineSpacing}dp")
                return
            }

            // 应用新行间距
            currentLineSpacing = lineSpacing
            onLineSpacingChangeListener?.invoke(lineSpacing)

            // 更新Spinner选择（动态添加自定义行间距）
            updateLineSpacingSpinnerSelection(lineSpacing)

            ToastUtils.showToast(context, "行间距已设置为${lineSpacing}dp")
            AppLog.d("【文本编辑面板】行间距已通过设置对话框更改: ${lineSpacing}dp")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】处理行间距选择失败", e)
            ToastUtils.showToast(context, "设置行间距失败")
        }
    }

    /**
     * 处理字号选择
     */
    private fun handleFontSizeSelection(fontSize: Int) {
        try {
            // 如果字号没有变化，直接返回
            if (fontSize == currentFontSize) {
                AppLog.d("【文本编辑面板】字号未发生变化: ${fontSize}sp")
                return
            }

            // 应用新字号
            currentFontSize = fontSize
            onFontSizeChangeListener?.invoke(fontSize)

            // 更新Spinner选择（动态添加自定义字号）
            updateSpinnerSelection(fontSize)

            ToastUtils.showToast(context, "字号已设置为${fontSize}sp")
            AppLog.d("【文本编辑面板】字号已通过设置对话框更改: ${fontSize}sp")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】处理字号选择失败", e)
            ToastUtils.showToast(context, "设置字号失败")
        }
    }



    /**
     * 更新Spinner选择到指定字号
     */
    private fun updateSpinnerSelection(fontSize: Int) {
        try {
            // 如果字号不在选项中，先通过全局管理器添加
            if (!fontSizeValues.contains(fontSize)) {
                fontSizePresetManager.addCustomFontSize(fontSize)
                // 重新加载列表以确保同步
                loadFontSizeListFromManager()
                fontSizeAdapter.notifyDataSetChanged()
            }

            // 设置选择
            val fontSizeIndex = fontSizeValues.indexOf(fontSize)
            if (fontSizeIndex >= 0) {
                spinnerFontSize.setSelection(fontSizeIndex)
                AppLog.d("【文本编辑面板】Spinner已选择字号: ${fontSize}sp")
            }

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】更新Spinner选择失败", e)
        }
    }

    /**
     * 更新字间距Spinner选择到指定字间距
     */
    private fun updateLetterSpacingSpinnerSelection(letterSpacing: Float) {
        try {
            // 如果字间距不在选项中，先通过全局管理器添加
            if (!letterSpacingValues.contains(letterSpacing)) {
                letterSpacingPresetManager.addCustomLetterSpacing(letterSpacing)
                // 重新加载列表以确保同步
                loadLetterSpacingListFromManager()
                letterSpacingAdapter.notifyDataSetChanged()
            }

            // 设置选择
            val letterSpacingIndex = letterSpacingValues.indexOf(letterSpacing)
            if (letterSpacingIndex >= 0) {
                spinnerLetterSpacing.setSelection(letterSpacingIndex)
                AppLog.d("【文本编辑面板】字间距Spinner已选择: ${letterSpacing}em")
            }

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】更新字间距Spinner选择失败", e)
        }
    }

    /**
     * 更新行间距Spinner选择到指定行间距
     */
    private fun updateLineSpacingSpinnerSelection(lineSpacing: Float) {
        try {
            // 如果行间距不在选项中，先通过全局管理器添加
            if (!lineSpacingValues.contains(lineSpacing)) {
                lineSpacingPresetManager.addCustomLineSpacing(lineSpacing)
                // 重新加载列表以确保同步
                loadLineSpacingListFromManager()
                lineSpacingAdapter.notifyDataSetChanged()
            }

            // 设置选择
            val lineSpacingIndex = lineSpacingValues.indexOf(lineSpacing)
            if (lineSpacingIndex >= 0) {
                spinnerLineSpacing.setSelection(lineSpacingIndex)
                AppLog.d("【文本编辑面板】行间距Spinner已选择: ${lineSpacing}dp")
            }

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】更新行间距Spinner选择失败", e)
        }
    }

    /**
     * 更新字体选择到指定字体
     */
    private fun updateFontFamilySelection(fontItem: FontPresetManager.FontItem?) {
        try {
            if (fontItem == null) return

            val fontIndex = fontFamilyItems.indexOf(fontItem)
            if (fontIndex >= 0) {
                spinnerFontFamily.setSelection(fontIndex)
                AppLog.d("【文本编辑面板】字体Spinner已选择: ${fontItem.name}")
            } else {
                AppLog.w("【文本编辑面板】字体不在列表中: ${fontItem.name}")
            }

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】更新字体选择失败", e)
        }
    }

    /**
     * 更新对齐选择到指定对齐方式
     */
    private fun updateTextAlignmentSelection(alignment: Int) {
        try {
            val alignmentIndex = textAlignmentValues.indexOf(alignment)
            if (alignmentIndex >= 0) {
                spinnerTextAlignment.setSelection(alignmentIndex)
                AppLog.d("【文本编辑面板】对齐Spinner已选择: ${textAlignmentOptions[alignmentIndex]}")
            } else {
                // 如果找不到精确匹配，默认选择居中对齐
                val centerAlignment = android.view.Gravity.CENTER_HORIZONTAL or android.view.Gravity.CENTER_VERTICAL
                val centerIndex = textAlignmentValues.indexOf(centerAlignment)
                if (centerIndex >= 0) {
                    spinnerTextAlignment.setSelection(centerIndex)
                    currentTextAlignment = centerAlignment
                    AppLog.d("【文本编辑面板】对齐方式不在列表中，默认选择居中对齐")
                }
            }

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】更新对齐选择失败", e)
        }
    }

    /**
     * 显示字体设置对话框
     */
    private fun showFontSettingsDialog() {
        try {
            val dialog = com.example.castapp.ui.dialog.FontSettingsDialog(
                context = context,
                currentFontName = currentFontFamily?.name ?: "Roboto",
                onFontSelected = { selectedFont ->
                    // 字体选择回调
                    currentFontFamily = selectedFont
                    updateFontFamilySelection(selectedFont)
                    onFontFamilyChangeListener?.invoke(selectedFont)
                    AppLog.d("【文本编辑面板】字体已选择: ${selectedFont.name}")
                },
                onFontAdded = { addedFont ->
                    // 字体添加回调
                    loadFontFamilyList()
                    fontFamilyAdapter.notifyDataSetChanged()
                    AppLog.d("【文本编辑面板】字体已添加: ${addedFont.name}")
                },
                onFontDeleted = { deletedFont ->
                    // 字体删除回调
                    loadFontFamilyList()
                    fontFamilyAdapter.notifyDataSetChanged()

                    // 如果删除的是当前字体，切换到默认字体
                    if (currentFontFamily == deletedFont) {
                        currentFontFamily = fontFamilyItems.firstOrNull()
                        currentFontFamily?.let { defaultFont ->
                            updateFontFamilySelection(defaultFont)
                            onFontFamilyChangeListener?.invoke(defaultFont)
                        }
                    }

                    AppLog.d("【文本编辑面板】字体已删除: ${deletedFont.name}")
                },
                onFontNameUpdated = { oldFont, newFont ->
                    // 字体名称更新回调
                    loadFontFamilyList()
                    fontFamilyAdapter.notifyDataSetChanged()

                    // 如果更新的是当前字体，切换到新字体
                    if (currentFontFamily == oldFont) {
                        currentFontFamily = newFont
                        updateFontFamilySelection(newFont)
                        onFontFamilyChangeListener?.invoke(newFont)
                    }

                    AppLog.d("【文本编辑面板】字体名称已更新: ${oldFont.name} -> ${newFont.name}")
                },
                onResetToDefault = {
                    // 重置回调
                    loadFontFamilyList()
                    fontFamilyAdapter.notifyDataSetChanged()
                    currentFontFamily = fontFamilyItems.firstOrNull()
                    currentFontFamily?.let { defaultFont ->
                        updateFontFamilySelection(defaultFont)
                        onFontFamilyChangeListener?.invoke(defaultFont)
                    }
                    AppLog.d("【文本编辑面板】字体已重置为默认")
                }
            )

            dialog.show()
            AppLog.d("【文本编辑面板】字体设置对话框已显示")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】显示字体设置对话框失败", e)
            ToastUtils.showToast(context, "显示字体设置对话框失败")
        }
    }

    /**
     * 自定义字号适配器（暂时禁用长按功能，确保基本选择正常）
     */
    private inner class FontSizeAdapter(
        context: Context,
        resource: Int,
        objects: MutableList<String>
    ) : ArrayAdapter<String>(context, resource, objects) {

        override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
            val view = super.getDropDownView(position, convertView, parent)

            // 获取对应的字号值
            val fontSize = fontSizeValues[position]
            val isCustomFontSize = !fontSizePresetManager.isPresetFontSize(fontSize)

            AppLog.v("【文本编辑面板】创建下拉视图: position=$position, fontSize=${fontSize}sp, isCustom=$isCustomFontSize")

            // 暂时不添加长按功能，确保基本点击选择正常工作
            // TODO: 稍后重新实现长按删除功能

            // 视觉提示
            if (isCustomFontSize) {
                view.alpha = 0.9f // 稍微透明一些表示这是自定义字号
            } else {
                view.alpha = 1.0f
            }

            return view
        }
    }

    /**
     * 自定义字间距适配器
     */
    private inner class LetterSpacingAdapter(
        context: Context,
        resource: Int,
        objects: MutableList<String>
    ) : ArrayAdapter<String>(context, resource, objects) {

        override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
            val view = super.getDropDownView(position, convertView, parent)

            // 获取对应的字间距值
            val letterSpacing = letterSpacingValues[position]
            val isCustomLetterSpacing = !letterSpacingPresetManager.isPresetLetterSpacing(letterSpacing)

            AppLog.v("【文本编辑面板】创建字间距下拉视图: position=$position, letterSpacing=${letterSpacing}em, isCustom=$isCustomLetterSpacing")

            // 视觉提示
            if (isCustomLetterSpacing) {
                view.alpha = 0.9f // 稍微透明一些表示这是自定义字间距
            } else {
                view.alpha = 1.0f
            }

            return view
        }
    }

    /**
     * 自定义行间距适配器
     */
    private inner class LineSpacingAdapter(
        context: Context,
        resource: Int,
        objects: MutableList<String>
    ) : ArrayAdapter<String>(context, resource, objects) {

        override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
            val view = super.getDropDownView(position, convertView, parent)

            // 获取对应的行间距值
            val lineSpacing = lineSpacingValues[position]
            val isCustomLineSpacing = !lineSpacingPresetManager.isPresetLineSpacing(lineSpacing)

            AppLog.v("【文本编辑面板】创建行间距下拉视图: position=$position, lineSpacing=${lineSpacing}dp, isCustom=$isCustomLineSpacing")

            // 视觉提示
            if (isCustomLineSpacing) {
                view.alpha = 0.9f // 稍微透明一些表示这是自定义行间距
            } else {
                view.alpha = 1.0f
            }

            return view
        }
    }

    /**
     * 自定义字体适配器
     */
    private inner class FontFamilyAdapter(
        context: Context,
        resource: Int,
        objects: MutableList<String>
    ) : ArrayAdapter<String>(context, resource, objects) {

        override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
            val view = super.getDropDownView(position, convertView, parent)

            // 获取对应的字体项
            if (position < fontFamilyItems.size) {
                val fontItem = fontFamilyItems[position]

                // 应用字体样式
                if (view is TextView) {
                    fontItem.loadTypeface()?.let { typeface ->
                        view.typeface = typeface
                    }

                    // 视觉提示
                    if (fontItem.isPreset) {
                        view.alpha = 1.0f
                    } else {
                        view.alpha = 0.9f // 稍微透明一些表示这是自定义字体
                    }
                }

                AppLog.v("【文本编辑面板】创建字体下拉视图: position=$position, font=${fontItem.name}, isPreset=${fontItem.isPreset}")
            }

            return view
        }

        override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
            val view = super.getView(position, convertView, parent)

            // 为主视图也应用字体样式
            if (position < fontFamilyItems.size) {
                val fontItem = fontFamilyItems[position]
                if (view is TextView) {
                    fontItem.loadTypeface()?.let { typeface ->
                        view.typeface = typeface
                    }
                }
            }

            return view
        }
    }







    /**
     * 查找最接近的预设字号
     */
    private fun findClosestPresetFontSize(targetFontSize: Int): Int {
        return fontSizePresetManager.findClosestPresetFontSize(targetFontSize)
    }

    // ========== FontSizePresetListener 接口实现 ==========

    /**
     * 字号被添加时的回调
     */
    override fun onFontSizeAdded(fontSize: Int) {
        try {
            // 重新加载字号列表
            loadFontSizeListFromManager()

            // 通知适配器数据变化
            if (::fontSizeAdapter.isInitialized) {
                fontSizeAdapter.notifyDataSetChanged()
            }

            AppLog.d("【文本编辑面板】监听到字号添加: ${fontSize}sp，已更新列表")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】处理字号添加事件失败", e)
        }
    }

    /**
     * 字号被删除时的回调
     */
    override fun onFontSizeDeleted(fontSize: Int) {
        try {
            // 重新加载字号列表
            loadFontSizeListFromManager()

            // 通知适配器数据变化
            if (::fontSizeAdapter.isInitialized) {
                fontSizeAdapter.notifyDataSetChanged()
            }

            // 如果删除的是当前选中的字号，切换到最接近的预设字号
            if (currentFontSize == fontSize) {
                val newFontSize = findClosestPresetFontSize(fontSize)
                currentFontSize = newFontSize
                updateSpinnerSelection(newFontSize)
                onFontSizeChangeListener?.invoke(newFontSize)
                AppLog.d("【文本编辑面板】当前字号已切换到最接近的预设字号: ${newFontSize}sp")
            }

            AppLog.d("【文本编辑面板】监听到字号删除: ${fontSize}sp，已更新列表")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】处理字号删除事件失败", e)
        }
    }

    /**
     * 字号列表被重置时的回调
     */
    override fun onFontSizeListReset() {
        try {
            // 重新加载字号列表
            loadFontSizeListFromManager()

            // 通知适配器数据变化
            if (::fontSizeAdapter.isInitialized) {
                fontSizeAdapter.notifyDataSetChanged()
            }

            AppLog.d("【文本编辑面板】监听到字号列表重置，已更新列表")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】处理字号列表重置事件失败", e)
        }
    }

    // ========== LetterSpacingPresetListener 接口实现 ==========

    /**
     * 字间距被添加时的回调
     */
    override fun onLetterSpacingAdded(letterSpacing: Float) {
        try {
            // 重新加载字间距列表
            loadLetterSpacingListFromManager()

            // 通知适配器数据变化
            if (::letterSpacingAdapter.isInitialized) {
                letterSpacingAdapter.notifyDataSetChanged()
            }

            AppLog.d("【文本编辑面板】监听到字间距添加: ${letterSpacing}em，已更新列表")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】处理字间距添加事件失败", e)
        }
    }

    /**
     * 字间距被删除时的回调
     */
    override fun onLetterSpacingDeleted(letterSpacing: Float) {
        try {
            // 重新加载字间距列表
            loadLetterSpacingListFromManager()

            // 通知适配器数据变化
            if (::letterSpacingAdapter.isInitialized) {
                letterSpacingAdapter.notifyDataSetChanged()
            }

            // 如果删除的是当前选中的字间距，切换到默认字间距
            if (currentLetterSpacing == letterSpacing) {
                val newLetterSpacing = 0.0f // 默认字间距
                currentLetterSpacing = newLetterSpacing
                updateLetterSpacingSpinnerSelection(newLetterSpacing)
                onLetterSpacingChangeListener?.invoke(newLetterSpacing)
                AppLog.d("【文本编辑面板】当前字间距已切换到默认字间距: ${newLetterSpacing}em")
            }

            AppLog.d("【文本编辑面板】监听到字间距删除: ${letterSpacing}em，已更新列表")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】处理字间距删除事件失败", e)
        }
    }

    /**
     * 字间距列表被重置时的回调
     */
    override fun onLetterSpacingListReset() {
        try {
            // 重新加载字间距列表
            loadLetterSpacingListFromManager()

            // 通知适配器数据变化
            if (::letterSpacingAdapter.isInitialized) {
                letterSpacingAdapter.notifyDataSetChanged()
            }

            AppLog.d("【文本编辑面板】监听到字间距列表重置，已更新列表")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】处理字间距列表重置事件失败", e)
        }
    }

    // ========== 行间距预设管理器监听器实现 ==========

    /**
     * 行间距被添加时的回调
     */
    override fun onLineSpacingAdded(lineSpacing: Float) {
        try {
            // 重新加载行间距列表
            loadLineSpacingListFromManager()

            // 通知适配器数据变化
            if (::lineSpacingAdapter.isInitialized) {
                lineSpacingAdapter.notifyDataSetChanged()
            }

            AppLog.d("【文本编辑面板】监听到行间距添加: ${lineSpacing}dp，已更新列表")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】处理行间距添加事件失败", e)
        }
    }

    /**
     * 行间距被删除时的回调
     */
    override fun onLineSpacingDeleted(lineSpacing: Float) {
        try {
            // 重新加载行间距列表
            loadLineSpacingListFromManager()

            // 通知适配器数据变化
            if (::lineSpacingAdapter.isInitialized) {
                lineSpacingAdapter.notifyDataSetChanged()
            }

            // 如果删除的是当前选中的行间距，切换到默认行间距
            if (currentLineSpacing == lineSpacing) {
                val newLineSpacing = 0.0f // 默认行间距
                currentLineSpacing = newLineSpacing
                updateLineSpacingSpinnerSelection(newLineSpacing)
                onLineSpacingChangeListener?.invoke(newLineSpacing)
                AppLog.d("【文本编辑面板】当前行间距已切换到默认行间距: ${newLineSpacing}dp")
            }

            AppLog.d("【文本编辑面板】监听到行间距删除: ${lineSpacing}dp，已更新列表")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】处理行间距删除事件失败", e)
        }
    }

    /**
     * 行间距列表被重置时的回调
     */
    override fun onLineSpacingListReset() {
        try {
            // 重新加载行间距列表
            loadLineSpacingListFromManager()

            // 通知适配器数据变化
            if (::lineSpacingAdapter.isInitialized) {
                lineSpacingAdapter.notifyDataSetChanged()
            }

            AppLog.d("【文本编辑面板】监听到行间距列表重置，已更新列表")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】处理行间距列表重置事件失败", e)
        }
    }

    /**
     * 显示颜色选择器对话框
     */
    private fun showColorPickerDialog() {
        try {
            // 使用当前文字颜色，如果没有则使用默认颜色
            val displayColor = currentTextColor ?: 0xFF333333.toInt()

            val colorPickerDialog = ColorPickerDialog(
                context = context,
                currentColor = displayColor,
                title = "🎨 文字颜色",
                isStrokeMode = false,
                currentStrokeWidth = 2.0f,
                currentStrokeEnabled = false,
                onColorSelected = { selectedColor ->
                    // 颜色选择完成回调
                    currentTextColor = selectedColor
                    updateColorButtonState(selectedColor)
                    onColorChangeListener?.invoke(selectedColor)
                    AppLog.d("【文本编辑面板】用户选择了文字颜色: ${String.format("#%08X", selectedColor)}")
                },
                onStrokeConfigChanged = null
            )

            colorPickerDialog.show()
            AppLog.d("【文本编辑面板】颜色选择器对话框已显示")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】显示颜色选择器失败", e)
            ToastUtils.showToast(context, "显示颜色选择器失败")
        }
    }

    /**
     * 更新颜色按钮状态，显示当前文字颜色
     */
    private fun updateColorButtonState(color: Int) {
        try {
            val imageView = btnColor.getChildAt(0) as? ImageView
            imageView?.setColorFilter(color)
            AppLog.d("【文本编辑面板】颜色按钮图标已更新为: ${String.format("#%08X", color)}")
        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】更新颜色按钮状态失败", e)
        }
    }

    /**
     * 重置颜色按钮状态为默认颜色
     */
    private fun resetColorButtonState() {
        try {
            val imageView = btnColor.getChildAt(0) as? ImageView
            imageView?.setColorFilter(0xFF333333.toInt()) // 默认黑色
            AppLog.d("【文本编辑面板】颜色按钮图标已重置为默认颜色")
        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】重置颜色按钮状态失败", e)
        }
    }

    /**
     * 显示描边颜色选择器对话框
     */
    private fun showStrokeColorPickerDialog() {
        try {
            val colorPickerDialog = ColorPickerDialog(
                context = context,
                currentColor = currentStrokeColor,
                title = "🎨 文字描边",
                isStrokeMode = true,
                currentStrokeWidth = currentStrokeWidth,
                currentStrokeEnabled = currentStrokeEnabled,
                onColorSelected = { /* 不使用，描边模式下使用onStrokeConfigChanged */ },
                onStrokeConfigChanged = { enabled, width, color ->
                    // 描边配置变化回调
                    currentStrokeEnabled = enabled
                    currentStrokeWidth = width
                    currentStrokeColor = color
                    updateStrokeButtonState(enabled, color)
                    onStrokeChangeListener?.invoke(enabled, width, color)
                    AppLog.d("【文本编辑面板】用户设置了描边: 启用=$enabled, 宽度=${width}px, 颜色=${String.format("#%08X", color)}")
                }
            )

            colorPickerDialog.show()
            AppLog.d("【文本编辑面板】描边颜色选择器对话框已显示")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】显示描边颜色选择器失败", e)
            ToastUtils.showToast(context, "显示描边颜色选择器失败")
        }
    }

    /**
     * 更新描边按钮状态
     */
    private fun updateStrokeButtonState(enabled: Boolean, color: Int) {
        try {
            if (enabled) {
                // 启用描边时，按钮背景变为激活状态，图标显示描边颜色
                btnStroke.setBackgroundResource(R.drawable.button_apply_background)
                val imageView = btnStroke.getChildAt(0) as? ImageView
                val textView = btnStroke.getChildAt(1) as? TextView
                imageView?.setColorFilter(color)
                textView?.setTextColor(0xFFFFFFFF.toInt())
            } else {
                // 禁用描边时，恢复默认状态
                resetStrokeButtonState()
            }
            AppLog.d("【文本编辑面板】描边按钮状态已更新: 启用=$enabled, 颜色=${String.format("#%08X", color)}")
        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】更新描边按钮状态失败", e)
        }
    }

    /**
     * 重置描边按钮状态为默认状态
     */
    private fun resetStrokeButtonState() {
        try {
            btnStroke.setBackgroundResource(R.drawable.button_background)
            val imageView = btnStroke.getChildAt(0) as? ImageView
            val textView = btnStroke.getChildAt(1) as? TextView
            imageView?.setColorFilter(0xFF333333.toInt()) // 默认灰色
            textView?.setTextColor(0xFF333333.toInt())
            AppLog.d("【文本编辑面板】描边按钮状态已重置为默认状态")
        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】重置描边按钮状态失败", e)
        }
    }

    /**
     * 设置颜色变化监听器
     */
    fun setOnColorChangeListener(listener: (Int) -> Unit) {
        this.onColorChangeListener = listener
    }

    /**
     * 设置描边变化监听器
     */
    fun setOnStrokeChangeListener(listener: (Boolean, Float, Int) -> Unit) {
        this.onStrokeChangeListener = listener
    }

    /**
     * 显示窗口颜色选择器对话框
     */
    private fun showWindowColorPickerDialog() {
        try {
            val colorPickerDialog = ColorPickerDialog(
                context = context,
                currentColor = currentWindowColor,
                title = "🎨 窗口颜色",
                isStrokeMode = false,
                currentStrokeWidth = 2.0f,
                currentStrokeEnabled = false,
                isWindowColorMode = true,
                currentWindowColorEnabled = currentWindowColorEnabled,
                onColorSelected = { /* 不使用，窗口颜色模式下使用onWindowColorConfigChanged */ },
                onStrokeConfigChanged = null,
                onWindowColorConfigChanged = { enabled, color ->
                    // 窗口颜色配置变化回调
                    currentWindowColorEnabled = enabled
                    currentWindowColor = color
                    updateWindowColorButtonState(enabled, color)
                    onWindowColorChangeListener?.invoke(enabled, color)
                    AppLog.d("【文本编辑面板】用户设置了窗口颜色: 启用=$enabled, 颜色=${String.format("#%08X", color)}")
                }
            )

            colorPickerDialog.show()
            AppLog.d("【文本编辑面板】窗口颜色选择器对话框已显示")

        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】显示窗口颜色选择器失败", e)
            ToastUtils.showToast(context, "显示窗口颜色选择器失败")
        }
    }

    /**
     * 更新窗色按钮状态
     */
    private fun updateWindowColorButtonState(enabled: Boolean, color: Int) {
        try {
            if (enabled) {
                // 启用窗口颜色时，按钮背景变为激活状态，图标显示窗口颜色
                btnWindowColor.setBackgroundResource(R.drawable.button_apply_background)
                val imageView = btnWindowColor.getChildAt(0) as? ImageView
                val textView = btnWindowColor.getChildAt(1) as? TextView
                imageView?.setColorFilter(color)
                textView?.setTextColor(0xFFFFFFFF.toInt())
            } else {
                // 禁用窗口颜色时，恢复默认状态
                resetWindowColorButtonState()
            }
            AppLog.d("【文本编辑面板】窗色按钮状态已更新: 启用=$enabled, 颜色=${String.format("#%08X", color)}")
        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】更新窗色按钮状态失败", e)
        }
    }

    /**
     * 重置窗色按钮状态为默认状态
     */
    private fun resetWindowColorButtonState() {
        try {
            btnWindowColor.setBackgroundResource(R.drawable.button_background)
            val imageView = btnWindowColor.getChildAt(0) as? ImageView
            val textView = btnWindowColor.getChildAt(1) as? TextView
            imageView?.setColorFilter(0xFF333333.toInt()) // 默认灰色
            textView?.setTextColor(0xFF333333.toInt())
            AppLog.d("【文本编辑面板】窗色按钮状态已重置为默认状态")
        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】重置窗色按钮状态失败", e)
        }
    }

    /**
     * 设置窗口颜色变化监听器
     */
    fun setOnWindowColorChangeListener(listener: (Boolean, Int) -> Unit) {
        this.onWindowColorChangeListener = listener
    }

    /**
     * 初始化窗口颜色状态（用于从TextWindowView恢复状态）
     */
    fun initializeWindowColorState(enabled: Boolean, color: Int) {
        try {
            currentWindowColorEnabled = enabled
            currentWindowColor = color
            updateWindowColorButtonState(enabled, color)
            AppLog.d("【文本编辑面板】窗口颜色状态已初始化: 启用=$enabled, 颜色=${String.format("#%08X", color)}")
        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】初始化窗口颜色状态失败", e)
        }
    }

    /**
     * 销毁时移除监听器
     */
    fun destroy() {
        try {
            fontSizePresetManager.removeListener(this)
            letterSpacingPresetManager.removeListener(this)
            lineSpacingPresetManager.removeListener(this)
            AppLog.d("【文本编辑面板】已移除字号、字间距和行间距预设管理器监听器")
        } catch (e: Exception) {
            AppLog.e("【文本编辑面板】移除监听器失败", e)
        }
    }

    /**
     * 自定义对齐适配器
     */
    private inner class TextAlignmentAdapter(
        context: Context,
        resource: Int,
        private val alignmentOptions: MutableList<String>,
        private val alignmentIcons: MutableList<Int>
    ) : ArrayAdapter<String>(context, resource, alignmentOptions) {

        override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
            val view = convertView ?: LayoutInflater.from(context).inflate(R.layout.spinner_text_alignment_item, parent, false)

            val iconView = view.findViewById<ImageView>(R.id.iv_alignment_icon)
            if (position < alignmentIcons.size) {
                iconView.setImageResource(alignmentIcons[position])
            }

            return view
        }

        override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
            val view = convertView ?: LayoutInflater.from(context).inflate(R.layout.spinner_text_alignment_dropdown_item, parent, false)

            val iconView = view.findViewById<ImageView>(R.id.iv_alignment_icon)
            val textView = view.findViewById<TextView>(R.id.tv_alignment_name)

            if (position < alignmentIcons.size) {
                iconView.setImageResource(alignmentIcons[position])
            }

            if (position < alignmentOptions.size) {
                textView.text = alignmentOptions[position]
            }

            return view
        }
    }
}
