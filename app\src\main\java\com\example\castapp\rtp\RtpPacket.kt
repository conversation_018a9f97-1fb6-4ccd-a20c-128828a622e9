package com.example.castapp.rtp

/**
 * RTP数据包实现 - 零拷贝优化版本
 */
class RtpPacket {
    companion object {
        const val RTP_HEADER_SIZE = 12
        const val RTP_VERSION = 2
        const val H264_PAYLOAD_TYPE = 96

        // H.264 FU-A 相关常量
        const val FU_A_TYPE = 28 // FU-A NAL单元类型
        const val FU_INDICATOR_SIZE = 1 // FU指示符大小
        const val FU_HEADER_SIZE = 1 // FU头部大小
        const val FU_HEADER_TOTAL_SIZE = FU_INDICATOR_SIZE + FU_HEADER_SIZE // FU-A总头部大小

        // FU头部位标志
        const val FU_START_BIT = 0x80 // S位：分片开始
        const val FU_END_BIT = 0x40   // E位：分片结束
    }

    // RTP头部字段
    var version: Int = RTP_VERSION
    private var padding: Boolean = false
    private var csrcCount: Int = 0
    var marker: Boolean = false
    var payloadType: Int = H264_PAYLOAD_TYPE
    var sequenceNumber: Int = 0
    var timestamp: Long = 0
    var ssrc: Long = 0

    // 负载数据视图（零拷贝优化）
    var payloadView: PayloadView = PayloadView.EMPTY

    // FU-A 相关字段
    var isFuA: Boolean = false // 是否为FU-A包
    var fuStart: Boolean = false // FU-A开始标志
    var fuEnd: Boolean = false // FU-A结束标志
    var originalNalType: Int = 0 // 原始NAL单元类型

    /**
     * 从SmartDataView解析RTP包（智能零拷贝）
     */
    fun fromDataView(smartDataView: com.example.castapp.network.SmartDataView): Boolean {
        if (smartDataView.size < RTP_HEADER_SIZE) {
            return false
        }

        try {
            // 解析RTP头部（直接从SmartDataView读取，智能零拷贝）
            val firstByte = smartDataView.getByte(0).toInt() and 0xFF
            version = (firstByte shr 6) and 0x03
            padding = (firstByte and 0x20) != 0
            csrcCount = firstByte and 0x0F

            val secondByte = smartDataView.getByte(1).toInt() and 0xFF
            marker = (secondByte and 0x80) != 0
            payloadType = secondByte and 0x7F

            sequenceNumber = ((smartDataView.getByte(2).toInt() and 0xFF) shl 8) or
                           (smartDataView.getByte(3).toInt() and 0xFF)
            timestamp = ((smartDataView.getByte(4).toInt() and 0xFF).toLong() shl 24) or
                       ((smartDataView.getByte(5).toInt() and 0xFF).toLong() shl 16) or
                       ((smartDataView.getByte(6).toInt() and 0xFF).toLong() shl 8) or
                       (smartDataView.getByte(7).toInt() and 0xFF).toLong()
            ssrc = ((smartDataView.getByte(8).toInt() and 0xFF).toLong() shl 24) or
                  ((smartDataView.getByte(9).toInt() and 0xFF).toLong() shl 16) or
                  ((smartDataView.getByte(10).toInt() and 0xFF).toLong() shl 8) or
                  (smartDataView.getByte(11).toInt() and 0xFF).toLong()

            // 🚀 智能零拷贝核心：直接从SmartDataView创建PayloadView
            val payloadSize = smartDataView.size - RTP_HEADER_SIZE
            if (payloadSize > 0) {
                payloadView = PayloadView()
                // 🎯 完全零拷贝：直接使用SmartDataView作为数据源
                payloadView.setView(smartDataView, RTP_HEADER_SIZE, payloadSize)

                // 检查是否为H.264负载并解析FU-A信息
                if (payloadType == H264_PAYLOAD_TYPE && payloadView.isNotEmpty()) {
                    parseFuAInfo()
                }
            } else {
                payloadView = PayloadView.EMPTY
            }

            return true

        } catch (_: Exception) {
            return false
        }
    }

    /**
     * 解析FU-A信息（使用PayloadView零拷贝）
     */
    private fun parseFuAInfo() {
        if (payloadView.size() < FU_HEADER_TOTAL_SIZE) {
            isFuA = false
            return
        }

        // 检查FU指示符
        val fuIndicator = payloadView.getByte(0).toInt() and 0xFF
        val nalType = fuIndicator and 0x1F

        if (nalType == FU_A_TYPE) {
            // 这是FU-A包
            isFuA = true

            // 解析FU头部
            val fuHeader = payloadView.getByte(1).toInt() and 0xFF
            fuStart = (fuHeader and FU_START_BIT) != 0
            fuEnd = (fuHeader and FU_END_BIT) != 0
            originalNalType = fuHeader and 0x1F
        } else {
            // 这是单个NAL单元
            isFuA = false
            originalNalType = nalType
        }
    }

    /**
     * 获取FU-A分片的NAL数据视图（不包含FU-A头部，零拷贝）
     */
    fun getFuANalDataView(): PayloadView? {
        if (!isFuA || payloadView.size() < FU_HEADER_TOTAL_SIZE) {
            return null
        }

        // 返回去掉FU-A头部的数据视图（零拷贝）
        val nalDataSize = payloadView.size() - FU_HEADER_TOTAL_SIZE
        return payloadView.subView(FU_HEADER_TOTAL_SIZE, nalDataSize)
    }
}
