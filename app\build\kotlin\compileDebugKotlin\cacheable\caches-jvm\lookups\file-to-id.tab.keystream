>app/src/main/java/com/example/castapp/audio/AudioBufferPool.ktBapp/src/main/java/com/example/castapp/audio/AudioCaptureManager.kt;app/src/main/java/com/example/castapp/audio/AudioDecoder.kt;app/src/main/java/com/example/castapp/audio/AudioEncoder.kt:app/src/main/java/com/example/castapp/audio/AudioPlayer.kt?app/src/main/java/com/example/castapp/audio/AudioRtpReceiver.kt=app/src/main/java/com/example/castapp/audio/AudioRtpSender.kt?app/src/main/java/com/example/castapp/audio/AudioSyncManager.kt;app/src/main/java/com/example/castapp/codec/VideoDecoder.kt;app/src/main/java/com/example/castapp/codec/VideoEncoder.ktAapp/src/main/java/com/example/castapp/database/CastAppDatabase.ktIapp/src/main/java/com/example/castapp/database/converter/DateConverter.ktEapp/src/main/java/com/example/castapp/database/dao/WindowLayoutDao.ktKapp/src/main/java/com/example/castapp/database/entity/WindowLayoutEntity.ktOapp/src/main/java/com/example/castapp/database/entity/WindowLayoutItemEntity.ktFapp/src/main/java/com/example/castapp/manager/AudioReceivingManager.ktFapp/src/main/java/com/example/castapp/manager/FloatingWindowManager.kt@app/src/main/java/com/example/castapp/manager/HideShowManager.kt>app/src/main/java/com/example/castapp/manager/LayoutManager.ktGapp/src/main/java/com/example/castapp/manager/MediaProjectionManager.ktHapp/src/main/java/com/example/castapp/manager/MessageReceivingManager.ktBapp/src/main/java/com/example/castapp/manager/MicrophoneManager.ktCapp/src/main/java/com/example/castapp/manager/MultiCameraManager.ktBapp/src/main/java/com/example/castapp/manager/PermissionManager.ktMapp/src/main/java/com/example/castapp/manager/PrecisionControlPanelManager.ktHapp/src/main/java/com/example/castapp/manager/RemoteConnectionManager.ktFapp/src/main/java/com/example/castapp/manager/RemoteReceiverManager.ktDapp/src/main/java/com/example/castapp/manager/RemoteSenderManager.ktBapp/src/main/java/com/example/castapp/manager/ResolutionManager.kt=app/src/main/java/com/example/castapp/manager/StateManager.ktFapp/src/main/java/com/example/castapp/manager/VideoReceivingManager.ktAapp/src/main/java/com/example/castapp/manager/WebSocketManager.ktFapp/src/main/java/com/example/castapp/manager/WindowSettingsManager.ktTapp/src/main/java/com/example/castapp/manager/windowsettings/WindowCreationModule.ktPapp/src/main/java/com/example/castapp/manager/windowsettings/WindowDataModule.ktRapp/src/main/java/com/example/castapp/manager/windowsettings/WindowDialogModule.ktPapp/src/main/java/com/example/castapp/manager/windowsettings/WindowInfoModule.ktRapp/src/main/java/com/example/castapp/manager/windowsettings/WindowLayoutModule.ktUapp/src/main/java/com/example/castapp/manager/windowsettings/WindowLifecycleModule.ktUapp/src/main/java/com/example/castapp/manager/windowsettings/WindowOperationModule.kt=app/src/main/java/com/example/castapp/model/CastWindowInfo.kt9app/src/main/java/com/example/castapp/model/Connection.ktGapp/src/main/java/com/example/castapp/model/RemoteReceiverConnection.ktEapp/src/main/java/com/example/castapp/model/RemoteSenderConnection.kt?app/src/main/java/com/example/castapp/model/WindowUpdateMode.ktFapp/src/main/java/com/example/castapp/model/WindowVisualizationData.kt=app/src/main/java/com/example/castapp/network/NetworkUtils.ktCapp/src/main/java/com/example/castapp/network/SmartBufferManager.kt<app/src/main/java/com/example/castapp/network/UdpReceiver.kt:app/src/main/java/com/example/castapp/network/UdpSender.ktKapp/src/main/java/com/example/castapp/remote/RemoteReceiverControlServer.ktBapp/src/main/java/com/example/castapp/remote/RemoteSenderServer.ktKapp/src/main/java/com/example/castapp/remote/RemoteSenderWebSocketClient.ktCapp/src/main/java/com/example/castapp/rtp/MultiConnectionManager.kt8app/src/main/java/com/example/castapp/rtp/PayloadView.kt6app/src/main/java/com/example/castapp/rtp/RtpPacket.kt8app/src/main/java/com/example/castapp/rtp/RtpReceiver.kt6app/src/main/java/com/example/castapp/rtp/RtpSender.kt=app/src/main/java/com/example/castapp/service/AudioService.kt?app/src/main/java/com/example/castapp/service/CastingService.ktIapp/src/main/java/com/example/castapp/service/FloatingStopwatchService.ktAapp/src/main/java/com/example/castapp/service/ReceivingService.ktFapp/src/main/java/com/example/castapp/service/RemoteReceiverService.kt8app/src/main/java/com/example/castapp/ui/MainActivity.ktBapp/src/main/java/com/example/castapp/ui/ReceiverDialogFragment.kt@app/src/main/java/com/example/castapp/ui/SenderDialogFragment.kt;app/src/main/java/com/example/castapp/ui/StopwatchWindow.ktEapp/src/main/java/com/example/castapp/ui/adapter/ConnectionAdapter.ktMapp/src/main/java/com/example/castapp/ui/adapter/CustomColorPaletteAdapter.ktGapp/src/main/java/com/example/castapp/ui/adapter/LayerManagerAdapter.ktGapp/src/main/java/com/example/castapp/ui/adapter/LayoutDetailAdapter.ktEapp/src/main/java/com/example/castapp/ui/adapter/LayoutListAdapter.ktOapp/src/main/java/com/example/castapp/ui/adapter/RemoteReceiverDeviceAdapter.ktGapp/src/main/java/com/example/castapp/ui/adapter/RemoteSenderAdapter.ktMapp/src/main/java/com/example/castapp/ui/adapter/RemoteSenderDeviceAdapter.ktIapp/src/main/java/com/example/castapp/ui/adapter/RemoteTabPagerAdapter.ktHapp/src/main/java/com/example/castapp/ui/adapter/WindowManagerAdapter.ktIapp/src/main/java/com/example/castapp/ui/dialog/AddMediaDialogFragment.ktPapp/src/main/java/com/example/castapp/ui/dialog/AddRemoteReceiverDeviceDialog.ktNapp/src/main/java/com/example/castapp/ui/dialog/AddRemoteSenderDeviceDialog.ktDapp/src/main/java/com/example/castapp/ui/dialog/ColorPickerDialog.ktAapp/src/main/java/com/example/castapp/ui/dialog/DirectorDialog.ktCapp/src/main/java/com/example/castapp/ui/dialog/EditLayoutDialog.ktQapp/src/main/java/com/example/castapp/ui/dialog/EditRemoteReceiverDeviceDialog.ktOapp/src/main/java/com/example/castapp/ui/dialog/EditRemoteSenderDeviceDialog.ktGapp/src/main/java/com/example/castapp/ui/dialog/FontFilePickerDialog.ktEapp/src/main/java/com/example/castapp/ui/dialog/FontSettingsDialog.ktIapp/src/main/java/com/example/castapp/ui/dialog/FontSizeSettingsDialog.ktEapp/src/main/java/com/example/castapp/ui/dialog/LayerManagerDialog.ktNapp/src/main/java/com/example/castapp/ui/dialog/LetterSpacingSettingsDialog.ktLapp/src/main/java/com/example/castapp/ui/dialog/LineSpacingSettingsDialog.ktAapp/src/main/java/com/example/castapp/ui/dialog/NoteEditDialog.ktMapp/src/main/java/com/example/castapp/ui/dialog/RemoteControlManagerDialog.ktNapp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktVapp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverSettingsControlDialog.ktLapp/src/main/java/com/example/castapp/ui/dialog/RemoteSenderControlDialog.ktLapp/src/main/java/com/example/castapp/ui/dialog/RemoteWindowManagerDialog.ktCapp/src/main/java/com/example/castapp/ui/dialog/SaveLayoutDialog.ktDapp/src/main/java/com/example/castapp/ui/dialog/SaveOptionsDialog.ktFapp/src/main/java/com/example/castapp/ui/dialog/WindowManagerDialog.ktNapp/src/main/java/com/example/castapp/ui/fragment/RemoteReceiverTabFragment.ktLapp/src/main/java/com/example/castapp/ui/fragment/RemoteSenderTabFragment.ktPapp/src/main/java/com/example/castapp/ui/helper/LayoutItemTouchHelperCallback.kt@app/src/main/java/com/example/castapp/ui/view/CropOverlayView.ktIapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.ktCapp/src/main/java/com/example/castapp/ui/view/GestureOverlayView.ktFapp/src/main/java/com/example/castapp/ui/view/PrecisionControlPanel.ktDapp/src/main/java/com/example/castapp/ui/view/ResizableBorderView.kt>app/src/main/java/com/example/castapp/ui/view/TextEditPanel.kt?app/src/main/java/com/example/castapp/ui/view/TextWindowView.ktQapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktQapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktFapp/src/main/java/com/example/castapp/ui/windowsettings/CropManager.ktNapp/src/main/java/com/example/castapp/ui/windowsettings/MediaSurfaceManager.ktLapp/src/main/java/com/example/castapp/ui/windowsettings/ScreenshotManager.ktIapp/src/main/java/com/example/castapp/ui/windowsettings/SurfaceManager.ktLapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktKapp/src/main/java/com/example/castapp/ui/windowsettings/TransformHandler.ktKapp/src/main/java/com/example/castapp/ui/windowsettings/TransformManager.ktLapp/src/main/java/com/example/castapp/ui/windowsettings/TransformRenderer.ktPapp/src/main/java/com/example/castapp/ui/windowsettings/WindowPositionManager.ktWapp/src/main/java/com/example/castapp/ui/windowsettings/interfaces/CropStateListener.kt\app/src/main/java/com/example/castapp/ui/windowsettings/interfaces/TransformStateListener.kt5app/src/main/java/com/example/castapp/utils/AppLog.ktBapp/src/main/java/com/example/castapp/utils/ColorPaletteManager.kt9app/src/main/java/com/example/castapp/utils/ColorUtils.kt:app/src/main/java/com/example/castapp/utils/DeviceUtils.kt@app/src/main/java/com/example/castapp/utils/DpiDensityManager.kt@app/src/main/java/com/example/castapp/utils/FontPresetManager.ktDapp/src/main/java/com/example/castapp/utils/FontSizePresetManager.ktIapp/src/main/java/com/example/castapp/utils/LetterSpacingPresetManager.kt@app/src/main/java/com/example/castapp/utils/LetterSpacingSpan.ktGapp/src/main/java/com/example/castapp/utils/LineSpacingPresetManager.kt>app/src/main/java/com/example/castapp/utils/LineSpacingSpan.kt?app/src/main/java/com/example/castapp/utils/MediaFileManager.kt<app/src/main/java/com/example/castapp/utils/MemoryMonitor.kt:app/src/main/java/com/example/castapp/utils/NoteManager.ktBapp/src/main/java/com/example/castapp/utils/NotificationManager.ktBapp/src/main/java/com/example/castapp/utils/PeriodicCleanupTask.kt>app/src/main/java/com/example/castapp/utils/ResourceManager.kt9app/src/main/java/com/example/castapp/utils/StrokeSpan.kt@app/src/main/java/com/example/castapp/utils/TextFormatManager.kt>app/src/main/java/com/example/castapp/utils/TextSizeManager.kt9app/src/main/java/com/example/castapp/utils/ToastUtils.ktDapp/src/main/java/com/example/castapp/utils/WindowScaleCalculator.kt@app/src/main/java/com/example/castapp/viewmodel/MainViewModel.ktDapp/src/main/java/com/example/castapp/viewmodel/ReceiverViewModel.ktBapp/src/main/java/com/example/castapp/viewmodel/SenderViewModel.ktAapp/src/main/java/com/example/castapp/websocket/ControlMessage.ktBapp/src/main/java/com/example/castapp/websocket/WebSocketClient.ktBapp/src/main/java/com/example/castapp/websocket/WebSocketServer.ktGapp/src/main/java/com/example/castapp/utils/CoordinateMappingManager.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       