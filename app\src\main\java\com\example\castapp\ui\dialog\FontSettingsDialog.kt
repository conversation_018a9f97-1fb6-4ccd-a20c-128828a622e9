package com.example.castapp.ui.dialog

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.FontPresetManager
import com.example.castapp.utils.ToastUtils
import java.io.File
import java.io.FileOutputStream

/**
 * 字体设置对话框
 * 提供字体管理功能，包括添加、删除、选择字体
 */
class FontSettingsDialog(
    private val context: Context,
    private val currentFontName: String,
    private val onFontSelected: (FontPresetManager.FontItem) -> Unit,
    private val onFontAdded: ((FontPresetManager.FontItem) -> Unit)? = null,
    private val onFontDeleted: ((FontPresetManager.FontItem) -> Unit)? = null,
    private val onFontNameUpdated: ((FontPresetManager.FontItem, FontPresetManager.FontItem) -> Unit)? = null,
    private val onResetToDefault: (() -> Unit)? = null
) {
    
    private var dialog: AlertDialog? = null
    private lateinit var tvCurrentFont: TextView
    private lateinit var rvFonts: RecyclerView
    private lateinit var etFontName: EditText
    private lateinit var tvSelectedFile: TextView
    private lateinit var btnSelectFile: Button
    private lateinit var btnAddFont: Button
    private lateinit var btnResetDefault: Button
    private lateinit var btnClose: ImageView

    // 字体数据
    private val fontList = mutableListOf<FontItem>()
    private lateinit var fontAdapter: FontAdapter
    private var selectedFontItem: FontPresetManager.FontItem? = null
    private var selectedFontFile: File? = null


    
    /**
     * 字体显示数据项
     */
    data class FontItem(
        val fontItem: FontPresetManager.FontItem,
        var isSelected: Boolean = false
    )
    
    /**
     * 显示对话框
     */
    fun show() {
        try {
            val view = LayoutInflater.from(context).inflate(R.layout.dialog_font_settings, null)

            // 初始化视图
            initViews(view)

            // 初始化数据
            initData()

            // 设置监听器
            setupListeners()

            // 创建对话框
            dialog = AlertDialog.Builder(context)
                .setView(view)
                .setCancelable(true)
                .create()

            dialog?.show()

            AppLog.d("【字体设置对话框】对话框已显示，当前字体: $currentFontName")

        } catch (e: Exception) {
            AppLog.e("【字体设置对话框】显示对话框失败", e)
            ToastUtils.showToast(context, "显示字体设置对话框失败")
        }
    }
    
    /**
     * 初始化视图
     */
    private fun initViews(view: View) {
        tvCurrentFont = view.findViewById(R.id.tv_current_font)
        rvFonts = view.findViewById(R.id.rv_fonts)
        etFontName = view.findViewById(R.id.et_font_name)
        tvSelectedFile = view.findViewById(R.id.tv_selected_file)
        btnSelectFile = view.findViewById(R.id.btn_select_file)
        btnAddFont = view.findViewById(R.id.btn_add_font)
        btnResetDefault = view.findViewById(R.id.btn_reset_default)
        btnClose = view.findViewById(R.id.btn_close)

        // 设置RecyclerView
        rvFonts.layoutManager = LinearLayoutManager(context)
        fontAdapter = FontAdapter()
        rvFonts.adapter = fontAdapter

        AppLog.d("【字体设置对话框】视图初始化完成")
    }
    
    /**
     * 初始化数据
     */
    private fun initData() {
        try {
            // 显示当前字体
            tvCurrentFont.text = currentFontName
            
            // 加载字体列表
            loadFontList()
            
            AppLog.d("【字体设置对话框】数据初始化完成")
            
        } catch (e: Exception) {
            AppLog.e("【字体设置对话框】数据初始化失败", e)
        }
    }
    
    /**
     * 加载字体列表
     */
    private fun loadFontList() {
        try {
            fontList.clear()
            
            val allFonts = FontPresetManager.getAllFonts()
            allFonts.forEach { fontItem ->
                val isSelected = fontItem.name == currentFontName
                fontList.add(FontItem(fontItem, isSelected))
                
                if (isSelected) {
                    selectedFontItem = fontItem
                }
            }
            
            fontAdapter.notifyDataSetChanged()
            
            AppLog.d("【字体设置对话框】已加载字体列表: ${fontList.size}个字体")
            
        } catch (e: Exception) {
            AppLog.e("【字体设置对话框】加载字体列表失败", e)
        }
    }
    
    /**
     * 设置监听器
     */
    private fun setupListeners() {
        // 关闭按钮
        btnClose.setOnClickListener {
            dialog?.dismiss()
        }
        
        // 恢复默认按钮
        btnResetDefault.setOnClickListener {
            showResetConfirmDialog()
        }

        // 选择文件按钮
        btnSelectFile.setOnClickListener {
            showFontFilePicker()
        }

        // 添加字体按钮
        btnAddFont.setOnClickListener {
            addCustomFont()
        }

        // 监听字体名称输入
        etFontName.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                updateAddButtonState()
            }
        })
    }

    /**
     * 显示字体文件选择器
     */
    private fun showFontFilePicker() {
        try {
            val filePicker = FontFilePickerDialog(context) { selectedFile ->
                handleSelectedFile(selectedFile)
            }
            filePicker.show()

        } catch (e: Exception) {
            AppLog.e("【字体设置对话框】显示文件选择器失败", e)
            ToastUtils.showToast(context, "显示文件选择器失败")
        }
    }

    /**
     * 处理选中的文件
     */
    private fun handleSelectedFile(file: File) {
        try {
            selectedFontFile = file
            tvSelectedFile.text = file.name

            // 自动填入文件名作为默认字体名称（去掉扩展名）
            val fileName = file.nameWithoutExtension
            if (etFontName.text.toString().trim().isEmpty()) {
                etFontName.setText(fileName)
            }

            updateAddButtonState()

            AppLog.d("【字体设置对话框】已选择字体文件: ${file.absolutePath}, 默认名称: $fileName")

        } catch (e: Exception) {
            AppLog.e("【字体设置对话框】处理选中文件失败", e)
            ToastUtils.showToast(context, "处理选中文件失败")
        }
    }


    /**
     * 添加自定义字体
     */
    private fun addCustomFont() {
        try {
            var fontName = etFontName.text.toString().trim()
            val fontFile = selectedFontFile

            if (fontFile == null) {
                ToastUtils.showToast(context, "请选择字体文件")
                return
            }

            // 如果没有输入字体名称，使用文件名（去掉扩展名）
            if (fontName.isEmpty()) {
                fontName = fontFile.nameWithoutExtension
            }

            AppLog.d("【字体设置对话框】开始添加字体: 名称=$fontName, 文件=${fontFile.absolutePath}")

            if (!fontFile.exists()) {
                ToastUtils.showToast(context, "字体文件不存在")
                return
            }

            if (!fontFile.canRead()) {
                ToastUtils.showToast(context, "无法读取字体文件，请检查权限")
                return
            }

            AppLog.d("【字体设置对话框】字体文件验证通过: ${fontFile.absolutePath}, 大小: ${fontFile.length()} bytes")

            // 添加字体
            AppLog.d("【字体设置对话框】调用FontPresetManager添加字体")
            val success = FontPresetManager.addCustomFont(fontName, fontFile)

            if (success) {
                AppLog.d("【字体设置对话框】字体添加成功")
                ToastUtils.showToast(context, "字体添加成功")

                // 清空输入
                etFontName.setText("")
                tvSelectedFile.text = "未选择文件"
                selectedFontFile = null
                updateAddButtonState()

                // 重新加载列表
                loadFontList()

                // 通知回调
                FontPresetManager.getFontByName(fontName)?.let { fontItem ->
                    onFontAdded?.invoke(fontItem)
                }

            } else {
                AppLog.e("【字体设置对话框】FontPresetManager返回失败")
                ToastUtils.showToast(context, "字体添加失败，可能名称已存在或文件无效")
            }

        } catch (e: Exception) {
            AppLog.e("【字体设置对话框】添加自定义字体失败", e)
            ToastUtils.showToast(context, "添加字体失败: ${e.message}")
        }
    }
    

    
    /**
     * 更新添加按钮状态
     */
    private fun updateAddButtonState() {
        val hasFile = selectedFontFile != null
        btnAddFont.isEnabled = hasFile
    }
    
    /**
     * 显示重置确认对话框
     */
    private fun showResetConfirmDialog() {
        AlertDialog.Builder(context)
            .setTitle("确认重置")
            .setMessage("确定要删除所有自定义字体并恢复默认设置吗？")
            .setPositiveButton("确定") { _, _ ->
                FontPresetManager.resetToDefault()
                loadFontList()
                onResetToDefault?.invoke()
                ToastUtils.showToast(context, "已恢复默认字体设置")
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 字体适配器
     */
    private inner class FontAdapter : RecyclerView.Adapter<FontAdapter.FontViewHolder>() {
        
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FontViewHolder {
            val view = LayoutInflater.from(context).inflate(
                R.layout.item_font_setting, parent, false
            )
            return FontViewHolder(view)
        }
        
        override fun onBindViewHolder(holder: FontViewHolder, position: Int) {
            holder.bind(fontList[position])
        }
        
        override fun getItemCount(): Int = fontList.size
        
        inner class FontViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            private val tvFontName: TextView = itemView.findViewById(R.id.tv_font_name)
            private val tvPresetTag: TextView = itemView.findViewById(R.id.tv_preset_tag)
            private val ivCurrentIndicator: ImageView = itemView.findViewById(R.id.iv_current_indicator)
            private val btnEdit: ImageView = itemView.findViewById(R.id.btn_edit)

            fun bind(item: FontItem) {
                val fontItem = item.fontItem

                // 设置字体名称和字体样式
                tvFontName.text = fontItem.name
                fontItem.loadTypeface()?.let { typeface ->
                    tvFontName.typeface = typeface
                }

                // 设置预设标识
                tvPresetTag.visibility = if (fontItem.isPreset) View.VISIBLE else View.GONE

                // 设置选中状态指示器
                ivCurrentIndicator.visibility = if (item.isSelected) View.VISIBLE else View.GONE

                // 设置选中状态背景
                itemView.setBackgroundColor(
                    if (item.isSelected) 0x1A4CAF50 else 0x00000000
                )

                // 设置修改按钮（仅自定义字体显示）
                btnEdit.visibility = if (!fontItem.isPreset) View.VISIBLE else View.GONE

                // 点击选择字体
                itemView.setOnClickListener {
                    selectFont(item)
                }

                // 点击修改按钮
                btnEdit.setOnClickListener {
                    showEditFontNameDialog(item)
                }

                // 长按删除（仅自定义字体）
                if (!fontItem.isPreset) {
                    itemView.setOnLongClickListener {
                        showDeleteConfirmDialog(item)
                        true
                    }
                }
            }
        }
    }
    
    /**
     * 选择字体
     */
    private fun selectFont(item: FontItem) {
        try {
            // 更新选中状态
            fontList.forEach { it.isSelected = false }
            item.isSelected = true
            selectedFontItem = item.fontItem
            
            fontAdapter.notifyDataSetChanged()
            
            // 通知回调
            onFontSelected(item.fontItem)
            
            // 关闭对话框
            dialog?.dismiss()
            
            AppLog.d("【字体设置对话框】已选择字体: ${item.fontItem.name}")
            
        } catch (e: Exception) {
            AppLog.e("【字体设置对话框】选择字体失败", e)
        }
    }

    /**
     * 显示字体名称编辑对话框
     */
    private fun showEditFontNameDialog(item: FontItem) {
        try {
            val fontItem = item.fontItem
            if (fontItem.isPreset) {
                ToastUtils.showToast(context, "预设字体不能修改名称")
                return
            }

            val editText = EditText(context).apply {
                setText(fontItem.name)
                selectAll()
                hint = "请输入字体名称"
                setSingleLine(true)
            }

            AlertDialog.Builder(context)
                .setTitle("修改字体名称")
                .setView(editText)
                .setPositiveButton("确定") { _, _ ->
                    val newName = editText.text.toString().trim()
                    updateFontName(item, newName)
                }
                .setNegativeButton("取消", null)
                .show()

            AppLog.d("【字体设置对话框】显示字体名称编辑对话框: ${fontItem.name}")

        } catch (e: Exception) {
            AppLog.e("【字体设置对话框】显示字体名称编辑对话框失败", e)
        }
    }

    /**
     * 更新字体名称
     */
    private fun updateFontName(item: FontItem, newName: String) {
        try {
            if (newName.isBlank()) {
                ToastUtils.showToast(context, "字体名称不能为空")
                return
            }

            val success = FontPresetManager.updateCustomFontName(item.fontItem, newName)
            if (success) {
                ToastUtils.showToast(context, "字体名称修改成功")

                // 重新加载字体列表
                loadFontList()

                // 通知回调
                val newFontItem = FontPresetManager.getFontByName(newName)
                if (newFontItem != null) {
                    onFontNameUpdated?.invoke(item.fontItem, newFontItem)
                }

                AppLog.d("【字体设置对话框】字体名称修改成功: ${item.fontItem.name} -> $newName")
            } else {
                ToastUtils.showToast(context, "字体名称修改失败，可能名称已存在")
                AppLog.w("【字体设置对话框】字体名称修改失败: ${item.fontItem.name} -> $newName")
            }

        } catch (e: Exception) {
            AppLog.e("【字体设置对话框】更新字体名称失败", e)
            ToastUtils.showToast(context, "修改字体名称失败: ${e.message}")
        }
    }

    /**
     * 显示删除确认对话框
     */
    private fun showDeleteConfirmDialog(item: FontItem) {
        AlertDialog.Builder(context)
            .setTitle("确认删除")
            .setMessage("确定要删除字体 \"${item.fontItem.name}\" 吗？")
            .setPositiveButton("删除") { _, _ ->
                deleteFont(item)
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 删除字体
     */
    private fun deleteFont(item: FontItem) {
        try {
            val success = FontPresetManager.deleteCustomFont(item.fontItem)
            if (success) {
                ToastUtils.showToast(context, "字体删除成功")
                loadFontList()
                onFontDeleted?.invoke(item.fontItem)
            } else {
                ToastUtils.showToast(context, "字体删除失败")
            }
            
        } catch (e: Exception) {
            AppLog.e("【字体设置对话框】删除字体失败", e)
            ToastUtils.showToast(context, "删除字体失败")
        }
    }
}
