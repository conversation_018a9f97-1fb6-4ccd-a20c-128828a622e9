package com.example.castapp.websocket

import com.example.castapp.viewmodel.MainViewModel
import org.java_websocket.WebSocket
import org.java_websocket.handshake.ClientHandshake
import org.java_websocket.server.WebSocketServer as JavaWebSocketServer
import java.net.InetSocketAddress
import java.util.concurrent.ConcurrentHashMap
import com.example.castapp.utils.AppLog

/**
 * WebSocket服务器
 * 用于接收发送端的控制消息
 */
class WebSocketServer(
    port: Int,
    private val onMessageReceived: (ControlMessage) -> Unit,
    private val onConnectionRequest: (String, String, Int, String?) -> Unit = { _, _, _, _ -> }, // connectionId, clientIP, clientPort, deviceName
    private val serverType: String = "casting" // 🐾 新增：服务器类型标识，"casting"为投屏服务器，"remote_control"为远程控制服务器
) : JavaWebSocketServer(InetSocketAddress(port)) {

    // 存储连接ID到WebSocket的映射
    private val connectionMap = ConcurrentHashMap<String, WebSocket>()
    // 存储WebSocket到连接ID的反向映射
    private val reverseConnectionMap = ConcurrentHashMap<WebSocket, String>()

    private var isRunning = false
    private var disconnectNotificationSent = false

    init {
        // 在构造函数中设置端口重用，确保服务器停止后端口能立即被重用
        try {
            isReuseAddr = true
            AppLog.websocket("WebSocket服务器端口重用已在构造函数中启用")
        } catch (e: Exception) {
            AppLog.w("在构造函数中设置WebSocket服务器端口重用失败: ${e.message}")
        }
    }

    override fun onOpen(conn: WebSocket, handshake: ClientHandshake) {
        AppLog.websocket("【WebSocket服务器】新的WebSocket连接: ${conn.remoteSocketAddress}")
        AppLog.websocket("【WebSocket服务器】客户端地址: ${conn.remoteSocketAddress.address.hostAddress}")
        AppLog.websocket("【WebSocket服务器】客户端端口: ${conn.remoteSocketAddress.port}")
    }

    override fun onClose(conn: WebSocket, code: Int, reason: String, remote: Boolean) {
        val connectionId = reverseConnectionMap.remove(conn)
        if (connectionId != null) {
            connectionMap.remove(connectionId)
            AppLog.websocket("WebSocket连接关闭: $connectionId, 原因: $reason, 服务器类型: $serverType")

            // 🐾 关键修复：远程控制服务器不发送disconnect消息，避免触发投屏窗口移除
            if (serverType == "remote_control") {
                AppLog.websocket("【远程控制服务器】连接关闭，不发送disconnect消息: $connectionId")
                return
            }

            // 只有投屏服务器在非正常关闭时才发送断开连接消息
            if (code != 1000) { // 1000 = NORMAL_CLOSURE
                AppLog.websocket("【投屏服务器】非正常关闭，发送断开连接消息: $connectionId, code: $code")
                val disconnectMessage = ControlMessage.createDisconnect(connectionId)
                onMessageReceived(disconnectMessage)

                // 额外的安全措施：直接通知UI移除窗口（防止断开消息处理失败）
                try {
                    MainViewModel.getInstance()?.handleConnectionDisconnected(connectionId)
                    AppLog.websocket("【WebSocket安全措施】已直接通知UI移除窗口: $connectionId")
                } catch (e: Exception) {
                    AppLog.e("【WebSocket安全措施】直接通知UI失败: $connectionId", e)
                }
            } else {
                AppLog.websocket("【投屏服务器】正常关闭，跳过发送断开连接消息: $connectionId")
            }
        }
    }

    override fun onMessage(conn: WebSocket, message: String) {
        try {
            AppLog.websocket("【WebSocket服务器】收到原始消息: $message")
            AppLog.websocket("【WebSocket服务器】来自客户端: ${conn.remoteSocketAddress}")

            val controlMessage = ControlMessage.fromJson(message) ?: return
            AppLog.websocket("【WebSocket服务器】解析消息成功，类型: ${controlMessage.type}")

            if (controlMessage.type != ControlMessage.TYPE_HEARTBEAT) {
                AppLog.websocket("【WebSocket服务器】收到控制消息: ${controlMessage.type}, 连接ID: ${controlMessage.connectionId}")
            }

            if (controlMessage.type == ControlMessage.TYPE_CONNECTION_REQUEST) {
                val connectionId = controlMessage.connectionId
                AppLog.websocket("【连接请求】开始处理连接请求: $connectionId")

                connectionMap[connectionId] = conn
                reverseConnectionMap[conn] = connectionId

                // 获取客户端IP地址和端口信息
                val clientAddress = conn.remoteSocketAddress
                val clientIP = clientAddress.address.hostAddress ?: "unknown"
                val clientPort = clientAddress.port // 真实的WebSocket客户端端口

                // 提取设备名称
                val deviceName = controlMessage.getStringData("device_name")

                AppLog.websocket("【连接请求】建立连接映射: $connectionId")
                AppLog.websocket("【连接请求】客户端IP: $clientIP")
                AppLog.websocket("【连接请求】客户端WebSocket端口: $clientPort")
                AppLog.websocket("【连接请求】设备名称: $deviceName")
                AppLog.websocket("【连接请求】准备调用onConnectionRequest回调")

                // 通知ReceivingService处理连接请求，使用真实的WebSocket客户端端口
                onConnectionRequest(connectionId, clientIP, clientPort, deviceName)
                AppLog.websocket("【连接请求】onConnectionRequest回调已调用完成")

                val response = ControlMessage.createConnectionResponse(connectionId, true, "连接成功")
                sendMessage(connectionId, response)
                AppLog.websocket("【连接请求】已发送连接响应: $connectionId")
            }

            onMessageReceived(controlMessage)

        } catch (e: Exception) {
            AppLog.e("【WebSocket服务器】处理消息失败: $message", e)
        }
    }

    override fun onError(conn: WebSocket?, ex: Exception) {
        AppLog.e("WebSocket错误", ex)

        if (conn != null) {
            val connectionId = reverseConnectionMap[conn]
            if (connectionId != null) {
                AppLog.e("连接 $connectionId 发生错误")
            }
        }
    }

    override fun onStart() {
        isRunning = true
        disconnectNotificationSent = false // 重置断开通知标志位
        AppLog.websocket("WebSocket服务器启动成功，端口: ${address.port}")
    }

    /**
     * 向指定连接发送消息（私有方法）
     */
    private fun sendMessage(connectionId: String, message: ControlMessage): Boolean {
        val conn = connectionMap[connectionId]
        return if (conn != null && conn.isOpen) {
            try {
                conn.send(message.toJson())
                AppLog.websocket("发送消息到 $connectionId: ${message.type}")
                true
            } catch (e: Exception) {
                AppLog.e("发送消息失败到 $connectionId", e)
                false
            }
        } else {
            AppLog.w("连接 $connectionId 不存在或已关闭")
            false
        }
    }

    /**
     * 向指定连接发送消息（公共方法）
     */
    fun sendMessageToClient(connectionId: String, message: ControlMessage): Boolean {
        return sendMessage(connectionId, message)
    }

    /**
     * 获取所有活跃连接ID
     */
    fun getActiveConnectionIds(): Set<String> {
        return connectionMap.filterValues { it.isOpen }.keys
    }

    /**
     * 广播消息到所有活跃连接
     */
    fun broadcastMessage(message: ControlMessage): Int {
        var successCount = 0
        val activeConnections = connectionMap.filterValues { it.isOpen }

        for ((connectionId, conn) in activeConnections) {
            try {
                conn.send(message.toJson())
                successCount++
                AppLog.websocket("广播消息到 $connectionId: ${message.type}")
            } catch (e: Exception) {
                AppLog.e("广播消息失败到 $connectionId", e)
            }
        }

        AppLog.d("广播消息完成: ${message.type}, 成功: $successCount/${activeConnections.size}")
        return successCount
    }

    /**
     * 根据连接ID获取客户端IP地址（统一ID架构）
     */
    fun getClientIP(connectionId: String): String? {
        val conn = connectionMap[connectionId]
        return if (conn != null && conn.isOpen) {
            try {
                // 从WebSocket连接中提取客户端IP
                conn.remoteSocketAddress?.address?.hostAddress
            } catch (e: Exception) {
                AppLog.e("获取客户端IP失败: $connectionId", e)
                null
            }
        } else {
            null
        }
    }

    /**
     * 根据连接ID获取客户端端口（统一ID架构）
     */
    fun getClientPort(connectionId: String): Int? {
        val conn = connectionMap[connectionId]
        return if (conn != null && conn.isOpen) {
            try {
                // 从WebSocket连接中提取客户端端口
                conn.remoteSocketAddress?.port
            } catch (e: Exception) {
                AppLog.e("获取客户端端口失败: $connectionId", e)
                null
            }
        } else {
            null
        }
    }

    /**
     * 停止服务器
     */
    fun stopServer() {
        try {
            isRunning = false

            // 只有在还没有发送过断开通知时才发送（避免重复发送）
            if (!disconnectNotificationSent) {
                AppLog.websocket("发送断开通知（WebSocket服务器停止时）")
                notifyAllConnectionsDisconnect()
                disconnectNotificationSent = true
            } else {
                AppLog.websocket("断开通知已发送过，跳过重复发送")
            }

            stop()
            connectionMap.clear()
            reverseConnectionMap.clear()
            AppLog.websocket("WebSocket服务器已停止")
        } catch (e: Exception) {
            AppLog.e("停止WebSocket服务器失败", e)
        }
    }

    /**
     * 标记断开通知已发送（供外部调用）
     */
    fun markDisconnectNotificationSent() {
        disconnectNotificationSent = true
        AppLog.websocket("断开通知标志位已设置")
    }

    /**
     * 🗑️ 断开特定连接（用于窗口删除等场景）- 优化版本
     */
    fun disconnectSpecificConnection(connectionId: String) {
        try {
            val conn = connectionMap[connectionId]
            if (conn != null && conn.isOpen) {
                AppLog.websocket("【断开特定连接】开始断开连接: $connectionId")

                // 🚀 优化：窗口删除时不发送断开消息，避免重复处理
                // 因为客户端已经在删除流程中被断开，不需要再通知
                AppLog.websocket("【断开特定连接】跳过发送断开通知，直接关闭连接: $connectionId")

                // 直接关闭连接
                conn.close(1000, "窗口删除")
                AppLog.websocket("【断开特定连接】已关闭连接: $connectionId")

                // 清理映射
                connectionMap.remove(connectionId)
                reverseConnectionMap.remove(conn)
                AppLog.websocket("【断开特定连接】已清理连接映射: $connectionId")
            } else {
                AppLog.w("【断开特定连接】连接不存在或已关闭: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("【断开特定连接】断开连接失败: $connectionId", e)
        }
    }

    /**
     * 通知所有连接断开
     */
    private fun notifyAllConnectionsDisconnect() {
        try {
            val activeConnections = connectionMap.toMap() // 创建副本避免并发修改
            AppLog.websocket("准备通知 ${activeConnections.size} 个连接断开")

            activeConnections.forEach { (connectionId, conn) ->
                if (conn.isOpen) {
                    try {
                        // 发送断开连接消息给发送端
                        val disconnectMessage = ControlMessage.createDisconnect(connectionId)
                        conn.send(disconnectMessage.toJson())
                        AppLog.websocket("已向连接 $connectionId 发送断开通知")

                        // 通知本地处理断开连接
                        onMessageReceived(disconnectMessage)

                        // 给发送端一点时间处理断开消息
                        Thread.sleep(100)

                        // 关闭连接
                        conn.close(1000, "服务器停止")
                    } catch (e: Exception) {
                        AppLog.e("通知连接 $connectionId 断开失败", e)
                    }
                }
            }

            AppLog.websocket("已完成所有连接的断开通知")
        } catch (e: Exception) {
            AppLog.e("通知所有连接断开失败", e)
        }
    }
}
