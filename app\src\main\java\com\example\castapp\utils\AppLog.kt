package com.example.castapp.utils

import android.util.Log

/**
 * CastAPP统一日志工具类
 * 
 * 功能特点：
 * - 统一使用"CastAPP"标签，方便在logcat中过滤：tag:CastAPP
 * - 自动添加类名和方法名信息，便于定位问题
 * - 支持所有Android日志级别
 * - 在Release版本中可以轻松禁用日志输出
 * 
 * 使用方法：
 * - AppLog.d("调试信息")
 * - AppLog.i("普通信息") 
 * - AppLog.w("警告信息")
 * - AppLog.e("错误信息", exception)
 * - AppLog.v("详细信息")
 */
object AppLog {
    
    // 统一的应用标签
    private const val APP_TAG = "CastAPP"
    
    // 是否启用日志输出（Release版本可设为false）
    private const val LOG_ENABLED = true
    
    /**
     * 🚀 性能优化：简化消息格式化
     * 移除字符串拼接操作，直接返回原始消息
     */
    private fun formatMessage(message: String): String {
        // 🚀 CPU优化：直接返回消息，避免不必要的字符串操作
        return message
    }
    
    /**
     * Debug级别日志
     */
    fun d(message: String) {
        if (LOG_ENABLED) {
            Log.d(APP_TAG, formatMessage(message))
        }
    }
    
    /**
     * Info级别日志
     * 公共API - 提供标准的Info级别日志输出
     */
    @Suppress("unused") // 公共API，可能在未来使用
    fun i(message: String) {
        if (LOG_ENABLED) {
            Log.i(APP_TAG, formatMessage(message))
        }
    }
    
    /**
     * Warning级别日志
     */
    fun w(message: String) {
        if (LOG_ENABLED) {
            Log.w(APP_TAG, formatMessage(message))
        }
    }
    
    /**
     * Warning级别日志（带异常）
     */
    fun w(message: String, throwable: Throwable) {
        if (LOG_ENABLED) {
            Log.w(APP_TAG, formatMessage(message), throwable)
        }
    }
    
    /**
     * Error级别日志
     */
    fun e(message: String) {
        if (LOG_ENABLED) {
            Log.e(APP_TAG, formatMessage(message))
        }
    }
    
    /**
     * Error级别日志（带异常）
     */
    fun e(message: String, throwable: Throwable) {
        if (LOG_ENABLED) {
            Log.e(APP_TAG, formatMessage(message), throwable)
        }
    }
    
    /**
     * Verbose级别日志
     */
    fun v(message: String) {
        if (LOG_ENABLED) {
            Log.v(APP_TAG, formatMessage(message))
        }
    }
    
    /**
     * 性能测试日志（专门用于性能统计）
     * 公共API - 专门用于性能相关的日志输出
     */
    @Suppress("unused") // 公共API，用于性能监控和调试
    fun perf(message: String) {
        if (LOG_ENABLED) {
            Log.i(APP_TAG, formatMessage("[PERF] $message"))
        }
    }
    
    /**
     * 网络相关日志
     */
    fun network(message: String) {
        if (LOG_ENABLED) {
            Log.d(APP_TAG, formatMessage("[NET] $message"))
        }
    }
    
    /**
     * 音频相关日志
     */
    fun audio(message: String) {
        if (LOG_ENABLED) {
            Log.i(APP_TAG, formatMessage("[AUDIO] $message"))
        }
    }
    
    /**
     * 视频相关日志
     */
    fun video(message: String) {
        if (LOG_ENABLED) {
            Log.i(APP_TAG, formatMessage("[VIDEO] $message"))
        }
    }
    
    /**
     * 内存相关日志
     */
    fun memory(message: String) {
        if (LOG_ENABLED) {
            Log.d(APP_TAG, formatMessage("[MEM] $message"))
        }
    }
    
    /**
     * WebSocket相关日志
     */
    fun websocket(message: String) {
        if (LOG_ENABLED) {
            Log.d(APP_TAG, formatMessage("[WS] $message"))
        }
    }
    
    /**
     * 服务相关日志
     */
    fun service(message: String) {
        if (LOG_ENABLED) {
            Log.i(APP_TAG, formatMessage("[SERVICE] $message"))
        }
    }
    
    /**
     * 权限相关日志
     */
    fun permission(message: String) {
        if (LOG_ENABLED) {
            Log.d(APP_TAG, formatMessage("[PERM] $message"))
        }
    }
    
    /**
     * 状态管理相关日志
     */
    fun state(message: String) {
        if (LOG_ENABLED) {
            Log.d(APP_TAG, formatMessage("[STATE] $message"))
        }
    }
    
    /**
     * 清理任务相关日志
     */
    fun cleanup(message: String) {
        if (LOG_ENABLED) {
            Log.d(APP_TAG, formatMessage("[CLEANUP] $message"))
        }
    }
}
