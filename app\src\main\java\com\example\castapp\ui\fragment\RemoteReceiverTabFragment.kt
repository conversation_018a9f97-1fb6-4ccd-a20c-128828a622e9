package com.example.castapp.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.ui.adapter.RemoteReceiverDeviceAdapter
import com.example.castapp.model.RemoteReceiverConnection

/**
 * 接收端标签页Fragment
 * 显示和管理远程接收端设备列表
 */
class RemoteReceiverTabFragment : Fragment() {

    private lateinit var receiverConnectionCountText: TextView
    private lateinit var addReceiverConnectionButton: ImageButton
    private lateinit var receiverConnectionsRecyclerView: RecyclerView
    private lateinit var receiverAdapter: RemoteReceiverDeviceAdapter

    private val receivers = mutableListOf<RemoteReceiverConnection>()

    // 回调接口
    private var onConnectClickListener: ((RemoteReceiverConnection) -> Unit)? = null
    private var onControlClickListener: ((RemoteReceiverConnection) -> Unit)? = null
    private var onEditClickListener: ((RemoteReceiverConnection) -> Unit)? = null
    private var onDeleteClickListener: ((RemoteReceiverConnection) -> Unit)? = null
    private var onAddReceiverClickListener: (() -> Unit)? = null

    companion object {
        fun newInstance(): RemoteReceiverTabFragment {
            return RemoteReceiverTabFragment()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_receiver_tab, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setupRecyclerView()
        setupClickListeners()
    }

    private fun initViews(view: View) {
        receiverConnectionCountText = view.findViewById(R.id.receiver_connection_count_text)
        addReceiverConnectionButton = view.findViewById(R.id.add_receiver_connection_button)
        receiverConnectionsRecyclerView = view.findViewById(R.id.receiver_connections_recycler_view)
    }

    private fun setupRecyclerView() {
        receiverAdapter = RemoteReceiverDeviceAdapter(
            receivers = receivers,
            onConnectClick = { receiver ->
                onConnectClickListener?.invoke(receiver)
            },
            onControlClick = { receiver ->
                onControlClickListener?.invoke(receiver)
            },
            onEditClick = { receiver ->
                onEditClickListener?.invoke(receiver)
            },
            onDeleteClick = { receiver ->
                onDeleteClickListener?.invoke(receiver)
            }
        )
        
        receiverConnectionsRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = receiverAdapter
        }
    }

    private fun setupClickListeners() {
        addReceiverConnectionButton.setOnClickListener {
            onAddReceiverClickListener?.invoke()
        }
    }

    /**
     * 设置连接点击监听器
     */
    fun setOnConnectClickListener(listener: (RemoteReceiverConnection) -> Unit) {
        onConnectClickListener = listener
    }

    /**
     * 设置控制点击监听器
     */
    fun setOnControlClickListener(listener: (RemoteReceiverConnection) -> Unit) {
        onControlClickListener = listener
    }

    /**
     * 设置编辑点击监听器
     */
    fun setOnEditClickListener(listener: (RemoteReceiverConnection) -> Unit) {
        onEditClickListener = listener
    }

    /**
     * 设置删除点击监听器
     */
    fun setOnDeleteClickListener(listener: (RemoteReceiverConnection) -> Unit) {
        onDeleteClickListener = listener
    }

    /**
     * 设置添加接收端点击监听器
     */
    fun setOnAddReceiverClickListener(listener: () -> Unit) {
        onAddReceiverClickListener = listener
    }

    /**
     * 更新接收端列表
     */
    fun updateReceivers(newReceivers: List<RemoteReceiverConnection>) {
        receivers.clear()
        receivers.addAll(newReceivers)
        receiverAdapter.notifyDataSetChanged()
        updateReceiverCount()
    }

    /**
     * 添加接收端
     */
    fun addReceiver(receiver: RemoteReceiverConnection) {
        receiverAdapter.addReceiver(receiver)
        updateReceiverCount()
    }

    /**
     * 更新接收端状态
     */
    fun updateReceiver(receiver: RemoteReceiverConnection) {
        receiverAdapter.updateReceiver(receiver)
    }

    /**
     * 删除接收端
     */
    fun removeReceiver(receiver: RemoteReceiverConnection) {
        receiverAdapter.removeReceiver(receiver)
        updateReceiverCount()
    }

    /**
     * 更新接收端数量显示
     */
    private fun updateReceiverCount() {
        receiverConnectionCountText.text = "${receivers.size}个设备"
    }
}
