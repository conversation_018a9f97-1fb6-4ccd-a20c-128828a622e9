-- Merging decision tree log ---
manifest
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:2:1-102:12
INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:2:1-102:12
INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:2:1-102:12
INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:2:1-102:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbc1af71e9c7a6d81d74e5415e27cca6\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.skydoves:colorpickerview:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aea08ab35834dc872b63d439d8e3e2ec\transformed\colorpickerview-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d5eaad81b8f7ff11d8d513b9a4ab70\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c2ebc944a1b713ae7f8e7a60e0d8c64\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3713a0c8f8ac4a98c195ead755fc68d\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3b921da228be640aba302ba99582620\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\754ceb2721ec9435c5544a4d26767ca5\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\966496e81aa0f2b9d3dfaf6862e22ced\transformed\fragment-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\919890b08c7d9f2c50942dc12125ca6b\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab5b74a91933de5b803b79a8692aa797\transformed\activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\742b85b0f667201d1063be02984c899b\transformed\activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f1e300ebfcd9a1938350b71ea84b802\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91680a5de2e6fdaf18ed75ae680d033\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8de88fe7432ef8925d03d07e8f25d6f7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b34fcafd738bc0f76beb23b14778aba\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9442e82dcade1d54102ce387f336942c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6ac4a4aa7daba555578b2a36304760e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\beff992f856d734f064ccfd9e4eea167\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3091dde40a58b1342004974603312dfc\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4eaef491ad8cd254bbfe1c40284ef97\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0463d8aea2df3b07ba7f901c0f9e1d70\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46251b6b523d560ec9fbf9198b7d8031\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbcf9a86d284ab707ef2b95e5f8e5ae3\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\992746c81f4c49f6db64fe433c4d2b70\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b16c73ef6056f1a31596023c6be03ebe\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3c81bd0e7c2de8c2c20f2eeb9cb9e9\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\436cfa9371b9fe6b6d8dc2735f397ab6\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3dede8157ec001a04e346e71fcb8bb2a\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f94c38bb4e4e79fbd3dcaace48e5c418\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78f8605ee6a0193cb2238debf3fc2e72\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b39b71c06562d2866e999e733b0dbe9f\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8160253ad106fd84d1b3c72983d659d\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8619a6c361c2475ffca89a7138fc4f5a\transformed\lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08d319e430a2dd770ec1a536b952306b\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8029a7a6240f5dd65441f32c6abd8a5f\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\922f6413f75cfdfdca202608fd6f9377\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5eac572bc5d79469587f41fef3768795\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7aceb956c4afc46c2d525b16deccb6ca\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcc9f887acf7d3dc8b25bdeef4bd255a\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db9d38a5b21456e0727dc3e0e5f5d1c2\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad56f7921b6c15b370779da1f40e3c77\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ea5b3c609f2f0dbad137707758a4070\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78b7e2188878810d09b47a0afd275b87\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.databinding:viewbinding:8.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85777f99c56d56f51b8e0dc3470db3ec\transformed\viewbinding-8.1.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0e4c92381e428813e332c467c4e046c\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ed41237343a1afa65ba5bb5fd078551\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7d7af5fc5f6a597b905327c7d1792a9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b377d6660d9a0ee7b9c34cc9286842c4\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c8542a27eaea11264689b21eb4903c0\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da2bfc1c4fbcc7c8df5dc92704ceb28\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ccca0b213f68866681211f09910d132\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:8:5-79
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:8:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:9:5-76
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:9:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:10:5-76
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:10:22-73
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:13:5-71
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:13:22-68
uses-permission#android.permission.CAMERA
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:14:5-65
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:14:22-62
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:15:5-77
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:15:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:16:5-94
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:16:22-91
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:17:5-92
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:17:22-89
uses-permission#android.permission.FOREGROUND_SERVICE_MICROPHONE
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:18:5-88
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:18:22-85
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:19:5-89
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:19:22-86
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:20:5-77
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:20:22-74
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:23:5-24:40
	tools:ignore
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:24:9-37
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:23:22-79
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:25:5-76
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:25:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:26:5-75
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:26:22-72
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:29:5-78
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:29:22-75
uses-feature#android.hardware.wifi
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:32:5-82
	android:required
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:32:56-79
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:32:19-55
uses-feature#android.hardware.camera
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:33:5-85
	android:required
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:33:58-82
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:33:19-57
uses-feature#android.hardware.screen.landscape
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:34:5-95
	android:required
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:34:68-92
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:34:19-67
uses-feature#android.hardware.screen.portrait
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:35:5-94
	android:required
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:35:67-91
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:35:19-66
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:38:5-80
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:38:22-77
application
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:40:5-100:19
INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:40:5-100:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbc1af71e9c7a6d81d74e5415e27cca6\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbc1af71e9c7a6d81d74e5415e27cca6\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c2ebc944a1b713ae7f8e7a60e0d8c64\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c2ebc944a1b713ae7f8e7a60e0d8c64\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ed41237343a1afa65ba5bb5fd078551\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ed41237343a1afa65ba5bb5fd078551\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7d7af5fc5f6a597b905327c7d1792a9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7d7af5fc5f6a597b905327c7d1792a9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:43:9-35
	android:label
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:42:9-32
	android:hardwareAccelerated
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:45:9-43
	tools:targetApi
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:48:9-29
	android:largeHeap
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:46:9-33
	android:allowBackup
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:41:9-35
	android:theme
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:44:9-65
	android:usesCleartextTraffic
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:47:9-44
activity#com.example.castapp.ui.MainActivity
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:50:9-58:20
	android:screenOrientation
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:53:13-49
	android:exported
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:52:13-36
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:51:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:54:13-57:29
action#android.intent.action.MAIN
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:55:17-69
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:55:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:56:17-77
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:56:27-74
service#com.example.castapp.service.CastingService
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:60:9-64:63
	android:enabled
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:62:13-35
	android:exported
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:63:13-37
	android:foregroundServiceType
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:64:13-60
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:61:13-51
service#com.example.castapp.service.ReceivingService
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:66:9-70:61
	android:enabled
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:68:13-35
	android:exported
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:69:13-37
	android:foregroundServiceType
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:70:13-58
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:67:13-53
service#com.example.castapp.service.AudioService
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:72:9-76:74
	android:enabled
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:74:13-35
	android:exported
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:75:13-37
	android:foregroundServiceType
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:76:13-71
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:73:13-49
service#com.example.castapp.service.FloatingStopwatchService
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:78:9-87:19
	android:enabled
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:80:13-35
	android:exported
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:81:13-37
	android:foregroundServiceType
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:82:13-55
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:79:13-61
property#android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:84:13-86:42
	android:value
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:86:17-39
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:85:17-76
service#com.example.castapp.service.RemoteReceiverService
ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:89:9-98:19
	android:enabled
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:91:13-35
	android:exported
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:92:13-37
	android:foregroundServiceType
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:93:13-55
	android:name
		ADDED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml:90:13-58
uses-sdk
INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml
INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbc1af71e9c7a6d81d74e5415e27cca6\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbc1af71e9c7a6d81d74e5415e27cca6\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.skydoves:colorpickerview:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aea08ab35834dc872b63d439d8e3e2ec\transformed\colorpickerview-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.skydoves:colorpickerview:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aea08ab35834dc872b63d439d8e3e2ec\transformed\colorpickerview-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d5eaad81b8f7ff11d8d513b9a4ab70\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d5eaad81b8f7ff11d8d513b9a4ab70\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c2ebc944a1b713ae7f8e7a60e0d8c64\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c2ebc944a1b713ae7f8e7a60e0d8c64\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3713a0c8f8ac4a98c195ead755fc68d\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3713a0c8f8ac4a98c195ead755fc68d\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3b921da228be640aba302ba99582620\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3b921da228be640aba302ba99582620\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\754ceb2721ec9435c5544a4d26767ca5\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\754ceb2721ec9435c5544a4d26767ca5\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\966496e81aa0f2b9d3dfaf6862e22ced\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\966496e81aa0f2b9d3dfaf6862e22ced\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\919890b08c7d9f2c50942dc12125ca6b\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\919890b08c7d9f2c50942dc12125ca6b\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab5b74a91933de5b803b79a8692aa797\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab5b74a91933de5b803b79a8692aa797\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\742b85b0f667201d1063be02984c899b\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\742b85b0f667201d1063be02984c899b\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f1e300ebfcd9a1938350b71ea84b802\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f1e300ebfcd9a1938350b71ea84b802\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91680a5de2e6fdaf18ed75ae680d033\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91680a5de2e6fdaf18ed75ae680d033\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8de88fe7432ef8925d03d07e8f25d6f7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8de88fe7432ef8925d03d07e8f25d6f7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b34fcafd738bc0f76beb23b14778aba\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b34fcafd738bc0f76beb23b14778aba\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9442e82dcade1d54102ce387f336942c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9442e82dcade1d54102ce387f336942c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6ac4a4aa7daba555578b2a36304760e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6ac4a4aa7daba555578b2a36304760e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\beff992f856d734f064ccfd9e4eea167\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\beff992f856d734f064ccfd9e4eea167\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3091dde40a58b1342004974603312dfc\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3091dde40a58b1342004974603312dfc\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4eaef491ad8cd254bbfe1c40284ef97\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4eaef491ad8cd254bbfe1c40284ef97\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0463d8aea2df3b07ba7f901c0f9e1d70\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0463d8aea2df3b07ba7f901c0f9e1d70\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46251b6b523d560ec9fbf9198b7d8031\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46251b6b523d560ec9fbf9198b7d8031\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbcf9a86d284ab707ef2b95e5f8e5ae3\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbcf9a86d284ab707ef2b95e5f8e5ae3\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\992746c81f4c49f6db64fe433c4d2b70\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\992746c81f4c49f6db64fe433c4d2b70\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b16c73ef6056f1a31596023c6be03ebe\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b16c73ef6056f1a31596023c6be03ebe\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3c81bd0e7c2de8c2c20f2eeb9cb9e9\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3c81bd0e7c2de8c2c20f2eeb9cb9e9\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\436cfa9371b9fe6b6d8dc2735f397ab6\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\436cfa9371b9fe6b6d8dc2735f397ab6\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3dede8157ec001a04e346e71fcb8bb2a\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3dede8157ec001a04e346e71fcb8bb2a\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f94c38bb4e4e79fbd3dcaace48e5c418\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f94c38bb4e4e79fbd3dcaace48e5c418\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78f8605ee6a0193cb2238debf3fc2e72\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78f8605ee6a0193cb2238debf3fc2e72\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b39b71c06562d2866e999e733b0dbe9f\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b39b71c06562d2866e999e733b0dbe9f\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8160253ad106fd84d1b3c72983d659d\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8160253ad106fd84d1b3c72983d659d\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8619a6c361c2475ffca89a7138fc4f5a\transformed\lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8619a6c361c2475ffca89a7138fc4f5a\transformed\lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08d319e430a2dd770ec1a536b952306b\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08d319e430a2dd770ec1a536b952306b\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8029a7a6240f5dd65441f32c6abd8a5f\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8029a7a6240f5dd65441f32c6abd8a5f\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\922f6413f75cfdfdca202608fd6f9377\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\922f6413f75cfdfdca202608fd6f9377\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5eac572bc5d79469587f41fef3768795\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5eac572bc5d79469587f41fef3768795\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7aceb956c4afc46c2d525b16deccb6ca\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7aceb956c4afc46c2d525b16deccb6ca\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcc9f887acf7d3dc8b25bdeef4bd255a\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcc9f887acf7d3dc8b25bdeef4bd255a\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db9d38a5b21456e0727dc3e0e5f5d1c2\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db9d38a5b21456e0727dc3e0e5f5d1c2\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad56f7921b6c15b370779da1f40e3c77\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad56f7921b6c15b370779da1f40e3c77\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ea5b3c609f2f0dbad137707758a4070\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ea5b3c609f2f0dbad137707758a4070\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78b7e2188878810d09b47a0afd275b87\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78b7e2188878810d09b47a0afd275b87\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.databinding:viewbinding:8.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85777f99c56d56f51b8e0dc3470db3ec\transformed\viewbinding-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85777f99c56d56f51b8e0dc3470db3ec\transformed\viewbinding-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0e4c92381e428813e332c467c4e046c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0e4c92381e428813e332c467c4e046c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ed41237343a1afa65ba5bb5fd078551\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ed41237343a1afa65ba5bb5fd078551\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7d7af5fc5f6a597b905327c7d1792a9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7d7af5fc5f6a597b905327c7d1792a9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b377d6660d9a0ee7b9c34cc9286842c4\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b377d6660d9a0ee7b9c34cc9286842c4\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c8542a27eaea11264689b21eb4903c0\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c8542a27eaea11264689b21eb4903c0\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da2bfc1c4fbcc7c8df5dc92704ceb28\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da2bfc1c4fbcc7c8df5dc92704ceb28\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ccca0b213f68866681211f09910d132\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ccca0b213f68866681211f09910d132\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Android\AndroidProject\CastAPP\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7d7af5fc5f6a597b905327c7d1792a9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7d7af5fc5f6a597b905327c7d1792a9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cef3d501089c1640ec455754068f51c6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.castapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.castapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b74c097ccea204c2309e4f5f5bb2f587\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\073e64923e79c4fa922c80f20831eb5f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce06f3ff9184025f52d2bd3757b0e8a\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e157be4c31beb863fa43e5eb88946a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
