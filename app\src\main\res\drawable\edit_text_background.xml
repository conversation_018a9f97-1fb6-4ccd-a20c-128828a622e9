<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 获得焦点时的状态 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="#F8F9FA" />
            <corners android:radius="6dp" />
            <stroke android:width="2dp" android:color="#2196F3" />
        </shape>
    </item>

    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#F5F5F5" />
            <corners android:radius="6dp" />
            <stroke android:width="1dp" android:color="#E0E0E0" />
        </shape>
    </item>
</selector>
