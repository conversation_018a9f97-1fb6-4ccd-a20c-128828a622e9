package com.example.castapp.database.converter

import androidx.room.TypeConverter
import java.util.Date

/**
 * Room数据库日期类型转换器
 * 用于在Date和Long之间进行转换
 */
class DateConverter {
    
    /**
     * 将时间戳转换为Date对象
     */
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }
    
    /**
     * 将Date对象转换为时间戳
     */
    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }
}
