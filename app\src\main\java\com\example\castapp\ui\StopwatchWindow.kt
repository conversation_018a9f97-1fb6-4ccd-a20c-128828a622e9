package com.example.castapp.ui

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.TextView
import com.example.castapp.R
import java.lang.ref.WeakReference
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.math.abs
import com.example.castapp.utils.AppLog

/**
 * 悬浮秒表窗口
 * 显示毫秒级精度的系统时间
 */
@SuppressLint("ClickableAccessibilityIssue")
class StopwatchWindow(
    private val context: Context,
    private val onCloseCallback: () -> Unit
) {

    /**
     * 静态内部Handler类，避免内存泄漏
     */
    private class TimeUpdateHandler(window: StopwatchWindow) : Handler(Looper.getMainLooper()) {
        private val windowRef = WeakReference(window)

        fun scheduleUpdate(delayMs: Long) {
            removeCallbacksAndMessages(null) // 清除之前的消息
            postDelayed({
                windowRef.get()?.updateTimeDisplay()
            }, delayMs)
        }

        fun stopUpdates() {
            removeCallbacksAndMessages(null)
        }
    }

    private val windowManager: WindowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private val layoutInflater: LayoutInflater = LayoutInflater.from(context)

    private var floatingView: View? = null
    private var timeTextView: TextView? = null
    private var closeButton: ImageButton? = null

    // 使用静态Handler避免内存泄漏
    private var updateHandler: TimeUpdateHandler? = null

    // 时间格式化器，精确到毫秒
    private val timeFormatter = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())

    // 普通模式时间更新相关
    private val targetFps = 60  // 60FPS，约16.67ms一次

    // 拖动相关变量
    private var initialX = 0
    private var initialY = 0
    private var initialTouchX = 0f
    private var initialTouchY = 0f
    private var isDragging = false

    /**
     * 显示悬浮窗
     */
    fun show() {
        try {
            if (floatingView != null) {
                AppLog.d("悬浮窗已显示")
                return
            }

            // 创建悬浮窗布局
            createFloatingView()

            // 初始化Handler
            updateHandler = TimeUpdateHandler(this)

            // 添加到WindowManager
            val layoutParams = createLayoutParams()
            windowManager.addView(floatingView, layoutParams)

            // 开始时间更新
            startTimeUpdate()

            AppLog.d("悬浮窗显示成功")
        } catch (e: Exception) {
            AppLog.e("显示悬浮窗失败", e)
        }
    }

    /**
     * 隐藏悬浮窗
     */
    fun hide() {
        try {
            stopTimeUpdate()

            floatingView?.let { view ->
                windowManager.removeView(view)
                floatingView = null
                timeTextView = null
                closeButton = null
            }

            // 清理Handler引用，防止内存泄漏
            updateHandler = null

            AppLog.d("悬浮窗隐藏成功")
        } catch (e: Exception) {
            AppLog.e("隐藏悬浮窗失败", e)
        }
    }

    /**
     * 创建悬浮窗视图
     */
    private fun createFloatingView() {
        // 创建临时父容器以正确解析布局参数
        val tempParent = FrameLayout(context)
        // 使用布局文件创建视图
        floatingView = layoutInflater.inflate(R.layout.floating_stopwatch_window, tempParent, false)

        // 获取子视图引用
        timeTextView = floatingView?.findViewById(R.id.tv_time)
        closeButton = floatingView?.findViewById(R.id.btn_close)

        // 设置初始时间和颜色
        timeTextView?.apply {
            text = getCurrentTimeString()
            setTextColor(0xFFFF0000.toInt()) // 红色文字
        }

        // 设置关闭按钮点击事件
        closeButton?.setOnClickListener {
            onCloseCallback()
        }

        // 设置拖动监听
        setupDragListener()
    }

    /**
     * 创建窗口布局参数
     */
    private fun createLayoutParams(): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }

        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            x = 100
            y = 100
        }
    }

    /**
     * 设置拖动监听器
     */
    @SuppressLint("ClickableAccessibilityIssue")
    private fun setupDragListener() {
        floatingView?.setOnTouchListener { view, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = (view.layoutParams as WindowManager.LayoutParams).x
                    initialY = (view.layoutParams as WindowManager.LayoutParams).y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    isDragging = false
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    val deltaX = event.rawX - initialTouchX
                    val deltaY = event.rawY - initialTouchY

                    // 判断是否开始拖动（移动距离超过阈值）
                    if (!isDragging && (abs(deltaX) > 10 || abs(deltaY) > 10)) {
                        isDragging = true
                    }

                    if (isDragging) {
                        val layoutParams = view.layoutParams as WindowManager.LayoutParams
                        layoutParams.x = initialX + deltaX.toInt()
                        layoutParams.y = initialY + deltaY.toInt()
                        windowManager.updateViewLayout(view, layoutParams)
                    }
                    true
                }
                MotionEvent.ACTION_UP -> {
                    if (!isDragging) {
                        // 如果没有拖动，则执行点击事件
                        view.performClick()
                    }
                    isDragging = false
                    true
                }
                else -> false
            }
        }
    }

    /**
     * 开始时间更新
     */
    private fun startTimeUpdate() {
        scheduleNextUpdate()
    }

    /**
     * 停止时间更新
     */
    private fun stopTimeUpdate() {
        updateHandler?.stopUpdates()
    }

    /**
     * 安排下次更新
     */
    private fun scheduleNextUpdate() {
        val frameTime = 1000L / targetFps // 每帧的毫秒数
        val nextUpdateDelay = frameTime - ((System.currentTimeMillis() % frameTime))
        updateHandler?.scheduleUpdate(maxOf(1L, nextUpdateDelay))
    }

    /**
     * 更新时间显示 - 由Handler调用
     */
    private fun updateTimeDisplay() {
        // 更新时间显示
        timeTextView?.text = getCurrentTimeString()

        // 安排下次更新
        scheduleNextUpdate()
    }

    /**
     * 获取当前时间字符串
     */
    private fun getCurrentTimeString(): String {
        // 使用System.currentTimeMillis()获取精确的毫秒时间
        val currentTimeMillis = System.currentTimeMillis()
        val date = Date(currentTimeMillis)

        // 格式化时间（小时:分钟:秒.毫秒）
        return timeFormatter.format(date)
    }
}