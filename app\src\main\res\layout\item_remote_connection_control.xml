<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="6dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp">

        <!-- 顶部：连接状态、地址、状态信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="6dp">

            <View
                android:id="@+id/connection_status_indicator"
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:background="@drawable/connection_status_indicator"
                android:layout_marginEnd="8dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/connection_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="127.0.0.1:8888"
                    android:textSize="12sp"
                    android:textColor="#333333"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="2dp">

                    <TextView
                        android:id="@+id/connection_id_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ID: 0.1_8888"
                        android:textSize="9sp"
                        android:textColor="#888888" />

                    <TextView
                        android:id="@+id/connection_status_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="未投屏"
                        android:textSize="9sp"
                        android:textColor="#888888"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- 操作按钮区域 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageButton
                    android:id="@+id/edit_button"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:padding="4dp"
                    android:background="@drawable/ic_edit"
                    android:alpha="0.7"
                    android:scaleType="centerInside"
                    android:layout_marginEnd="8dp"
                    android:contentDescription="编辑连接" />

                <ImageButton
                    android:id="@+id/delete_button"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:padding="4dp"
                    android:background="@drawable/ic_delete"
                    android:alpha="0.7"
                    android:scaleType="centerInside"
                    android:contentDescription="删除连接" />

            </LinearLayout>

        </LinearLayout>

        <!-- 控制开关区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 投屏开关 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginStart="12dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="投屏"
                    android:textSize="10sp"
                    android:textColor="#666666"/>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/cast_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:scaleX="0.8"
                    android:scaleY="0.8" />

            </LinearLayout>

            <!-- 媒体音频开关 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="媒体"
                    android:textSize="10sp"
                    android:textColor="#666666"/>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/media_audio_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:scaleX="0.8"
                    android:scaleY="0.8" />

            </LinearLayout>

            <!-- 麦克风音频开关 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginEnd="12dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="麦克风"
                    android:textSize="10sp"
                    android:textColor="#666666"/>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/mic_audio_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:scaleX="0.8"
                    android:scaleY="0.8" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
