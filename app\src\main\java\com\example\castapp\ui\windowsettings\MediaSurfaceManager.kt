package com.example.castapp.ui.windowsettings

import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.SurfaceTexture
import android.media.MediaPlayer
import android.net.Uri
import android.view.Surface
import android.view.TextureView
import android.widget.FrameLayout
import android.widget.ImageView
import com.example.castapp.utils.AppLog

/**
 * 媒体Surface管理器
 * 负责管理视频和图片的显示
 * 视频使用TextureView + MediaPlayer渲染，与投屏窗口保持一致的变换机制
 */
class MediaSurfaceManager(
    private val context: Context,
    private val container: FrameLayout
) {

    private var textureView: TextureView? = null
    private var imageView: ImageView? = null
    private var mediaPlayer: MediaPlayer? = null
    private var connectionId: String = ""
    private var contentType: String = ""

    // 视频播放控制相关
    private var isPlayEnabled: Boolean = true
    private var loopCount: Int = -1 // -1表示无限循环
    private var currentPlayCount: Int = 0
    private var volume: Float = 0.8f // 默认音量80%
    
    /**
     * 为视频设置TextureView（使用MediaPlayer + Surface渲染）
     */
    fun setupVideoView(mediaId: String, fileName: String, uri: Uri) {
        this.connectionId = mediaId
        this.contentType = "video"

        // 移除旧的视图
        clearViews()

        // 创建TextureView
        textureView = TextureView(context).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )

            // 设置SurfaceTextureListener
            surfaceTextureListener = createVideoSurfaceTextureListener(fileName, uri)
        }

        // 添加到容器
        container.addView(textureView)

        AppLog.d("为视频 $fileName (ID: $mediaId) 创建TextureView完成")
    }

    /**
     * 创建视频SurfaceTextureListener
     */
    private fun createVideoSurfaceTextureListener(fileName: String, uri: Uri): TextureView.SurfaceTextureListener {
        return object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                AppLog.d("视频TextureView Surface可用: $fileName, size=${width}x${height}")

                // 创建MediaPlayer并设置Surface
                try {
                    mediaPlayer = MediaPlayer().apply {
                        setDataSource(context, uri)
                        setSurface(Surface(surface))

                        // 根据循环次数设置循环模式
                        isLooping = (loopCount == -1)

                        // 设置音量
                        setVideoScalingMode(MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING)
                        setVolume(volume, volume)

                        setOnPreparedListener { player ->
                            if (isPlayEnabled) {
                                player.start()
                                AppLog.d("视频开始播放: $fileName (音量: ${(volume * 100).toInt()}%)")
                            } else {
                                AppLog.d("视频已准备但播放开关关闭: $fileName")
                            }
                        }

                        setOnErrorListener { _, what, extra ->
                            AppLog.e("视频播放错误: what=$what, extra=$extra")
                            true
                        }

                        setOnCompletionListener {
                            AppLog.d("视频播放完成: $fileName")
                            handleVideoCompletion()
                        }

                        prepareAsync()
                    }
                } catch (e: Exception) {
                    AppLog.e("创建MediaPlayer失败: $fileName", e)
                }
            }

            override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {
                AppLog.d("视频TextureView Surface尺寸改变: $fileName, size=${width}x${height}")
            }

            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                AppLog.d("视频TextureView Surface销毁: $fileName")

                // 释放MediaPlayer资源
                mediaPlayer?.let {
                    try {
                        if (it.isPlaying) {
                            it.stop()
                        }
                        it.release()
                        mediaPlayer = null
                        AppLog.d("MediaPlayer资源已释放: $fileName")
                    } catch (e: Exception) {
                        AppLog.e("释放MediaPlayer失败: $fileName", e)
                    }
                }

                return true
            }

            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
                // 帧更新，通常不需要处理
            }
        }
    }
    
    /**
     * 为图片设置ImageView
     */
    fun setupImageView(mediaId: String, fileName: String, uri: Uri) {
        this.connectionId = mediaId
        this.contentType = "image"
        
        // 移除旧的视图
        clearViews()
        
        // 创建ImageView
        imageView = ImageView(context).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )

            scaleType = ImageView.ScaleType.CENTER_CROP
            
            // 加载图片
            try {
                val inputStream = context.contentResolver.openInputStream(uri)
                val bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream?.close()
                
                if (bitmap != null) {
                    setImageBitmap(bitmap)
                    AppLog.d("图片加载成功: $fileName")
                } else {
                    AppLog.e("图片加载失败: $fileName")
                }
            } catch (e: Exception) {
                AppLog.e("加载图片失败: $fileName", e)
            }
        }
        
        // 添加到容器
        container.addView(imageView)
        
        AppLog.d("为图片 $fileName (ID: $mediaId) 创建ImageView完成")
    }
    
    /**
     * 获取当前的视图（用于变换操作）
     */
    fun getCurrentView(): android.view.View? {
        return when (contentType) {
            "video" -> textureView
            "image" -> imageView
            else -> null
        }
    }

    /**
     * 获取TextureView（视频媒体窗口现在使用TextureView）
     */
    fun getTextureView(): TextureView? {
        return if (contentType == "video") {
            textureView
        } else {
            null
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            // 停止视频播放
            mediaPlayer?.let {
                if (it.isPlaying) {
                    it.stop()
                }
                it.release()
                mediaPlayer = null
            }

            // 移除视图
            clearViews()

            // 🎬 如果是视频窗口，清理SharedPreferences中的播放设置
            if (connectionId.startsWith("video_") && connectionId.isNotEmpty()) {
                clearVideoSettingsFromPreferences()
            }

            AppLog.d("MediaSurfaceManager资源清理完成: $connectionId")

        } catch (e: Exception) {
            AppLog.e("MediaSurfaceManager资源清理失败", e)
        }
    }
    
    /**
     * 清除所有视图
     */
    private fun clearViews() {
        textureView?.let {
            container.removeView(it)
            textureView = null
        }

        imageView?.let {
            container.removeView(it)
            imageView = null
        }
    }

    // ==================== 视频播放控制方法 ====================

    /**
     * 设置播放开关
     */
    fun setPlayEnabled(enabled: Boolean) {
        isPlayEnabled = enabled
        mediaPlayer?.let { player ->
            try {
                if (enabled && !player.isPlaying) {
                    player.start()
                    AppLog.d("视频播放已启用: $connectionId")
                } else if (!enabled && player.isPlaying) {
                    player.pause()
                    AppLog.d("视频播放已暂停: $connectionId")
                }
            } catch (e: Exception) {
                AppLog.e("设置播放状态失败: $connectionId", e)
            }
        }
    }

    /**
     * 设置循环次数
     * @param count -1表示无限循环，0表示不循环，>0表示循环指定次数
     */
    fun setLoopCount(count: Int) {
        loopCount = count
        currentPlayCount = 0

        mediaPlayer?.let { player ->
            try {
                // 如果是无限循环，设置MediaPlayer的isLooping为true
                player.isLooping = (count == -1)
                AppLog.d("设置循环次数: $connectionId, count=$count")
            } catch (e: Exception) {
                AppLog.e("设置循环次数失败: $connectionId", e)
            }
        }
    }

    /**
     * 设置音量
     * @param volumePercent 音量百分比 (0-100)
     */
    fun setVolume(volumePercent: Int) {
        volume = volumePercent / 100f
        mediaPlayer?.let { player ->
            try {
                player.setVolume(volume, volume)
                AppLog.d("设置音量: $connectionId, volume=${volumePercent}%")
            } catch (e: Exception) {
                AppLog.e("设置音量失败: $connectionId", e)
            }
        }
    }

    /**
     * 处理视频播放完成
     */
    private fun handleVideoCompletion() {
        if (loopCount == -1) {
            // 无限循环，MediaPlayer会自动处理
            return
        } else if (loopCount == 0) {
            // 不循环，播放完成后停止
            AppLog.d("视频播放完成，不循环: $connectionId")
            return
        } else {
            // 有限次数循环
            currentPlayCount++
            if (currentPlayCount < loopCount) {
                // 还需要继续播放
                mediaPlayer?.let { player ->
                    try {
                        player.seekTo(0)
                        if (isPlayEnabled) {
                            player.start()
                        }
                        AppLog.d("视频重新播放: $connectionId (${currentPlayCount + 1}/$loopCount)")
                    } catch (e: Exception) {
                        AppLog.e("重新播放视频失败: $connectionId", e)
                    }
                }
            } else {
                // 播放次数已达到，停止播放
                AppLog.d("视频播放完成，已达到循环次数: $connectionId (${loopCount}次)")
            }
        }
    }

    /**
     * 🎬 清理SharedPreferences中的视频播放设置
     */
    private fun clearVideoSettingsFromPreferences() {
        try {
            val prefs = context.getSharedPreferences("video_settings", Context.MODE_PRIVATE)
            prefs.edit().apply {
                remove("${connectionId}_play_enabled")
                remove("${connectionId}_loop_count")
                remove("${connectionId}_volume")
                apply()
            }
            AppLog.d("【视频设置清理】已清理SharedPreferences中的视频播放设置: $connectionId")
        } catch (e: Exception) {
            AppLog.e("【视频设置清理】清理SharedPreferences失败: $connectionId", e)
        }
    }
}
