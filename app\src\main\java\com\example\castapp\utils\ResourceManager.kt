package com.example.castapp.utils

import android.media.AudioRecord
import android.media.MediaCodec
import android.media.projection.MediaProjection
import android.view.Surface
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import java.io.Closeable
import java.net.DatagramSocket
import java.net.Socket
import java.util.concurrent.ExecutorService
import java.util.concurrent.TimeUnit

/**
 * 资源管理工具类
 * 提供统一的try-with-resources模式支持，确保资源正确释放
 */
object ResourceManager {
    /**
     * 安全执行资源操作的扩展函数
     * 提供统一的异常处理和日志记录
     */
    inline fun <T> safeExecute(
        operation: String,
        noinline onError: ((Exception) -> Unit)? = null, // 添加 noinline
        block: () -> T
    ): T? {
        return try {
            block()
        } catch (e: Exception) {
            AppLog.w("执行操作失败: $operation", e)
            onError?.invoke(e)
            null
        }
    }

    /**
     * 安全释放MediaCodec资源 - 🚀 修复：移除延迟，避免音频中断
     */
    fun safeReleaseMediaCodec(codec: MediaCodec?, type: String = "MediaCodec") {
        codec?.let {
            AppLog.d("开始快速释放${type}...")

            // 🚀 修复：移除延迟，直接停止MediaCodec
            safeExecute("停止$type", onError = { e ->
                when (e) {
                    is IllegalStateException -> {
                        AppLog.w("${type}已处于无效状态，跳过停止操作: ${e.message}")
                    }
                    is RuntimeException -> {
                        AppLog.w("${type}停止时发生运行时异常: ${e.message}")
                    }
                    else -> {
                        AppLog.w("停止${type}时发生异常: ${e.message}")
                    }
                }
            }) {
                it.stop()
                AppLog.d("${type}已停止")
            }

            // 🚀 修复：移除延迟，直接释放MediaCodec
            safeExecute("释放$type", onError = { e ->
                when (e) {
                    is IllegalStateException -> {
                        AppLog.w("${type}已处于无效状态，跳过释放操作: ${e.message}")
                    }
                    is RuntimeException -> {
                        AppLog.w("${type}释放时发生运行时异常: ${e.message}")
                    }
                    else -> {
                        AppLog.w("释放${type}时发生异常: ${e.message}")
                    }
                }
            }) {
                it.release()
                AppLog.d("${type}资源已快速释放，避免音频中断")
            }
        }
    }

    /**
     * 安全释放AudioRecord资源
     */
    fun safeReleaseAudioRecord(audioRecord: AudioRecord?, type: String = "AudioRecord") {
        audioRecord?.let {
            safeExecute("停止${type}录音") {
                if (it.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                    it.stop()
                    AppLog.d("${type}已停止录音")
                }
            }

            safeExecute("释放$type") {
                it.release()
                AppLog.d("${type}资源已释放")
            }
        }
    }

    /**
     * 安全释放Surface资源
     */
    fun safeReleaseSurface(surface: Surface?, type: String = "Surface") {
        surface?.let {
            try {
                if (it.isValid) {
                    it.release()
                    AppLog.d("${type}已释放")
                } else {
                    AppLog.d("${type}已经无效")
                }
            } catch (e: Exception) {
                AppLog.w("释放${type}时发生异常", e)
            }
        }
    }

    /**
     * 安全关闭Socket资源
     */
    fun safeCloseSocket(socket: Socket?, type: String = "Socket") {
        socket?.let {
            safeExecute("关闭$type") {
                if (!it.isClosed) {
                    it.close()
                    AppLog.d("${type}已关闭")
                } else {
                    AppLog.d("${type}已经关闭")
                }
            }
        }
    }

    /**
     * 安全关闭DatagramSocket资源
     */
    fun safeCloseDatagramSocket(socket: DatagramSocket?, type: String = "DatagramSocket") {
        socket?.let {
            safeExecute("关闭$type") {
                if (!it.isClosed) {
                    it.close()
                    AppLog.d("${type}已关闭")
                } else {
                    AppLog.d("${type}已经关闭")
                }
            }
        }
    }

    /**
     * 安全停止MediaProjection资源
     */
    fun safeStopMediaProjection(projection: MediaProjection?, type: String = "MediaProjection") {
        projection?.let {
            try {
                it.stop()
                AppLog.d("${type}已停止")
            } catch (e: Exception) {
                AppLog.w("停止${type}时发生异常", e)
            }
        }
    }

    /**
     * 安全关闭Closeable资源
     */
    fun safeClose(closeable: Closeable?, type: String = "Closeable") {
        closeable?.let {
            try {
                it.close()
                AppLog.d("${type}已关闭")
            } catch (e: Exception) {
                AppLog.w("关闭${type}时发生异常", e)
            }
        }
    }

    /**
     * 安全停止线程
     */
    fun safeStopThread(thread: Thread?, type: String = "Thread", timeoutMs: Long = 1000) {
        thread?.let {
            try {
                it.interrupt()
                it.join(timeoutMs)
                AppLog.d("${type}已停止")
            } catch (e: Exception) {
                AppLog.w("停止${type}时发生异常", e)
            }
        }
    }

    /**
     * 安全取消协程作用域
     */
    fun safeCancelCoroutineScope(scope: CoroutineScope?, type: String = "CoroutineScope") {
        scope?.let {
            try {
                it.cancel()
                AppLog.d("${type}已取消")
            } catch (e: Exception) {
                AppLog.w("取消${type}时发生异常", e)
            }
        }
    }

    /**
     * 安全关闭ExecutorService
     */
    fun safeShutdownExecutor(executor: ExecutorService?, type: String = "ExecutorService", timeoutMs: Long = 2000) {
        executor?.let {
            try {
                it.shutdown()
                if (!it.awaitTermination(timeoutMs, TimeUnit.MILLISECONDS)) {
                    AppLog.w("${type}未在${timeoutMs}ms内关闭，强制关闭")
                    it.shutdownNow()
                    if (!it.awaitTermination(1000, TimeUnit.MILLISECONDS)) {
                        AppLog.e("${type}强制关闭失败")
                    }
                }
                AppLog.d("${type}已关闭")
            } catch (e: InterruptedException) {
                AppLog.w("等待${type}关闭时被中断", e)
                it.shutdownNow()
                Thread.currentThread().interrupt()
            } catch (e: Exception) {
                AppLog.w("关闭${type}时发生异常", e)
            }
        }
    }

    /**
     * 批量释放资源
     */
    fun safeReleaseAll(vararg resources: Pair<Any?, String>) {
        resources.forEach { (resource, type) ->
            when (resource) {
                is MediaCodec -> safeReleaseMediaCodec(resource, type)
                is AudioRecord -> safeReleaseAudioRecord(resource, type)
                is Surface -> safeReleaseSurface(resource, type)
                is Socket -> safeCloseSocket(resource, type)
                is DatagramSocket -> safeCloseDatagramSocket(resource, type)
                is MediaProjection -> safeStopMediaProjection(resource, type)
                is Closeable -> safeClose(resource, type)
                is Thread -> safeStopThread(resource, type)
                is CoroutineScope -> safeCancelCoroutineScope(resource, type)
                is ExecutorService -> safeShutdownExecutor(resource, type)
                null -> AppLog.d("${type}为null，跳过释放")
                else -> AppLog.w("不支持的资源类型: ${resource::class.java.simpleName}")
            }
        }
    }
}
