# 圆角半径缩放问题修复

## 问题描述
测试发现将接收端的文字窗口进行缩放后，遥控端的文字窗口的圆角半径效果会变得更明显。

## 问题根源分析

### 双重圆角缩放问题
当接收端文字窗口被用户缩放后，遥控端应用容器级缩放时，圆角半径被重复缩放：

1. **第一次缩放**：在数据层计算圆角半径
   ```kotlin
   val cornerRadiusPx = dpToPx(data.cornerRadius * data.scaleFactor)
   ```

2. **第二次缩放**：在视图层应用容器级缩放
   ```kotlin
   applyContainerScale(data.scaleFactor, data.remoteControlScale)
   ```

3. **结果**：圆角半径被缩放了两次，变得过于明显

### 具体场景
- **接收端**：用户将文字窗口缩放到1.5倍
- **数据层**：`cornerRadius * 1.5f` = 圆角半径被放大1.5倍
- **视图层**：整个容器再次被缩放1.5倍，圆角半径再次被放大
- **最终效果**：圆角半径被放大了 `1.5 × 1.5 = 2.25` 倍

## 修复方案

### 核心修复思路
**根据是否应用容器级缩放决定圆角半径的计算方式**：

1. **文字窗口已被用户缩放**（将应用容器级缩放）：
   - 圆角半径不预先乘以 `scaleFactor`
   - 让容器级缩放来处理圆角的缩放
   - 避免双重缩放

2. **其他情况**（不应用容器级缩放）：
   - 圆角半径正常乘以 `scaleFactor`
   - 保持原有的缩放逻辑

### 已实施的修复

#### 1. applyClipBounds() 方法
```kotlin
// 🎯 圆角缩放修复：根据是否应用容器级缩放决定圆角半径计算方式
val cornerRadiusPx = if (data.isTextWindow() && data.scaleFactor != 1.0f) {
    // 文字窗口已被用户缩放，将应用容器级缩放，圆角半径不预先缩放
    dpToPx(data.cornerRadius)
} else {
    // 其他情况：正常应用缩放因子到圆角半径
    dpToPx(data.cornerRadius * data.scaleFactor)
}
```

#### 2. clearClipBounds() 方法
```kotlin
val cornerRadiusPx = if (currentData.isTextWindow() && currentData.scaleFactor != 1.0f) {
    // 文字窗口已被用户缩放，将应用容器级缩放，圆角半径不预先缩放
    dpToPx(currentData.cornerRadius)
} else {
    // 其他情况：正常应用缩放因子到圆角半径
    dpToPx(currentData.cornerRadius * currentData.scaleFactor)
}
```

#### 3. applyFinalCropBounds() 方法
```kotlin
val cornerRadiusPx = if (data.isTextWindow() && data.scaleFactor != 1.0f) {
    // 文字窗口已被用户缩放，将应用容器级缩放，圆角半径不预先缩放
    dpToPx(data.cornerRadius)
} else {
    // 其他情况：正常应用缩放因子到圆角半径
    dpToPx(data.cornerRadius * data.scaleFactor)
}
```

## 修复后的圆角缩放流程

### 文字窗口（未被用户缩放）
1. **接收端**：`scaleFactor = 1.0f`，圆角半径正常
2. **遥控端**：不应用容器级缩放，圆角半径 = `cornerRadius * 1.0f`
3. **结果**：圆角半径保持原始大小

### 文字窗口（已被用户缩放）
1. **接收端**：`scaleFactor = 1.5f`，圆角半径被缩放
2. **遥控端**：应用容器级缩放，圆角半径 = `cornerRadius`（不预先缩放）
3. **容器级缩放**：整个容器包括圆角被缩放1.5倍
4. **结果**：圆角半径最终被缩放1.5倍（正确）

### 投屏窗口
1. **接收端**：`scaleFactor` 根据窗口大小计算
2. **遥控端**：应用容器级缩放，圆角半径 = `cornerRadius * scaleFactor`
3. **结果**：圆角半径保持正确的比例

## 测试验证

### 测试步骤
1. **创建文字窗口**：在接收端创建文字窗口，观察圆角效果
2. **建立远程连接**：遥控端连接到接收端，确认圆角效果一致
3. **测试用户缩放**：
   - 在接收端用手势将文字窗口缩放到1.5倍
   - 观察遥控端的圆角效果是否过于明显
   - 确认圆角半径与接收端保持一致
4. **测试缩放恢复**：将文字窗口缩放回1.0倍，确认圆角效果恢复正常

### 预期结果
- ✅ 未缩放的文字窗口：圆角效果正常
- ✅ 已缩放的文字窗口：圆角效果与接收端一致，不会过于明显
- ✅ 投屏窗口：圆角效果不受影响
- ✅ 缩放过程：圆角效果平滑过渡

## 技术细节

### 判断条件
```kotlin
data.isTextWindow() && data.scaleFactor != 1.0f
```
- `isTextWindow()`：确保只对文字窗口应用特殊处理
- `scaleFactor != 1.0f`：确保只对被用户缩放的窗口应用特殊处理

### 修复范围
- `applyClipBounds()`：主要的圆角设置方法
- `clearClipBounds()`：清除圆角时的处理
- `applyFinalCropBounds()`：最终裁剪时的圆角处理

### 日志增强
增加了详细的日志输出，帮助调试圆角缩放问题：
```kotlin
if (data.isTextWindow() && data.scaleFactor != 1.0f) {
    AppLog.d("圆角半径: ${data.cornerRadius}dp (文字窗口容器级缩放，不预先缩放圆角)")
} else {
    AppLog.d("圆角半径: ${data.cornerRadius}dp × ${data.scaleFactor} = ${data.cornerRadius * data.scaleFactor}dp")
}
```

这个修复确保了圆角效果在所有缩放情况下都保持正确的视觉比例。
