package com.example.castapp.audio

import com.example.castapp.network.UdpReceiver
import com.example.castapp.network.DataView
import com.example.castapp.network.SmartDataView
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import com.example.castapp.utils.AppLog

/**
 * 音频RTP接收器
 * 专门用于接收AAC音频数据
 */
class AudioRtpReceiver(
    private val port: Int,
    private val onAacDataReceived: (DataView, Long) -> Unit // 🚀 零拷贝：直接传递DataView和SSRC
) {
    companion object {        private const val RTP_VERSION = 2

        // 🚀 CPU优化：增加日志统计间隔，减少日志频率
        private const val LOG_INTERVAL_MS = 60000L // 60秒
    }

    // 🚀 零拷贝优化：使用DataView版本的UdpReceiver
    private val udpReceiver = UdpReceiver(port) { dataView ->
        processRtpPacketZeroCopy(dataView)
    }
    private val isRunning = AtomicBoolean(false)

    // 用于重组分片的缓存（虽然音频很少分片）
    private val fragmentCache = ConcurrentHashMap<String, MutableList<FragmentInfo>>()

    // 统计信息
    private var lastLogTime = 0L
    private var packetsReceived = 0
    private var bytesReceived = 0L

    data class FragmentInfo(
        val sequenceNumber: Int,
        val timestamp: Long,
        val data: ByteArray,
        val isLast: Boolean,
        val receivedTime: Long = System.currentTimeMillis()
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as FragmentInfo

            if (sequenceNumber != other.sequenceNumber) return false
            if (timestamp != other.timestamp) return false
            if (!data.contentEquals(other.data)) return false
            if (isLast != other.isLast) return false
            if (receivedTime != other.receivedTime) return false

            return true
        }

        override fun hashCode(): Int {
            var result = sequenceNumber
            result = 31 * result + timestamp.hashCode()
            result = 31 * result + data.contentHashCode()
            result = 31 * result + isLast.hashCode()
            result = 31 * result + receivedTime.hashCode()
            return result
        }
    }

    /**
     * 启动RTP接收器
     */
    fun start(): Boolean {
        if (isRunning.get()) {
            AppLog.w("音频RTP接收器已在运行")
            return true
        }

        return if (udpReceiver.start()) {
            isRunning.set(true)
            startReceiveThread()
            AppLog.audio("音频RTP接收器启动成功，端口: $port")
            true
        } else {
            AppLog.e("启动音频RTP接收器失败，端口: $port")
            false
        }
    }

    /**
     * 停止RTP接收器
     */
    fun stop() {
        isRunning.set(false)
        udpReceiver.stop()
        fragmentCache.clear()
        AppLog.audio("音频RTP接收器已停止")
    }

    /**
     * 启动接收线程
     */
    private fun startReceiveThread() {
        // UdpReceiver现在使用回调方式，不需要单独的接收线程
        // processRtpPacket将在UdpReceiver的回调中被调用
        AppLog.audio("音频RTP接收器已配置回调处理")
    }

    /**
     * 处理RTP包 - 零拷贝优化版本
     */
    private fun processRtpPacketZeroCopy(dataView: DataView) {
        if (dataView.size < 12) {
            AppLog.w("RTP包太小: ${dataView.size}")
            return
        }

        try {
            // 🎯 零拷贝优化：直接从DataView读取RTP头部信息
            val firstByte = dataView.getByte(0).toInt() and 0xFF
            val version = (firstByte shr 6) and 0x03
            val extension = (firstByte shr 4) and 0x01
            val csrcCount = firstByte and 0x0F

            if (version != RTP_VERSION) {
                AppLog.w("不支持的RTP版本: $version")
                return
            }

            val secondByte = dataView.getByte(1).toInt() and 0xFF
            val marker = (secondByte shr 7) and 0x01

            // 读取序列号（大端序）
            val sequenceNumber = ((dataView.getByte(2).toInt() and 0xFF) shl 8) or
                                (dataView.getByte(3).toInt() and 0xFF)

            // 读取时间戳（大端序）
            val timestamp = ((dataView.getByte(4).toLong() and 0xFF) shl 24) or
                           ((dataView.getByte(5).toLong() and 0xFF) shl 16) or
                           ((dataView.getByte(6).toLong() and 0xFF) shl 8) or
                           (dataView.getByte(7).toLong() and 0xFF)

            // 读取SSRC（大端序）
            val ssrc = ((dataView.getByte(8).toLong() and 0xFF) shl 24) or
                      ((dataView.getByte(9).toLong() and 0xFF) shl 16) or
                      ((dataView.getByte(10).toLong() and 0xFF) shl 8) or
                      (dataView.getByte(11).toLong() and 0xFF)

            // 计算载荷偏移量
            var payloadOffset = 12 + csrcCount * 4

            // 处理扩展头部
            if (extension == 1 && payloadOffset + 4 <= dataView.size) {
                val extLength = ((dataView.getByte(payloadOffset).toInt() and 0xFF) shl 8) or
                               (dataView.getByte(payloadOffset + 1).toInt() and 0xFF)
                payloadOffset += 4 + extLength * 4
            }

            // 获取载荷数据大小
            val payloadSize = dataView.size - payloadOffset
            if (payloadSize <= 0) {
                AppLog.w("RTP载荷为空")
                return
            }

            // 🚀 架构优化：配置数据现在通过WebSocket传输，RTP仅用于媒体流
            // 如果收到小包（可能是配置数据），直接忽略并记录警告
            if (payloadSize <= 4) {
                AppLog.w("⚠️ 收到疑似RTP配置数据包，已忽略（配置数据应通过WebSocket传输）: seq=$sequenceNumber, ssrc=$ssrc, size=$payloadSize")
                return
            }

            // 🚀 真正的零拷贝：直接从原始SmartDataView创建子视图，无数据拷贝
            val sourceSmartDataView = dataView as SmartDataView
            val payloadView = SmartDataView(
                smartBuffer = sourceSmartDataView.smartBuffer.retain(), // 直接复用原始缓冲区
                offset = sourceSmartDataView.offset + payloadOffset,
                size = payloadSize
            )

            processAacPayloadView(payloadView, sequenceNumber, timestamp, ssrc, marker == 1)



        } catch (e: Exception) {
            AppLog.e("处理RTP包失败", e)
        }
    }

    /**
     * 处理AAC载荷 - 零拷贝优化版本
     */
    private fun processAacPayloadView(payloadView: DataView, sequenceNumber: Int, timestamp: Long, ssrc: Long, marker: Boolean) {
        try {
            // 简化处理：直接将载荷作为AAC数据
            // 对于我们的实现，发送端直接发送AAC帧数据，不使用复杂的AU-header格式

            // 如果是完整的包（marker=true），直接处理
            if (marker) {
                // 🚀 真正的零拷贝：直接传递DataView，无任何数据拷贝
                onAacDataReceived(payloadView, ssrc)
                // 更新统计信息
                packetsReceived++
                bytesReceived += payloadView.size
                logStatisticsIfNeeded()
            } else {
                // 处理分片（虽然音频很少分片）
                // 当前实现中非marker包都视为中间分片
                val payload = payloadView.toByteArray() // 分片处理仍需要ByteArray
                handleFragment(payload, sequenceNumber, timestamp, ssrc)
            }

        } catch (e: Exception) {
            AppLog.e("处理AAC载荷失败", e)
        }
    }

    /**
     * 处理分片数据
     */
    private fun handleFragment(data: ByteArray, sequenceNumber: Int, timestamp: Long, ssrc: Long) {
        val key = "${ssrc}_${timestamp}"

        val fragments = fragmentCache.getOrPut(key) { mutableListOf() }

        fragments.add(FragmentInfo(sequenceNumber, timestamp, data, false))

        // 注意：当前实现中音频分片都视为中间分片，不进行自动重组
        // 如果需要支持真正的分片重组，需要根据RTP协议标志位来判断isLast

        // 清理过期的分片
        cleanupExpiredFragments()
    }

    /**
     * 定期输出统计信息
     */
    private fun logStatisticsIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastLogTime >= LOG_INTERVAL_MS) {
            if (packetsReceived > 0) {
                AppLog.audio("AAC接收统计 [端口=$port]: 数据包=${packetsReceived}个, " +
                        "数据量=${bytesReceived}字节")

                // 重置统计计数器
                packetsReceived = 0
                bytesReceived = 0L
            }
            lastLogTime = currentTime
        }
    }

    /**
     * 清理过期的分片
     */
    private fun cleanupExpiredFragments() {
        val currentTime = System.currentTimeMillis()
        val expireTime = 1000L // 1秒过期

        val iterator = fragmentCache.iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            val fragments = entry.value

            if (fragments.isNotEmpty() && currentTime - fragments[0].receivedTime > expireTime) {
                iterator.remove()
                AppLog.w("清理过期分片: ${entry.key}")
            }
        }
    }
}
