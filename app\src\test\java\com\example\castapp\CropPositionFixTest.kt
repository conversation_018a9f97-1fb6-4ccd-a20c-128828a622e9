package com.example.castapp

import org.junit.Test
import org.junit.Assert.*

/**
 * 🎯 裁剪窗口位置同步修复测试
 * 
 * 修复内容：
 * - 遥控端发送：容器位置坐标
 * - 接收端应用：直接应用到容器位置
 * 
 * 这个修复解决了坐标系统不匹配的问题
 */
class CropPositionFixTest {

    @Test
    fun testCropPositionSync() {
        // 🎯 测试裁剪窗口的位置同步修复
        
        // 模拟裁剪窗口数据
        val containerX = 100f
        val containerY = 200f
        val windowWidth = 400
        val windowHeight = 600
        val cropLeft = 0.1f  // 左10%
        val cropTop = 0.2f   // 顶20%
        
        val cropOffsetX = cropLeft * windowWidth // 40
        val cropOffsetY = cropTop * windowHeight // 120
        val visibleX = containerX + cropOffsetX // 140
        val visibleY = containerY + cropOffsetY // 320
        
        println("=== 裁剪窗口位置同步测试 ===")
        println("容器位置: ($containerX, $containerY)")
        println("可见区域位置: ($visibleX, $visibleY)")
        println("裁剪偏移: ($cropOffsetX, $cropOffsetY)")
        
        // 🎯 修复后：遥控端发送容器位置
        val sentX = containerX // 100
        val sentY = containerY // 200
        
        println("\n遥控端发送:")
        println("  发送坐标: ($sentX, $sentY)")
        println("  🎯 修复：发送容器位置")
        
        // 🎯 修复后：接收端直接应用到容器
        val receivedContainerX = sentX // 100
        val receivedContainerY = sentY // 200
        
        println("\n接收端处理:")
        println("  接收坐标: ($sentX, $sentY)")
        println("  🎯 修复：直接应用到容器位置")
        println("  应用结果: ($receivedContainerX, $receivedContainerY)")
        
        // 验证结果
        assertEquals("容器X坐标应该正确", containerX, receivedContainerX, 0.01f)
        assertEquals("容器Y坐标应该正确", containerY, receivedContainerY, 0.01f)
        
        // 验证可见区域位置
        val finalVisibleX = receivedContainerX + cropOffsetX // 100 + 40 = 140
        val finalVisibleY = receivedContainerY + cropOffsetY // 200 + 120 = 320
        
        println("\n最终验证:")
        println("  最终可见区域位置: ($finalVisibleX, $finalVisibleY)")
        println("  期望可见区域位置: ($visibleX, $visibleY)")
        
        assertEquals("可见区域X坐标应该正确", visibleX, finalVisibleX, 0.01f)
        assertEquals("可见区域Y坐标应该正确", visibleY, finalVisibleY, 0.01f)
        
        println("✅ 修复验证通过！")
    }
    
    @Test
    fun testDragScenario() {
        // 🎯 测试拖动场景
        
        val containerX = 200f
        val containerY = 300f
        val windowWidth = 600
        val windowHeight = 800
        val cropLeft = 0.2f
        val cropTop = 0.1f
        val remoteControlScale = 0.4
        
        val cropOffsetX = cropLeft * windowWidth // 120
        val cropOffsetY = cropTop * windowHeight // 80
        
        // 初始状态
        val initialVisibleX = containerX + cropOffsetX // 320
        val initialVisibleY = containerY + cropOffsetY // 380
        val initialVisualizedX = (initialVisibleX * remoteControlScale).toFloat() // 128
        val initialVisualizedY = (initialVisibleY * remoteControlScale).toFloat() // 152
        
        println("=== 拖动场景测试 ===")
        println("初始容器位置: ($containerX, $containerY)")
        println("初始可见区域位置: ($initialVisibleX, $initialVisibleY)")
        println("初始可视化位置: ($initialVisualizedX, $initialVisualizedY)")
        
        // 模拟拖动
        val dragDeltaX = 20f
        val dragDeltaY = 15f
        val newVisualizedX = initialVisualizedX + dragDeltaX // 148
        val newVisualizedY = initialVisualizedY + dragDeltaY // 167
        
        // 转换为接收端坐标
        val newActualVisibleX = (newVisualizedX / remoteControlScale).toFloat() // 370
        val newActualVisibleY = (newVisualizedY / remoteControlScale).toFloat() // 417.5
        
        // 🎯 修复：计算并发送容器位置
        val sentContainerX = newActualVisibleX - cropOffsetX // 370 - 120 = 250
        val sentContainerY = newActualVisibleY - cropOffsetY // 417.5 - 80 = 337.5
        
        println("\n拖动操作:")
        println("  拖动距离: ($dragDeltaX, $dragDeltaY)")
        println("  新可视化位置: ($newVisualizedX, $newVisualizedY)")
        println("  新可见区域位置: ($newActualVisibleX, $newActualVisibleY)")
        println("  🎯 发送容器位置: ($sentContainerX, $sentContainerY)")
        
        // 🎯 修复：接收端直接应用
        val receivedContainerX = sentContainerX // 250
        val receivedContainerY = sentContainerY // 337.5
        
        println("\n接收端处理:")
        println("  接收容器位置: ($receivedContainerX, $receivedContainerY)")
        println("  🎯 修复：直接应用到容器")
        
        // 验证最终结果
        val expectedContainerX = containerX + (dragDeltaX / remoteControlScale) // 200 + 50 = 250
        val expectedContainerY = containerY + (dragDeltaY / remoteControlScale) // 300 + 37.5 = 337.5
        
        assertEquals("拖动后容器X坐标应该正确", expectedContainerX.toDouble(), receivedContainerX.toDouble(), 0.01)
        assertEquals("拖动后容器Y坐标应该正确", expectedContainerY.toDouble(), receivedContainerY.toDouble(), 0.01)
        
        // 验证可见区域
        val finalVisibleX = receivedContainerX + cropOffsetX // 250 + 120 = 370
        val finalVisibleY = receivedContainerY + cropOffsetY // 337.5 + 80 = 417.5
        
        println("\n最终验证:")
        println("  期望容器位置: ($expectedContainerX, $expectedContainerY)")
        println("  实际容器位置: ($receivedContainerX, $receivedContainerY)")
        println("  最终可见区域位置: ($finalVisibleX, $finalVisibleY)")
        println("  期望可见区域位置: ($newActualVisibleX, $newActualVisibleY)")
        
        assertEquals("最终可见区域X坐标应该正确", newActualVisibleX.toDouble(), finalVisibleX.toDouble(), 0.01)
        assertEquals("最终可见区域Y坐标应该正确", newActualVisibleY.toDouble(), finalVisibleY.toDouble(), 0.01)
        
        println("✅ 拖动场景验证通过！")
    }
}
