package com.example.castapp.model

import android.graphics.Bitmap
import android.graphics.RectF
import androidx.core.graphics.toColorInt

/**
 * 🪟 投屏窗口容器可视化数据类
 * 用于在远程接收端控制窗口中绘制投屏窗口容器的可视化框架
 */
data class WindowVisualizationData(
    val connectionId: String,
    val deviceName: String? = null,
    
    // 原始窗口信息（来自CastWindowInfo）
    val originalX: Float,
    val originalY: Float,
    val originalWidth: Int,
    val originalHeight: Int,
    val rotationAngle: Float,
    val zOrder: Int,
    val scaleFactor: Float,
    val isVisible: Boolean,
    
    // 可视化计算后的数据（二次缩放）
    val visualizedX: Float,
    val visualizedY: Float,
    val visualizedWidth: Float,
    val visualizedHeight: Float,
    
    // 缩放相关参数
    val remoteControlScale: Double, // 远程控制窗口的缩放比例
    val containerScale: Double,     // 投屏窗口容器的二次缩放比例
    
    // 视觉属性
    val alpha: Float = 1.0f,
    val cornerRadius: Float = 16f,
    val isMirrored: Boolean = false,
    val isCropping: Boolean = false,
    val cropRectRatio: RectF? = null,

    // 边框属性
    val isBorderEnabled: Boolean = false,
    val borderColor: Int = "#6B6B6B".toColorInt(),
    val borderWidth: Float = 2f,

    // 🎯 新增：截图数据
    val screenshotBitmap: android.graphics.Bitmap? = null
) {
    
    /**
     * 获取简化的连接ID（显示规则）
     * - 摄像头窗口：显示完整ID
     * - 媒体窗口：显示文件名前8位
     * - 文本窗口：显示UUID后8位
     * - 投屏窗口：显示后8位
     */
    fun getShortConnectionId(): String {
        return when {
            connectionId == "front_camera" -> "front_camera"
            connectionId == "rear_camera" -> "rear_camera"
            connectionId.startsWith("video_") || connectionId.startsWith("image_") -> {
                // 媒体窗口：提取文件名部分并显示前8位
                val parts = connectionId.split("_", limit = 2)
                if (parts.size >= 2) {
                    val fileName = parts[1]
                    if (fileName.length > 8) fileName.take(8) else fileName
                } else {
                    connectionId.takeLast(8)
                }
            }
            connectionId.startsWith("text_") -> {
                // 文本窗口：显示UUID后8位
                connectionId.takeLast(8)
            }
            else -> connectionId.takeLast(8)
        }
    }
    
    /**
     * 获取设备显示信息
     */
    fun getDeviceDisplayInfo(): String {
        val displayDeviceName = if (!deviceName.isNullOrBlank()) deviceName else "未知设备"
        return "$displayDeviceName(${getShortConnectionId()})"
    }
    
    /**
     * 获取可视化窗口的边界矩形
     */
    fun getVisualizationBounds(): RectF {
        return RectF(
            visualizedX,
            visualizedY,
            visualizedX + visualizedWidth,
            visualizedY + visualizedHeight
        )
    }
    
    /**
     * 检查是否需要显示（可见且有有效尺寸）
     */
    fun shouldDisplay(): Boolean {
        return isVisible && visualizedWidth > 0 && visualizedHeight > 0
    }
    
    /**
     * 获取层级显示文本
     */
    fun getLayerText(): String = zOrder.toString()
    
    /**
     * 获取尺寸显示文本
     */
    fun getSizeText(): String = "${visualizedWidth.toInt()}×${visualizedHeight.toInt()}"
    

    
    companion object {
        
        /**
         * 从CastWindowInfo创建WindowVisualizationData
         * @param windowInfo 投屏窗口信息
         * @param remoteControlScale 远程控制窗口的缩放比例
         * @return 可视化数据对象
         */
        fun fromCastWindowInfo(
            windowInfo: CastWindowInfo,
            remoteControlScale: Double
        ): WindowVisualizationData {
            
            // 🎯 修复：可视化尺寸计算应该基于原始窗口尺寸，不应用裁剪
            // 裁剪效果应该完全由Canvas裁剪来实现，避免双重应用裁剪
            val currentWidth = windowInfo.baseWindowWidth.toFloat()
            val currentHeight = windowInfo.baseWindowHeight.toFloat()

            // 🎯 关键修复：只应用缩放倍数，不应用裁剪比例
            // 这样可视化容器保持原始窗口的缩放版本，裁剪只影响Canvas显示区域
            val actualWidth = (currentWidth * windowInfo.scaleFactor).toInt()
            val actualHeight = (currentHeight * windowInfo.scaleFactor).toInt()

            // 用于可视化计算的尺寸（只包含缩放，不包含裁剪）
            val scaledWidth = actualWidth.toFloat()
            val scaledHeight = actualHeight.toFloat()
            
            // 计算投屏窗口容器的二次缩放比例
            val containerScale = remoteControlScale
            
            // 计算可视化后的尺寸和位置
            val visualizedWidth = (scaledWidth * containerScale).toFloat()
            val visualizedHeight = (scaledHeight * containerScale).toFloat()

            // 🎯 关键分析：接收端发送的是容器位置，需要确定可视化窗口应该显示在哪里
            val containerX = windowInfo.positionX
            val containerY = windowInfo.positionY

            // 🎯 调试：分析位置计算策略
            com.example.castapp.utils.AppLog.d("【可视化位置计算调试】窗口: ${windowInfo.connectionId}")
            com.example.castapp.utils.AppLog.d("  接收端发送的容器位置: ($containerX, $containerY)")
            com.example.castapp.utils.AppLog.d("  是否裁剪: ${windowInfo.isCropping}")

            val actualDisplayX: Float
            val actualDisplayY: Float

            if (windowInfo.isCropping && windowInfo.cropRectRatio != null) {
                // 🎯 策略选择：对于裁剪窗口，可视化窗口应该显示在哪里？
                // 选项1：显示在容器位置（用户看不到的位置）
                // 选项2：显示在可见区域位置（用户实际看到的位置）

                val cropOffsetX = windowInfo.cropRectRatio.left * currentWidth
                val cropOffsetY = windowInfo.cropRectRatio.top * currentHeight

                // 🎯 当前策略：显示在容器位置（与接收端发送的位置一致）
                actualDisplayX = containerX
                actualDisplayY = containerY

                com.example.castapp.utils.AppLog.d("  裁剪偏移: ($cropOffsetX, $cropOffsetY)")
                com.example.castapp.utils.AppLog.d("  可见区域位置: (${containerX + cropOffsetX}, ${containerY + cropOffsetY})")
                com.example.castapp.utils.AppLog.d("  🎯 当前策略：可视化窗口显示在容器位置: ($actualDisplayX, $actualDisplayY)")
                com.example.castapp.utils.AppLog.d("  🎯 这意味着可视化窗口位置与用户看到的窗口位置不一致")
            } else {
                // 🎯 普通窗口：容器位置就是可见位置
                actualDisplayX = containerX
                actualDisplayY = containerY
                com.example.castapp.utils.AppLog.d("  🎯 普通窗口：直接使用容器位置: ($actualDisplayX, $actualDisplayY)")
            }

            // 按远程控制窗口的缩放比例缩放位置
            val visualizedX = (actualDisplayX * remoteControlScale).toFloat()
            val visualizedY = (actualDisplayY * remoteControlScale).toFloat()

            com.example.castapp.utils.AppLog.d("  远程控制缩放: $remoteControlScale")
            com.example.castapp.utils.AppLog.d("  最终可视化位置: ($visualizedX, $visualizedY)")
            
            // 🔍 添加详细的尺寸计算日志
            com.example.castapp.utils.AppLog.d("【尺寸计算】窗口: ${windowInfo.getDisplayTextWithDevice()}")
            com.example.castapp.utils.AppLog.d("  基础尺寸: ${windowInfo.baseWindowWidth}×${windowInfo.baseWindowHeight}")
            com.example.castapp.utils.AppLog.d("  是否裁剪: ${windowInfo.isCropping}")
            windowInfo.cropRectRatio?.let { cropRatio ->
                val cropWidth = cropRatio.right - cropRatio.left
                val cropHeight = cropRatio.bottom - cropRatio.top
                com.example.castapp.utils.AppLog.d("  裁剪比例: $cropWidth × $cropHeight")
                com.example.castapp.utils.AppLog.d("  🎯 注意: 裁剪效果将由Canvas裁剪实现，不影响容器尺寸")
            }
            com.example.castapp.utils.AppLog.d("  窗口缩放: ${windowInfo.scaleFactor}")
            com.example.castapp.utils.AppLog.d("  🎯 容器尺寸（基于原始窗口）: ${actualWidth}×${actualHeight}")
            com.example.castapp.utils.AppLog.d("  远程控制缩放: ${"%.6f".format(remoteControlScale)}")
            com.example.castapp.utils.AppLog.d("  可视化尺寸: ${visualizedWidth.toInt()}×${visualizedHeight.toInt()}")
            com.example.castapp.utils.AppLog.d("  缩放验证: $actualWidth × ${"%.6f".format(remoteControlScale)} = ${(actualWidth * remoteControlScale).toInt()}")
            com.example.castapp.utils.AppLog.d("  接收端容器位置: (${windowInfo.positionX}, ${windowInfo.positionY})")
            com.example.castapp.utils.AppLog.d("  🎯 直接使用容器位置: ($actualDisplayX, $actualDisplayY)")
            com.example.castapp.utils.AppLog.d("  可视化位置: (${visualizedX.toInt()}, ${visualizedY.toInt()})")
            com.example.castapp.utils.AppLog.d("  位置验证: $actualDisplayX × ${"%.6f".format(remoteControlScale)} = ${(actualDisplayX * remoteControlScale).toInt()}")

            // 🎯 添加边框参数调试日志
            com.example.castapp.utils.AppLog.d("【可视化数据转换】边框参数传递:")
            com.example.castapp.utils.AppLog.d("  源数据边框启用: ${windowInfo.isBorderEnabled}")
            com.example.castapp.utils.AppLog.d("  源数据边框颜色: ${String.format("#%08X", windowInfo.borderColor)}")
            com.example.castapp.utils.AppLog.d("  源数据边框宽度: ${windowInfo.borderWidth}dp")

            return WindowVisualizationData(
                connectionId = windowInfo.connectionId,
                deviceName = windowInfo.deviceName,
                originalX = actualDisplayX,  // 🎯 修复：接收端发送的容器位置，裁剪由Canvas处理
                originalY = actualDisplayY,  // 🎯 修复：接收端发送的容器位置，裁剪由Canvas处理
                originalWidth = actualWidth,
                originalHeight = actualHeight,
                rotationAngle = windowInfo.rotationAngle,
                zOrder = windowInfo.zOrder,
                scaleFactor = windowInfo.scaleFactor,
                isVisible = windowInfo.isVisible,
                visualizedX = visualizedX,
                visualizedY = visualizedY,
                visualizedWidth = visualizedWidth,
                visualizedHeight = visualizedHeight,
                remoteControlScale = remoteControlScale,
                containerScale = containerScale,
                alpha = windowInfo.alpha,
                cornerRadius = windowInfo.cornerRadius,
                isMirrored = windowInfo.isMirrored,
                isCropping = windowInfo.isCropping,
                cropRectRatio = windowInfo.cropRectRatio,
                // 🎯 添加边框参数传递
                isBorderEnabled = windowInfo.isBorderEnabled,
                borderColor = windowInfo.borderColor,
                borderWidth = windowInfo.borderWidth
            )
        }
    }
}
