package com.example.castapp.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.appcompat.widget.SwitchCompat
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.model.Connection
import com.example.castapp.utils.AppLog

/**
 * 远程连接列表控制适配器
 * 用于在远程控制端显示和控制发送端的连接列表
 */
class RemoteSenderAdapter(
    private val connections: MutableList<Connection>,
    private val onCastToggle: (Connection, Boolean) -> Unit,
    private val onMediaAudioToggle: (Connection, Boolean) -> Unit,
    private val onMicAudioToggle: (Connection, Boolean) -> Unit,
    private val onEditConnection: (Connection) -> Unit,
    private val onDeleteConnection: (Connection) -> Unit
) : RecyclerView.Adapter<RemoteSenderAdapter.ViewHolder>() {

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val connectionText: TextView = itemView.findViewById(R.id.connection_text)
        val connectionIdText: TextView = itemView.findViewById(R.id.connection_id_text)
        val connectionStatusText: TextView = itemView.findViewById(R.id.connection_status_text)
        val statusIndicator: View = itemView.findViewById(R.id.connection_status_indicator)
        val castSwitch: SwitchCompat = itemView.findViewById(R.id.cast_switch)
        val mediaAudioSwitch: SwitchCompat = itemView.findViewById(R.id.media_audio_switch)
        val micAudioSwitch: SwitchCompat = itemView.findViewById(R.id.mic_audio_switch)
        val editButton: ImageButton = itemView.findViewById(R.id.edit_button)
        val deleteButton: ImageButton = itemView.findViewById(R.id.delete_button)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_remote_connection_control, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val connection = connections[position]

        // 设置连接信息
        holder.connectionText.text = connection.getDisplayText()
        holder.connectionIdText.text = holder.itemView.context.getString(
            R.string.connection_id_format,
            connection.connectionId.takeLast(8)
        )

        // 设置连接状态
        holder.connectionStatusText.text = connection.getStatusSummary()

        // 设置状态指示器
        holder.statusIndicator.isSelected = connection.isCasting

        // 设置投屏开关
        holder.castSwitch.setOnCheckedChangeListener(null) // 清除之前的监听器
        holder.castSwitch.isChecked = connection.isCasting
        holder.castSwitch.setOnCheckedChangeListener { _, isChecked ->
            AppLog.d("远程控制投屏开关切换: ${connection.getDisplayText()}, 新状态: $isChecked")
            onCastToggle(connection, isChecked)
        }

        // 设置媒体音频开关
        holder.mediaAudioSwitch.setOnCheckedChangeListener(null)
        holder.mediaAudioSwitch.isChecked = connection.isMediaAudioEnabled
        holder.mediaAudioSwitch.setOnCheckedChangeListener { _, isChecked ->
            AppLog.d("远程控制媒体音频开关切换: ${connection.getDisplayText()}, 新状态: $isChecked")
            onMediaAudioToggle(connection, isChecked)
        }

        // 设置麦克风音频开关
        holder.micAudioSwitch.setOnCheckedChangeListener(null)
        holder.micAudioSwitch.isChecked = connection.isMicAudioEnabled
        holder.micAudioSwitch.setOnCheckedChangeListener { _, isChecked ->
            AppLog.d("远程控制麦克风音频开关切换: ${connection.getDisplayText()}, 新状态: $isChecked")
            onMicAudioToggle(connection, isChecked)
        }

        // 设置编辑按钮
        holder.editButton.setOnClickListener {
            AppLog.d("远程控制编辑连接: ${connection.getDisplayText()}")
            onEditConnection(connection)
        }

        // 设置删除按钮
        holder.deleteButton.setOnClickListener {
            AppLog.d("远程控制删除连接: ${connection.getDisplayText()}")
            onDeleteConnection(connection)
        }
    }

    override fun getItemCount(): Int = connections.size

    /**
     * 更新连接列表
     */
    fun updateConnections(newConnections: List<Connection>) {
        connections.clear()
        connections.addAll(newConnections)
        notifyDataSetChanged()
        AppLog.d("远程连接列表已更新: ${connections.size} 个连接")
    }

    /**
     * 更新单个连接
     */
    fun updateConnection(updatedConnection: Connection) {
        val index = connections.indexOfFirst { it.connectionId == updatedConnection.connectionId }
        if (index >= 0) {
            connections[index] = updatedConnection
            notifyItemChanged(index)
            AppLog.d("远程连接已更新: ${updatedConnection.getDisplayText()}")
        }
    }

    /**
     * 添加连接
     */
    fun addConnection(connection: Connection) {
        connections.add(connection)
        notifyItemInserted(connections.size - 1)
        AppLog.d("远程连接已添加: ${connection.getDisplayText()}")
    }

    /**
     * 移除连接
     */
    fun removeConnection(connectionId: String) {
        val index = connections.indexOfFirst { it.connectionId == connectionId }
        if (index >= 0) {
            val removedConnection = connections.removeAt(index)
            notifyItemRemoved(index)
            AppLog.d("远程连接已移除: ${removedConnection.getDisplayText()}")
        }
    }
}
