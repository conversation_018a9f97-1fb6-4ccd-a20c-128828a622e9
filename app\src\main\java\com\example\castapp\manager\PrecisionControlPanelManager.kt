package com.example.castapp.manager

import android.app.Activity
import android.widget.RelativeLayout
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.ui.view.PrecisionControlPanel
import com.example.castapp.utils.AppLog

/**
 * 精准控制面板管理器
 * 负责动态创建、管理和销毁多个调控面板
 * 遵循单一职责原则，专门处理面板相关逻辑
 */
class PrecisionControlPanelManager(
    private val activity: Activity,
    private val mainContainer: RelativeLayout
) {
    
    // 存储连接ID到调控面板的映射
    private val precisionControlPanels = mutableMapOf<String, PrecisionControlPanel>()
    
    // 面板事件回调接口
    interface PanelEventListener {
        fun onTransformChanged(connectionId: String, x: Float, y: Float, scale: Float, rotation: Float)
        fun onResetTransform(connectionId: String)
        fun onPanelClosed(connectionId: String)
    }
    
    private var panelEventListener: PanelEventListener? = null
    
    /**
     * 设置面板事件监听器
     */
    fun setPanelEventListener(listener: PanelEventListener) {
        panelEventListener = listener
    }
    
    /**
     * 更新精准控制面板
     * 根据窗口信息列表动态创建或移除面板
     */
    fun updatePanels(windowInfoList: List<CastWindowInfo>) {
        val enabledWindows = windowInfoList.filter { it.isControlEnabled }
        
        // 移除不再启用调控的面板
        removePanelsForDisabledWindows(enabledWindows)
        
        // 为新启用调控的窗口创建面板
        createPanelsForEnabledWindows(enabledWindows)
        
        AppLog.d("【精准控制面板管理器】当前活跃面板数: ${precisionControlPanels.size}")
    }
    
    /**
     * 移除不再启用调控的面板
     */
    private fun removePanelsForDisabledWindows(enabledWindows: List<CastWindowInfo>) {
        val toRemove = mutableListOf<String>()
        precisionControlPanels.forEach { (connectionId, panel) ->
            if (enabledWindows.none { it.connectionId == connectionId }) {
                toRemove.add(connectionId)
                mainContainer.removeView(panel)
                AppLog.d("【精准控制面板管理器】移除面板: $connectionId")
            }
        }
        toRemove.forEach { precisionControlPanels.remove(it) }
    }
    
    /**
     * 为新启用调控的窗口创建面板
     */
    private fun createPanelsForEnabledWindows(enabledWindows: List<CastWindowInfo>) {
        enabledWindows.forEach { windowInfo ->
            val connectionId = windowInfo.connectionId
            if (!precisionControlPanels.containsKey(connectionId)) {
                createPrecisionControlPanel(windowInfo)
            } else {
                // 更新现有面板的窗口信息
                precisionControlPanels[connectionId]?.updateBoundWindow(windowInfo)
            }
        }
    }
    
    /**
     * 创建精准控制面板
     */
    private fun createPrecisionControlPanel(windowInfo: CastWindowInfo) {
        val panel = PrecisionControlPanel(activity)
        
        // 设置面板监听器
        setupPanelListeners(panel)
        
        // 绑定窗口信息
        panel.bindWindow(windowInfo)
        
        // 设置面板布局参数
        val layoutParams = createPanelLayoutParams()
        panel.layoutParams = layoutParams
        
        // 添加到容器并保存引用
        mainContainer.addView(panel)
        precisionControlPanels[windowInfo.connectionId] = panel
        
        // 显示面板
        panel.showPanel()
        
        AppLog.d("【精准控制面板管理器】创建面板: ${windowInfo.connectionId}")
    }
    
    /**
     * 设置面板监听器
     */
    private fun setupPanelListeners(panel: PrecisionControlPanel) {
        panel.setOnTransformChangeListener(object : PrecisionControlPanel.OnTransformChangeListener {
            override fun onTransformChanged(connectionId: String, x: Float, y: Float, scale: Float, rotation: Float) {
                panelEventListener?.onTransformChanged(connectionId, x, y, scale, rotation)
                AppLog.d("【精准控制面板管理器】变换事件: $connectionId, X=$x, Y=$y, Scale=$scale, Rotation=$rotation")
            }

            override fun onResetTransform(connectionId: String) {
                panelEventListener?.onResetTransform(connectionId)
                AppLog.d("【精准控制面板管理器】重置事件: $connectionId")
            }

            override fun onPanelClosed(connectionId: String) {
                panelEventListener?.onPanelClosed(connectionId)
                AppLog.d("【精准控制面板管理器】关闭事件: $connectionId")
            }
        })
    }
    
    /**
     * 创建面板布局参数
     * 计算面板的初始位置，避免重叠
     */
    private fun createPanelLayoutParams(): RelativeLayout.LayoutParams {
        val layoutParams = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT
        )
        
        // 计算面板的初始位置（避免重叠）
        val panelCount = precisionControlPanels.size
        val offsetX = (panelCount % 3) * 20 // 水平偏移
        val offsetY = (panelCount / 3) * 60 // 垂直偏移
        
        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
        layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL)
        layoutParams.setMargins(offsetX, 0, 0, 8 + offsetY)
        
        return layoutParams
    }
    
    /**
     * 更新指定面板的变换数值显示
     */
    fun updateTransformValues(connectionId: String, x: Float, y: Float, scale: Float, rotation: Float) {
        precisionControlPanels[connectionId]?.updateTransformValues(connectionId, x, y, scale, rotation)
    }
    
    /**
     * 获取当前活跃面板数量
     */
    fun getActivePanelCount(): Int {
        return precisionControlPanels.size
    }
    
    /**
     * 清理所有面板
     */
    fun clearAllPanels() {
        precisionControlPanels.values.forEach { panel ->
            mainContainer.removeView(panel)
        }
        precisionControlPanels.clear()
        AppLog.d("【精准控制面板管理器】清理所有面板")
    }
}
