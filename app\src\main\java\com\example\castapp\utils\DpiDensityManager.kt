package com.example.castapp.utils

import android.content.Context
import android.util.DisplayMetrics
import android.view.WindowManager

/**
 * DPI密度管理器
 * 用于处理不同设备间的DPI密度差异，确保文字显示一致性
 * 
 * 🎯 解决问题：不同设备DPI密度差异导致的文字换行位置不一致
 */
class DpiDensityManager(private val context: Context) {
    
    companion object {
        private const val TAG = "DpiDensityManager"
        
        // 标准DPI密度值（Android标准）
        const val STANDARD_DPI = 160f // mdpi标准密度
        
        // DPI密度类别
        const val LDPI = 120f    // 0.75x
        const val MDPI = 160f    // 1.0x (标准)
        const val HDPI = 240f    // 1.5x
        const val XHDPI = 320f   // 2.0x
        const val XXHDPI = 480f  // 3.0x
        const val XXXHDPI = 640f // 4.0x
    }
    
    /**
     * 设备DPI密度信息
     */
    data class DpiInfo(
        val densityDpi: Int,        // DPI密度值
        val density: Float,         // 密度比例
        val scaledDensity: Float,   // 缩放密度比例
        val xdpi: Float,           // X轴DPI
        val ydpi: Float,           // Y轴DPI
        val widthPixels: Int,      // 屏幕宽度像素
        val heightPixels: Int      // 屏幕高度像素
    ) {
        /**
         * 获取DPI密度类别名称
         */
        fun getDensityCategory(): String {
            return when {
                densityDpi <= 120 -> "LDPI (${densityDpi})"
                densityDpi <= 160 -> "MDPI (${densityDpi})"
                densityDpi <= 240 -> "HDPI (${densityDpi})"
                densityDpi <= 320 -> "XHDPI (${densityDpi})"
                densityDpi <= 480 -> "XXHDPI (${densityDpi})"
                densityDpi <= 640 -> "XXXHDPI (${densityDpi})"
                else -> "ULTRA (${densityDpi})"
            }
        }
        
        /**
         * 转换为Map格式（用于网络传输）
         */
        fun toMap(): Map<String, Any> {
            return mapOf(
                "densityDpi" to densityDpi,
                "density" to density,
                "scaledDensity" to scaledDensity,
                "xdpi" to xdpi,
                "ydpi" to ydpi,
                "widthPixels" to widthPixels,
                "heightPixels" to heightPixels
            )
        }
        
        companion object {
            /**
             * 从Map格式创建DpiInfo
             */
            fun fromMap(map: Map<String, Any>): DpiInfo? {
                return try {
                    DpiInfo(
                        densityDpi = (map["densityDpi"] as? Number)?.toInt() ?: return null,
                        density = (map["density"] as? Number)?.toFloat() ?: return null,
                        scaledDensity = (map["scaledDensity"] as? Number)?.toFloat() ?: return null,
                        xdpi = (map["xdpi"] as? Number)?.toFloat() ?: return null,
                        ydpi = (map["ydpi"] as? Number)?.toFloat() ?: return null,
                        widthPixels = (map["widthPixels"] as? Number)?.toInt() ?: return null,
                        heightPixels = (map["heightPixels"] as? Number)?.toInt() ?: return null
                    )
                } catch (e: Exception) {
                    AppLog.e("【DPI密度管理器】从Map创建DpiInfo失败", e)
                    null
                }
            }
        }
    }
    
    /**
     * 获取当前设备的DPI密度信息
     */
    fun getCurrentDeviceDpiInfo(): DpiInfo {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        
        val dpiInfo = DpiInfo(
            densityDpi = displayMetrics.densityDpi,
            density = displayMetrics.density,
            scaledDensity = displayMetrics.scaledDensity,
            xdpi = displayMetrics.xdpi,
            ydpi = displayMetrics.ydpi,
            widthPixels = displayMetrics.widthPixels,
            heightPixels = displayMetrics.heightPixels
        )
        
        AppLog.d("【DPI密度管理器】当前设备DPI信息: ${dpiInfo.getDensityCategory()}")
        AppLog.d("【DPI密度管理器】  密度比例: ${dpiInfo.density}")
        AppLog.d("【DPI密度管理器】  屏幕尺寸: ${dpiInfo.widthPixels}x${dpiInfo.heightPixels}")
        
        return dpiInfo
    }
    
    /**
     * 计算两个设备间的DPI密度比值
     * @param sourceDpiInfo 源设备DPI信息（通常是接收端）
     * @param targetDpiInfo 目标设备DPI信息（通常是遥控端）
     * @return DPI密度比值（目标设备相对于源设备的比值）
     */
    fun calculateDpiRatio(sourceDpiInfo: DpiInfo, targetDpiInfo: DpiInfo): Float {
        val ratio = sourceDpiInfo.density / targetDpiInfo.density
        
        AppLog.d("【DPI密度管理器】DPI密度比值计算:")
        AppLog.d("【DPI密度管理器】  源设备密度: ${sourceDpiInfo.density} (${sourceDpiInfo.getDensityCategory()})")
        AppLog.d("【DPI密度管理器】  目标设备密度: ${targetDpiInfo.density} (${targetDpiInfo.getDensityCategory()})")
        AppLog.d("【DPI密度管理器】  密度比值: $ratio")
        
        return ratio
    }
    
    /**
     * 根据DPI密度比值调整字体大小
     * @param originalFontSizeSp 原始字体大小（sp）
     * @param dpiRatio DPI密度比值
     * @return 调整后的字体大小（sp）
     */
    fun adjustFontSizeForDpi(originalFontSizeSp: Int, dpiRatio: Float): Int {
        val adjustedSize = (originalFontSizeSp * dpiRatio).toInt().coerceAtLeast(1)
        
        AppLog.d("【DPI密度管理器】字体大小DPI调整:")
        AppLog.d("【DPI密度管理器】  原始字号: ${originalFontSizeSp}sp")
        AppLog.d("【DPI密度管理器】  DPI比值: $dpiRatio")
        AppLog.d("【DPI密度管理器】  调整后字号: ${adjustedSize}sp")
        
        return adjustedSize
    }
    
    /**
     * 将sp值转换为像素值
     */
    fun spToPx(sp: Float): Float {
        return sp * context.resources.displayMetrics.scaledDensity
    }
    
    /**
     * 将像素值转换为sp值
     */
    fun pxToSp(px: Float): Float {
        return px / context.resources.displayMetrics.scaledDensity
    }
    
    /**
     * 检查两个设备的DPI密度是否相近
     * @param threshold 阈值（默认0.1，即10%的差异）
     */
    fun isDpiSimilar(dpiInfo1: DpiInfo, dpiInfo2: DpiInfo, threshold: Float = 0.1f): Boolean {
        val ratio = calculateDpiRatio(dpiInfo1, dpiInfo2)
        val difference = kotlin.math.abs(ratio - 1.0f)
        val isSimilar = difference <= threshold
        
        AppLog.d("【DPI密度管理器】DPI密度相似性检查:")
        AppLog.d("【DPI密度管理器】  密度差异: ${(difference * 100).toInt()}%")
        AppLog.d("【DPI密度管理器】  是否相近: $isSimilar (阈值: ${(threshold * 100).toInt()}%)")
        
        return isSimilar
    }
}
