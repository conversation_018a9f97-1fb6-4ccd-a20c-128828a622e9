package com.example.castapp.manager

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.SurfaceTexture
import android.hardware.camera2.*
import android.hardware.camera2.params.OutputConfiguration
import android.hardware.camera2.params.SessionConfiguration
import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import android.util.Size
import android.view.Surface
import java.util.concurrent.Executor
import androidx.core.app.ActivityCompat
import com.example.castapp.utils.AppLog
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Semaphore
import java.util.concurrent.TimeUnit

/**
 * 多摄像头管理器
 * 支持同时管理前置和后置摄像头预览
 */
class MultiCameraManager private constructor() {

    // Camera2相关
    private var cameraManager: CameraManager? = null
    private var backgroundThread: HandlerThread? = null
    private var backgroundHandler: Handler? = null
    private var backgroundExecutor: Executor? = null

    // 多摄像头实例管理
    private val cameraInstances = ConcurrentHashMap<String, CameraInstance>()
    private val cameraOpenCloseLock = Semaphore(1)

    companion object {
        @Volatile
        private var INSTANCE: MultiCameraManager? = null

        fun getInstance(): MultiCameraManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MultiCameraManager().also {
                    INSTANCE = it
                    it.startBackgroundThread()
                }
            }
        }
    }

    /**
     * 初始化摄像头管理器
     */
    private fun initializeCameraManager(context: Context) {
        if (cameraManager == null) {
            cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
        }
    }
    
    /**
     * 摄像头实例数据类
     */
    private data class CameraInstance(
        var cameraDevice: CameraDevice? = null,
        var captureSession: CameraCaptureSession? = null,
        var previewSize: Size? = null,
        var isActive: Boolean = false,
        val cameraId: String,
        val isFrontCamera: Boolean
    )
    
    /**
     * 启动摄像头预览
     */
    fun startCameraPreview(context: Context, windowId: String, surfaceTexture: SurfaceTexture, isFrontCamera: Boolean, callback: (Boolean) -> Unit) {
        try {
            // 初始化摄像头管理器
            initializeCameraManager(context)

            // 检查是否已有该窗口的摄像头实例
            val existingInstance = cameraInstances[windowId]
            if (existingInstance?.isActive == true) {
                AppLog.d("摄像头预览已在运行: $windowId")
                callback(true)
                return
            }

            // 检查摄像头权限
            if (!checkCameraPermission(context)) {
                AppLog.e("缺少摄像头权限")
                callback(false)
                return
            }
            
            // 获取摄像头ID
            val cameraId = getCameraId(isFrontCamera)
            if (cameraId == null) {
                AppLog.e("未找到${if (isFrontCamera) "前置" else "后置"}摄像头")
                callback(false)
                return
            }
            
            // 检查是否已有其他窗口在使用同一个摄像头
            val conflictingInstance = cameraInstances.values.find { 
                it.cameraId == cameraId && it.isActive 
            }
            
            if (conflictingInstance != null) {
                AppLog.w("${if (isFrontCamera) "前置" else "后置"}摄像头已被其他窗口使用")
                callback(false)
                return
            }
            
            // 创建或更新摄像头实例
            val cameraInstance = CameraInstance(
                cameraId = cameraId,
                isFrontCamera = isFrontCamera
            )
            cameraInstances[windowId] = cameraInstance
            
            // 获取摄像头特性
            val characteristics = cameraManager?.getCameraCharacteristics(cameraId)
            val map = characteristics?.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
            
            // 选择预览尺寸
            cameraInstance.previewSize = chooseOptimalSize(map?.getOutputSizes(SurfaceTexture::class.java))
            
            // 设置SurfaceTexture尺寸
            cameraInstance.previewSize?.let { size ->
                surfaceTexture.setDefaultBufferSize(size.width, size.height)
            }
            
            // 打开摄像头
            openCamera(context, windowId, cameraId, surfaceTexture, callback)
            
        } catch (e: Exception) {
            AppLog.e("启动摄像头预览失败: $windowId", e)
            callback(false)
        }
    }
    
    /**
     * 停止摄像头预览
     */
    fun stopCameraPreview(windowId: String) {
        try {
            val cameraInstance = cameraInstances[windowId]
            if (cameraInstance?.isActive != true) {
                AppLog.d("摄像头预览未运行: $windowId")
                return
            }
            
            closeCamera(windowId)
            cameraInstances.remove(windowId)
            
            AppLog.d("摄像头预览已停止: $windowId")
            
        } catch (e: Exception) {
            AppLog.e("停止摄像头预览失败: $windowId", e)
        }
    }
    
    /**
     * 检查摄像头权限
     */
    private fun checkCameraPermission(context: Context): Boolean {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 获取摄像头ID
     */
    private fun getCameraId(isFrontCamera: Boolean): String? {
        try {
            val cameraIdList = cameraManager?.cameraIdList ?: return null
            
            for (cameraId in cameraIdList) {
                val characteristics = cameraManager?.getCameraCharacteristics(cameraId)
                val facing = characteristics?.get(CameraCharacteristics.LENS_FACING)
                
                val targetFacing = if (isFrontCamera) {
                    CameraCharacteristics.LENS_FACING_FRONT
                } else {
                    CameraCharacteristics.LENS_FACING_BACK
                }
                
                if (facing == targetFacing) {
                    return cameraId
                }
            }
            
        } catch (e: Exception) {
            AppLog.e("获取摄像头ID失败", e)
        }
        
        return null
    }
    
    /**
     * 选择最佳预览尺寸
     */
    private fun chooseOptimalSize(choices: Array<Size>?): Size? {
        if (choices == null || choices.isEmpty()) {
            return Size(640, 480) // 默认尺寸
        }
        
        // 选择接近16:9比例且不超过1080p的尺寸
        val targetRatio = 16.0 / 9.0
        val maxWidth = 1920
        val maxHeight = 1080
        
        var bestSize: Size? = null
        var minDiff = Double.MAX_VALUE
        
        for (size in choices) {
            if (size.width <= maxWidth && size.height <= maxHeight) {
                val ratio = size.width.toDouble() / size.height.toDouble()
                val diff = kotlin.math.abs(ratio - targetRatio)

                if (diff < minDiff) {
                    minDiff = diff
                    bestSize = size
                }
            }
        }
        
        return bestSize ?: choices[0]
    }
    
    /**
     * 打开摄像头
     */
    private fun openCamera(context: Context, windowId: String, cameraId: String, surfaceTexture: SurfaceTexture, callback: (Boolean) -> Unit) {
        try {
            if (!cameraOpenCloseLock.tryAcquire(2500, TimeUnit.MILLISECONDS)) {
                AppLog.e("摄像头打开超时: $windowId")
                callback(false)
                return
            }
            
            if (!checkCameraPermission(context)) {
                AppLog.e("缺少摄像头权限")
                callback(false)
                return
            }
            
            cameraManager?.openCamera(cameraId, object : CameraDevice.StateCallback() {
                override fun onOpened(camera: CameraDevice) {
                    cameraOpenCloseLock.release()
                    
                    val cameraInstance = cameraInstances[windowId]
                    if (cameraInstance != null) {
                        cameraInstance.cameraDevice = camera
                        createCameraPreviewSession(windowId, surfaceTexture, callback)
                        AppLog.d("摄像头打开成功: $windowId, cameraId: $cameraId")
                    } else {
                        camera.close()
                        callback(false)
                    }
                }
                
                override fun onDisconnected(camera: CameraDevice) {
                    cameraOpenCloseLock.release()
                    camera.close()
                    
                    val cameraInstance = cameraInstances[windowId]
                    if (cameraInstance != null) {
                        cameraInstance.cameraDevice = null
                        cameraInstance.isActive = false
                    }
                    
                    AppLog.d("摄像头断开连接: $windowId")
                    callback(false)
                }
                
                override fun onError(camera: CameraDevice, error: Int) {
                    cameraOpenCloseLock.release()
                    camera.close()
                    
                    val cameraInstance = cameraInstances[windowId]
                    if (cameraInstance != null) {
                        cameraInstance.cameraDevice = null
                        cameraInstance.isActive = false
                    }
                    
                    AppLog.e("摄像头打开错误: $windowId, 错误码: $error")
                    callback(false)
                }
            }, backgroundHandler)
            
        } catch (e: Exception) {
            AppLog.e("打开摄像头失败: $windowId", e)
            callback(false)
        }
    }
    
    /**
     * 创建摄像头预览会话
     */
    private fun createCameraPreviewSession(windowId: String, surfaceTexture: SurfaceTexture, callback: (Boolean) -> Unit) {
        try {
            val cameraInstance = cameraInstances[windowId] ?: return
            val cameraDevice = cameraInstance.cameraDevice ?: return
            
            val surface = Surface(surfaceTexture)
            val previewRequestBuilder = cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW)
            previewRequestBuilder.addTarget(surface)
            
            // 使用新的API替代已弃用的方法
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                val sessionConfig = SessionConfiguration(
                    SessionConfiguration.SESSION_REGULAR,
                    listOf(OutputConfiguration(surface)),
                    backgroundExecutor!!,
                    object : CameraCaptureSession.StateCallback() {
                        override fun onConfigured(session: CameraCaptureSession) {
                            val instance = cameraInstances[windowId]
                            if (instance?.cameraDevice == null) {
                                callback(false)
                                return
                            }

                            instance.captureSession = session

                            try {
                                // 设置自动对焦
                                previewRequestBuilder.set(
                                    CaptureRequest.CONTROL_AF_MODE,
                                    CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE
                                )

                                // 设置自动曝光
                                previewRequestBuilder.set(
                                    CaptureRequest.CONTROL_AE_MODE,
                                    CaptureRequest.CONTROL_AE_MODE_ON
                                )

                                val previewRequest = previewRequestBuilder.build()
                                session.setRepeatingRequest(previewRequest, null, backgroundHandler)

                                instance.isActive = true
                                callback(true)
                                AppLog.d("摄像头预览会话创建成功: $windowId")

                            } catch (e: Exception) {
                                AppLog.e("启动摄像头预览失败: $windowId", e)
                                callback(false)
                            }
                        }

                        override fun onConfigureFailed(session: CameraCaptureSession) {
                            AppLog.e("摄像头预览会话配置失败: $windowId")
                            callback(false)
                        }
                    }
                )
                cameraDevice.createCaptureSession(sessionConfig)
            } else {
                // 兼容旧版本API
                @Suppress("DEPRECATION")
                cameraDevice.createCaptureSession(
                    listOf(surface),
                    object : CameraCaptureSession.StateCallback() {
                        override fun onConfigured(session: CameraCaptureSession) {
                            val instance = cameraInstances[windowId]
                            if (instance?.cameraDevice == null) {
                                callback(false)
                                return
                            }

                            instance.captureSession = session

                            try {
                                // 设置自动对焦
                                previewRequestBuilder.set(
                                    CaptureRequest.CONTROL_AF_MODE,
                                    CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE
                                )

                                // 设置自动曝光
                                previewRequestBuilder.set(
                                    CaptureRequest.CONTROL_AE_MODE,
                                    CaptureRequest.CONTROL_AE_MODE_ON
                                )

                                val previewRequest = previewRequestBuilder.build()
                                session.setRepeatingRequest(previewRequest, null, backgroundHandler)

                                instance.isActive = true
                                callback(true)
                                AppLog.d("摄像头预览会话创建成功: $windowId")

                            } catch (e: Exception) {
                                AppLog.e("启动摄像头预览失败: $windowId", e)
                                callback(false)
                            }
                        }

                        override fun onConfigureFailed(session: CameraCaptureSession) {
                            AppLog.e("摄像头预览会话配置失败: $windowId")
                            callback(false)
                        }
                    },
                    backgroundHandler
                )
            }
            
        } catch (e: Exception) {
            AppLog.e("创建摄像头预览会话失败: $windowId", e)
            callback(false)
        }
    }
    
    /**
     * 关闭摄像头
     */
    private fun closeCamera(windowId: String) {
        try {
            cameraOpenCloseLock.acquire()
            
            val cameraInstance = cameraInstances[windowId]
            if (cameraInstance != null) {
                cameraInstance.captureSession?.close()
                cameraInstance.captureSession = null
                
                cameraInstance.cameraDevice?.close()
                cameraInstance.cameraDevice = null
                
                cameraInstance.isActive = false
            }
            
        } catch (e: Exception) {
            AppLog.e("关闭摄像头失败: $windowId", e)
        } finally {
            cameraOpenCloseLock.release()
        }
    }
    
    /**
     * 启动后台线程
     */
    private fun startBackgroundThread() {
        backgroundThread = HandlerThread("MultiCameraBackground").also { it.start() }
        backgroundHandler = Handler(backgroundThread?.looper!!)
        backgroundExecutor = Executor { command -> backgroundHandler?.post(command) }
    }
    
    /**
     * 停止后台线程
     */
    private fun stopBackgroundThread() {
        backgroundThread?.quitSafely()
        try {
            backgroundThread?.join()
            backgroundThread = null
            backgroundHandler = null
            backgroundExecutor = null
        } catch (e: InterruptedException) {
            AppLog.e("停止多摄像头后台线程失败", e)
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        // 停止所有摄像头预览
        cameraInstances.keys.toList().forEach { windowId ->
            stopCameraPreview(windowId)
        }
        
        stopBackgroundThread()
        AppLog.d("MultiCameraManager清理完成")
    }
}
